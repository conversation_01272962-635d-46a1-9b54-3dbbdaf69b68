<script setup lang="ts">
  import { ref, watch } from 'vue';
  import { DownOutlined, RightOutlined } from '@ant-design/icons-vue';
  interface IProps {
    expend: boolean;
    expandClick: () => void;
    bordered?: boolean;
  }
  withDefaults(defineProps<IProps>(), {
    expend: false,
    expandClick: () => {},
    bordered: true,
  });
  const box_height = ref(0);
  const box_ref = ref();

  const resizeObserver = new ResizeObserver(() => {
    if (box_ref.value) box_height.value = box_ref.value?.getBoundingClientRect()['height'];
  });

  watch(
    () => box_ref.value,
    (val: number) => {
      if (val) {
        resizeObserver.observe(box_ref.value);
      }
    },
    { immediate: true, deep: true },
  );
</script>

<template>
  <div>
    <div class="title flex mb-15px" :style="bordered ? {} : { border: 'none' }" @click="expandClick()">
      <div class="mr-10px">
        <DownOutlined v-if="expend" />
        <RightOutlined v-else />
      </div>
      <slot name="title"></slot>
    </div>
    <transition name="fade">
      <div
        v-show="expend"
        class="content mb-20px ml-15px"
        :style="{
          height: expend ? `${box_height}px` : '0px',
        }"
      >
        <div ref="box_ref">
          <slot name="content"></slot>
        </div>
      </div>
    </transition>
  </div>
</template>

<style scoped lang="less">
  .title {
    cursor: pointer;
    border-top: 1px solid #ddd;
    padding-top: 10px;
  }
  .content {
    height: 0;
    overflow: hidden;
    transition: all 0.15s linear;
  }
</style>
