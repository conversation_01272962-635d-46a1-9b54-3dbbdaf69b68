<script setup lang="ts">
  import { ref, reactive, onMounted, onUnmounted, nextTick, watch } from 'vue';
  import { CustomForm } from '@/components';
  import type { IFormItem, IPagination } from '@/interface';
  import { TABLE_PAGINATION } from '@/json/common';
  import type { ColumnType, TablePaginationConfig } from 'ant-design-vue/es/table/interface';
  import { useRoute, useRouter } from 'vue-router';
  import { deploymentOperation, fetchDeployList, singleDeployList } from '@/api/deploy';
  import { message } from 'ant-design-vue';
  import { CopyOutlined } from '@ant-design/icons-vue';
  import { copyText, convertIsoTimeToLocalTime } from '@/utils/common';
  import dayjs from 'dayjs';
  import { DeployStatus, deployMethod, DEPLOY_STATE_MAP } from './index';

  const route = useRoute();
  const router = useRouter();
  const props = withDefaults(defineProps<{ singleModel?: boolean; modelId?: string; category?: string }>(), {
    singleModel: false, // 单个模型的列表
    modelId: '',
  });
  const DEFAULT_SEARCHSTATE = {
    service_name: undefined,
  };
  const searchState = reactive({ ...DEFAULT_SEARCHSTATE });
  const apiVisible = ref(false);
  const iconMap = [
    { dataIndex: 'log', href: '#icon-rizhi' },
    { dataIndex: 'monitor', href: '#icon-jiankong' },
  ];
  const formConfig: IFormItem[] = [
    {
      field: 'service_name',
      type: 'input',
      label: '服务名称',
      placeholder: '请输入',
    },
  ];
  const columns: ColumnType[] = props?.singleModel
    ? [
        { title: '服务名称', dataIndex: 'service_name', fixed: 'left' },
        { title: '简介', dataIndex: 'introduction', ellipsis: true },
        { title: '部署方式', dataIndex: 'deploy_method' },
        // { title: '部署资源', dataIndex: 'resource' },
        { title: '状态', dataIndex: 'status' },
        { title: '调用接口', dataIndex: 'api' },
        { title: '创建时间', dataIndex: 'created_at' },
        { title: '更新时间', dataIndex: 'updated_at' },
        { title: '操作', dataIndex: 'operation', width: 240, fixed: 'right' },
      ]
    : [
        { title: '服务名称', dataIndex: 'service_name', fixed: 'left' },
        { title: '简介', dataIndex: 'introduction', ellipsis: true },
        { title: '部署模型名', dataIndex: 'model_name' },
        { title: '部署方式', dataIndex: 'deploy_method' },
        // { title: '部署资源', dataIndex: 'resource' },
        { title: '状态', dataIndex: 'status' },
        { title: '日志', dataIndex: 'log', align: 'center', width: 100 },
        { title: '监控', dataIndex: 'monitor', align: 'center', width: 100 },
        { title: '调用接口', dataIndex: 'api' },
        { title: '创建时间', dataIndex: 'created_at' },
        { title: '更新时间', dataIndex: 'updated_at' },
        { title: '操作', dataIndex: 'operation', width: 240, fixed: 'right' },
      ];
  const pagination = reactive({ ...TABLE_PAGINATION });
  const page: IPagination = reactive({ page_index: 1, page_size: 10 });
  const api = ref('');
  const loading = ref(false);
  const dataSource: Record<string, string>[] = reactive([]);
  const timer = ref();
  const tableHeight = ref(0);
  const statusState = reactive<{ list: boolean }>({ list: false });

  const getTaskListReq = async () => {
    loading.value = true;
    let data: Record<string, string>;
    const { modelId, singleModel } = props;
    if (singleModel && modelId) {
      data = await singleDeployList({
        id: modelId,
        ...{ page: page.page_index, limit: page.page_size },
        ...searchState,
      });
    } else {
      data = await fetchDeployList({
        ...{ page: page.page_index, limit: page.page_size },
        ...searchState,
      });
    }
    const { total, items: list } = data;
    dataSource.length = 0;
    // @ts-expect-error
    dataSource.push(...list);
    Object.assign(pagination, { current: page.page_index, total: total });
    // if (!data.finished) {
    //   if (timer.value) {
    //     clearInterval(timer.value);
    //     timer.value = null;
    //   }
    if (!timer.value) {
      startTaskListRR();
    }
    // }
    // Object.assign(statusState, { list: data.finished });
    loading.value = false;
  };
  const getTableHeight = () => {
    const tableItem = document.querySelector('.container');
    if (props.singleModel) tableHeight.value = (tableItem?.clientHeight as number) - 130;
    else tableHeight.value = tableItem?.clientHeight as number;
  };

  const startTaskListRR = () => {
    timer.value = setInterval(async () => {
      if (statusState.list) {
        clearInterval(timer.value);
        timer.value = null;
        return;
      }
      let data: Record<string, string>;
      const { modelId, singleModel } = props;
      if (singleModel && modelId) {
        data = await singleDeployList({
          id: modelId,
          ...{ page: page.page_index, limit: page.page_size },
          ...searchState,
        });
      } else {
        data = await fetchDeployList({
          ...{ page: page.page_index, limit: page.page_size },
          ...searchState,
        });
      }
      const { total, items: list } = data;
      dataSource.length = 0;
      // @ts-expect-error
      dataSource.push(...list);
      Object.assign(pagination, { current: page.page_index, total: total });
      Object.assign(statusState, { list: data.finished });
    }, 10000);
  };
  const checkApi = (record: { id: string }) => {
    api.value = `${location.origin}/data/ai-platform-backend/api/v1/deploy/api/call/${record.id}`;
    apiVisible.value = true;
  };
  const jumpDetail = (record: { id: string; status: string }, key: string) => {
    const { id, status } = record;
    if (status == DEPLOY_STATE_MAP.FAILED) return;
    if (status == DEPLOY_STATE_MAP.EXITED && key == 'monitor') return;
    router.push({
      path: '/deploy/detail',
      query: { ...route.query, id, tab: key },
    });
  };
  const jumpModelDetail = (record: { model_id: string }) => {
    if (props.singleModel) return;
    const { model_id } = record;
    router.push({
      path: '/model/detail',
      query: { ...route.query, modelid: model_id },
    });
  };
  const serverInfo = (record: { id: string; status: string }) => {
    const { id, status } = record;

    if (status == DEPLOY_STATE_MAP.FAILED) return;
    router.push({
      path: '/deploy/detail',
      query: { ...route.query, id },
    });
  };
  const reStart = async (id: string) => {
    await deploymentOperation({ opration: 'restart', oid: id });
    message.success('启动成功');
    getTaskListReq();
  };

  const comfirmStop = async (id: string) => {
    await deploymentOperation({ opration: 'stop', oid: id });
    message.success('停止成功');
    getTaskListReq();
  };

  const jumpExploration = (record: { id: string; service_name: string; exp_temp: string; model_category: string }) => {
    const { id, service_name, model_category } = record;
    let path: string = '/text/detail';
    // 根据模型分类，决定体验跳转的页面
    if (props.category) {
      switch (props.category) {
        case 'gotocr':
        case 'ocr':
        case 'paddleocr':
          path = '/ocr/detail';
          break;
        default:
          break;
      }
    } else {
      switch (model_category) {
        case 'ocr':
          path = '/ocr/detail';
          break;
        default:
          break;
      }
    }
    router.push({
      path,
      query: { serviceId: id, service_name, model_category },
    });
  };
  const comfirmDelete = async (id: string) => {
    await deploymentOperation({ opration: 'rm', oid: id });
    message.success('删除成功');
    getTaskListReq();
  };

  const toggleTable = (_pagination: TablePaginationConfig) => {
    const { current, pageSize } = _pagination;
    console.log(current, pageSize);
    Object.assign(pagination, { current, pageSize });
    Object.assign(page, { page_index: current, page_size: pageSize });
    getTaskListReq();
  };
  const onFinish = (values: Record<string, string>) => {
    const searchConfig: { [key: string]: string } = {};
    for (const key in values) {
      searchConfig[key] = values[key];
    }
    Object.assign(searchState, { ...searchConfig });
    page.page_index = 1;
    getTaskListReq();
  };
  const onRest = () => {
    Object.assign(pagination, { ...TABLE_PAGINATION });
    Object.assign(page, { page_index: 1, page_size: 10 });
    Object.assign(searchState, { ...DEFAULT_SEARCHSTATE });
    getTaskListReq();
  };

  watch(
    () => props,
    async () => {
      getTaskListReq();
    },
  );

  onMounted(async () => {
    getTaskListReq();
    await nextTick();
    getTableHeight();
  });

  onUnmounted(() => {
    clearInterval(timer.value);
    timer.value = null;
  });
</script>

<template>
  <template v-if="route.name === 'server_detail'">
    <router-view></router-view>
  </template>
  <template v-else>
    <CustomForm v-if="!singleModel" :form-items="formConfig" @on-finish="onFinish" @on-rest="onRest" />
    <a-table
      :data-source="dataSource"
      :columns="columns"
      :pagination="pagination"
      :loading="loading"
      :scroll="{ y: tableHeight - 200, x: 'max-content' }"
      @change="toggleTable"
    >
      <template #bodyCell="{ column, record, text }">
        <div v-if="column.dataIndex === 'operation'" class="operation-box">
          <a
            v-bind="[DEPLOY_STATE_MAP.FAILED].includes(record.status) ? { disabled: true } : {}"
            @click="serverInfo(record)"
            >查看</a
          >
          <a-popconfirm
            v-if="record.status === DEPLOY_STATE_MAP.STOPPED"
            title="确定重启这条任务吗?"
            @confirm="reStart(record.id)"
          >
            <a>重启</a>
          </a-popconfirm>
          <a v-if="record.status === DEPLOY_STATE_MAP.RUNNING" @click="jumpExploration(record)">体验</a>
          <a-popconfirm
            v-if="record.status === DEPLOY_STATE_MAP.RUNNING"
            title="确定停止这条任务吗?"
            @confirm="comfirmStop(record.id)"
          >
            <a>停止</a>
          </a-popconfirm>
          <a-popconfirm
            v-if="
              [
                DEPLOY_STATE_MAP.STOPPED,
                DEPLOY_STATE_MAP.FAILED,
                DEPLOY_STATE_MAP.CREATE_FAILED,
                DEPLOY_STATE_MAP.EXITED,
                DEPLOY_STATE_MAP.ABNORMAL,
              ].includes(record.status) ||
              (DEPLOY_STATE_MAP.CREATING == record.status &&
                dayjs(record.created_at).add(20, 's').isBefore(dayjs(new Date())))
            "
            title="确定删除这条任务吗?"
            @confirm="comfirmDelete(record.id)"
          >
            <a class="del-btn">删除</a>
          </a-popconfirm>
        </div>
        <div v-else-if="column.dataIndex === 'resource'">公共资源</div>
        <div v-else-if="['created_at', 'updated_at'].includes(column.dataIndex)">
          {{ convertIsoTimeToLocalTime(text) }}
        </div>
        <div v-else-if="column.dataIndex === 'service_name'">
          <div>{{ record.service_name }}</div>
          <div class="text-#ccc">{{ record.id }}</div>
        </div>
        <div v-else-if="column.dataIndex === 'api'">
          <a
            v-if="[DEPLOY_STATE_MAP.RUNNING, DEPLOY_STATE_MAP.STOPPED].includes(record.status)"
            class="table-a-btn"
            @click="checkApi(record)"
            >调用信息</a
          >
          <div v-else>--</div>
        </div>
        <div v-else-if="['log', 'monitor'].includes(column.dataIndex)">
          <svg class="w-20px cursor-pointer" aria-hidden="true" @click="jumpDetail(record, column.dataIndex)">
            <!-- iconfont图表 新增菜单记得更新 iconfont文件 @/assets/font/iconfont -->
            <use :xlink:href="`${iconMap.find((item) => item.dataIndex === column.dataIndex)?.href}`"></use>
          </svg>
        </div>
        <div v-else-if="column.dataIndex === 'deploy_method'">
          {{ deployMethod.find((item) => item.value === record.deploy_method)?.label }}
        </div>
        <div v-else-if="column.dataIndex === 'model_name'">
          <a v-if="!record.by_trained_model" class="table-a-btn" @click="jumpModelDetail(record)">{{
            record.model_name
          }}</a>
          <div v-else class="table-a-btn">{{ record.model_name }}</div>
        </div>
        <div v-else-if="column.dataIndex === 'status'">
          <a-tag :color="DeployStatus.find((item) => item.value === text)?.color">{{
            DeployStatus.find((item) => item.value === text)?.label
          }}</a-tag>
        </div>
        <div v-else>{{ text || '--' }}</div>
      </template>
    </a-table>
    <a-modal v-model:open="apiVisible" title="调用接口" width="40%" centered @ok="apiVisible = false">
      <template #footer>
        <a-button @click="apiVisible = false">取消</a-button>
      </template>
      <a-input v-model:value="api">
        <template #addonBefore> POST </template>
        <template #addonAfter>
          <CopyOutlined @click="copyText(api)" />
        </template>
      </a-input>
    </a-modal>
  </template>
</template>
