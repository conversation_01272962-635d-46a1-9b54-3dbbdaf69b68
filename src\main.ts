// import './assets/main.css'

import { createApp } from 'vue';
import Antd from 'ant-design-vue';
import { createPinia } from 'pinia';

import 'virtual:uno.css';
import 'ant-design-vue/dist/reset.css';
import '@/assets/font/iconfont.js';

import App from './App.vue';
import router from './router';
import { ellipsisTooltip } from '@/directive/tool-tip/ellipsis-tool-tip';
import JsonEditorVue from 'json-editor-vue3';

const app = createApp(App);

app.use(createPinia());
app.use(router);
app.use(Antd);
app.use(JsonEditorVue);
app.directive('ellipse-tooltip', ellipsisTooltip);
app.mount('#app');
