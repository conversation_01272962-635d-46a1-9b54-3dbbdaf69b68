<script setup lang="ts">
  import { modelCodeListV2, addModelV2, editModelV2, checkModelName, getModelManageInfoV2 } from '@/api/model';
  import type { IModelFormState, ITag, IModelItemResponse } from '@/interface/model';
  import { ref, onMounted, reactive, watch } from 'vue';
  import { Cascader, TreeSelect, message } from 'ant-design-vue';
  import { QuestionCircleFilled } from '@ant-design/icons-vue';
  import type { Dayjs } from 'dayjs';
  import dayjs from 'dayjs';
  import { templates, modelCategory, machineLearningList } from './index';
  import type { Rule } from 'ant-design-vue/es/form';
  import { dockerDropDown } from '@/api/docker';
  interface IProps {
    // model: Partial<IModelFormState>;
    id: string;
    visible: boolean;
    default: Partial<IModelFormState>;
    type: 'add' | 'edit';
    tags: ITag[];
  }
  const props = withDefaults(defineProps<IProps>(), {});
  const data = reactive<Partial<IModelFormState>>(JSON.parse(JSON.stringify(props.default)));
  const dockerList = reactive<{ id: string; name: string }[]>([]);
  const modelRef = ref();
  const vllmRef = ref();
  const sglangRef = ref();
  const spinning = ref(false);
  const modelCodeSpinning = ref(false);
  const disabledDate = (current: Dayjs) => {
    // 禁用当前日期之后的日期
    return current && current > dayjs().endOf('day');
  };
  interface TreeNode {
    label: string;
    value: string;
    children?: TreeNode[];
  }

  const treeData: TreeNode[] = [
    {
      label: 'LLama-Factory',
      value: `llama-factory`,
      children: [
        {
          label: 'SFT',
          value: `llama-factory/sft`,
          children: [
            {
              label: 'LoRA',
              value: `llama-factory/sft/lora`,
            },
            {
              label: '全参数',
              value: `llama-factory/sft/full`,
            },
            {
              label: '部分参数',
              value: `llama-factory/sft/freeze`,
            },
          ],
        },
      ],
    },
    {
      label: 'Unsloth',
      value: `unsloth`,
      children: [
        {
          label: 'SFT',
          value: `unsloth/sft`,
          children: [
            {
              label: 'LoRA',
              value: `unsloth/sft/lora`,
            },
          ],
        },
      ],
    },
  ];
  const vllm = reactive({
    max_model_len: 4096,
    gpu_memory_utilization: 0.1,
  });
  const sglang = reactive({
    tool_func_tmp: '',
    mem_fraction_static: 0.1,
  });
  const modelCodes = reactive<{ name: string; path: string }[]>([]);
  const getModalCode = async () => {
    modelCodeSpinning.value = true;
    const list: { name: string; path: string }[] = await modelCodeListV2();
    modelCodes.length = 0;
    modelCodes.push(...list);
  };

  const handleChange = (e: string) => {
    const item = modelCodes.find((item) => item.name === e);
    data.source_path = item?.path;
    data.name = item?.name;
    if (machineLearningList.includes(e)) {
      data.category = e;
    }
  };

  const validatorName = async (_rule: Rule, value: string) => {
    // 编辑不需要校验重名
    if (props.type !== 'add') {
      return Promise.resolve();
    }
    if (value == '' || value.trim().length == 0) {
      return Promise.reject('请输入服务名称');
    }
    // const regex = /^[a-z0-9][a-z0-9\-.]*[a-z0-9]$/;
    // if (!regex.test(value)) {
    //   return Promise.reject('格式不正确：仅支持小写字母、数字、-和.，且首尾必须为字母或数字');
    // }
    // if (value.length > 63) {
    //   return Promise.reject('服务名称最多输入 63 个字');
    // }
    try {
      await checkModelName(value.trim());
    } catch (e) {
      if (e === 'AlreadyExists') {
        return Promise.reject('该名称已存在，请重新命名');
      }
      return Promise.reject(e);
    }
    return Promise.resolve();
  };

  const submit = async () => {
    await modelRef.value.validateFields();
    if (vllmRef.value) {
      await vllmRef.value.validateFields();
    }
    if (sglangRef.value) {
      await sglangRef.value.validateFields();
    }
    const params: Partial<IModelFormState> | IModelItemResponse = {
      ...data,
      name: data.name?.trim(),
    };
    params.published_at = (data.published_at as Dayjs).format('YYYY-MM-DDTHH:mm:ss');
    params.tags = data.tags
      ? data.tags.map((e: object | Array<Record<string, string>>) => (Array.isArray(e) ? e[e.length - 1] : e))
      : [];
    const result: Record<string, object> = {};
    params.engine?.forEach((key) => {
      result[key] = {};
      if (params.engine?.includes('vllm')) {
        result.vllm = vllm;
      }
      if (params.engine?.includes('sglang')) {
        result.sglang = sglang;
      }
    });
    params.inference_engine = result;
    if (data.tf) {
      // @ts-expect-error
      const customTf = data.tf.reduce((acc, path) => {
        const [org, type, method] = path.split('/');

        acc[org] = acc[org] || {};
        acc[org][type] = acc[org][type] || [];

        if (!acc[org][type].includes(method)) {
          acc[org][type].push(method);
        }

        return acc;
      }, {});
      params.tf = customTf;
    }
    delete params.engine;
    delete params.tool_template;
    props.type === 'add'
      ? await addModelV2(params as IModelFormState)
      : await editModelV2(params as IModelItemResponse);
    message.success(`${props.type === 'add' ? '添加' : '编辑'}成功`);
  };

  const handleChangeCategory = (value: string) => {
    vllm.max_model_len = value === 'multimodal' ? 8192 : 4096;
    data.ops = [];
    data.template = '';
  };
  const resetFormFields = async () => {
    await modelRef.value.resetFields();
    await modelRef.value.clearValidate();
    if (vllmRef.value) {
      await vllmRef.value.resetFields();
      await vllmRef.value.clearValidate();
    }
  };
  const getDockerList = async () => {
    const list = await dockerDropDown();
    dockerList.length = 0;
    dockerList.push(...list);
  };
  const getInfo = async () => {
    spinning.value = true;
    const { id } = props;
    if (id) {
      const modelDetail = await getModelManageInfoV2(id);
      // 回显模型标签
      const tags: Record<string, string>[] = props.tags.reduce((r: Record<string, string>[], n) => {
        const { id, children } = n;
        if (children)
          children.map((e) =>
            // @ts-expect-error
            r.push([id, ...((modelDetail.tags.map((e) => e.id) || []).includes(e.id) ? [e.id] : [])]),
          );
        return r;
      }, []);
      const sft = modelDetail.tf
        ? Object.entries(modelDetail.tf).flatMap(([org, types]) =>
            // @ts-expect-error
            Object.entries(types).flatMap(([type, methods]) =>
              // @ts-expect-error
              methods.map((method: string) => `${org}/${type}/${method}`),
            ),
          )
        : null;
      const keys = Object.keys(modelDetail.inference_engine || {});
      let toolTemplate = '';
      if (keys.includes('vllm')) {
        Object.assign(vllm, modelDetail.inference_engine!.vllm);
      }
      if (keys.includes('sglang')) {
        Object.assign(sglang, modelDetail.inference_engine!.sglang);
      }
      const res: Partial<IModelFormState> = {
        ...modelDetail,
        // @ts-expect-error
        tf: sft.length ? sft : null,
        engine: keys,
        tool_template: toolTemplate,
        docker_image_ids: modelDetail.docker_images.map((item: { id: string }) => item.id),
        // @ts-expect-error
        tags: tags.filter((e) => e.length - 1),
        published_at: modelDetail!.published_at ? dayjs(String(modelDetail!.published_at)!.split('T')[0]) : '',
      };
      Object.assign(data, res);
      spinning.value = false;
    }
  };

  onMounted(() => {
    getModalCode();
    getDockerList();
  });
  watch(
    [() => props.visible, () => props.type],
    ([visible, type]) => {
      if (visible) {
        if (type === 'edit') {
          getInfo();
        } else {
          Object.assign(data, props.default);
        }
      }
    },
    { deep: true, immediate: true },
  );

  defineExpose({
    submit,
    resetFormFields,
  });
</script>

<template>
  <a-form
    ref="modelRef"
    :model="data"
    class="defcourse"
    label-align="right"
    layout="vertical"
    :label-col="{ style: { width: '200px' } }"
  >
    <div class="textbefo">模型信息</div>
    <div class="form-item-box">
      <a-form-item label="模型" name="source_name" :rules="[{ required: true, message: '请选择' }]">
        <a-select
          v-model:value="data.source_name"
          style="width: 100%"
          placeholder="请选择模型"
          :disabled="type !== 'add'"
          :not-found-content="modelCodeSpinning ? undefined : null"
          @change="handleChange"
        >
          <template v-if="modelCodeSpinning" #notFoundContent>
            <a-spin size="small" />
          </template>
          <a-select-option v-for="(n, i) in modelCodes" :key="i" :value="n.name">{{ n.name }}</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item
        label="模型名称"
        name="name"
        :rules="[{ required: true, validator: validatorName, trigger: 'change' }]"
      >
        <a-input v-model:value="data.name" style="width: 100%" placeholder="请输入模型"></a-input>
      </a-form-item>
      <a-form-item label="模型标签" name="tags" :rules="[{ required: true, message: '请输入' }]">
        <a-cascader
          v-model:value="data.tags"
          :field-names="{ label: 'name', value: 'id', children: 'children' }"
          style="width: 100%"
          multiple
          max-tag-count="responsive"
          :options="tags"
          placeholder="请选择标签"
          :show-checked-strategy="Cascader.SHOW_CHILD"
        >
          <template v-if="modelCodeSpinning" #notFoundContent>
            <a-spin size="small" />
          </template>
        </a-cascader>
      </a-form-item>
      <a-form-item name="seq" :rules="[{ required: true, message: '请输入' }]">
        <template #label>
          <span style="margin-right: 5px">排序</span>
          <a-tooltip>
            <template #title
              >排序数值越大，表示同一分类里该模型的排序越靠前，同一排序数值，更新时间越新排序越靠前</template
            >
            <question-circle-filled class="span-mg-left" />
          </a-tooltip>
        </template>
        <a-input-number v-model:value="data.seq" :min="0" :max="9999" style="width: 100%"> </a-input-number>
      </a-form-item>
      <a-form-item label="模型大小" name="size">
        <a-input v-model:value="data.size" style="width: 100%" placeholder="请输入"> </a-input>
      </a-form-item>
      <a-form-item label="发布时间" name="published_at" :rules="[{ required: true, message: '请选择' }]">
        <a-date-picker v-model:value="data.published_at" :disabled-date="disabledDate" style="width: 100%" />
      </a-form-item>
      <a-form-item label="模型类型" name="category" :rules="[{ required: true, message: '请选择' }]">
        <a-select v-model:value="data.category" style="width: 100%" placeholder="请选择" @change="handleChangeCategory">
          <a-select-option v-for="item in modelCategory" :key="item.value" :value="item.value">{{
            item.label
          }}</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="模型来源" name="source_from" :rules="[{ required: true, message: '请选择' }]">
        <a-select v-model:value="data.source_from" style="width: 100%" placeholder="请选择">
          <a-select-option value="thrid">第三方</a-select-option>
        </a-select>
      </a-form-item>
      <!-- <div class="full-width">
        <a-form-item label="模型简介" style="width: 100%">
          <a-textarea v-model:value="data.description" :auto-size="{ minRows: 2, maxRows: 6 }" placeholder="请输入" />
        </a-form-item>
      </div> -->
    </div>

    <div class="textbefo">模型操作</div>
    <div class="form-item-box">
      <a-form-item label="模型操作" name="ops" :rules="[{ required: true, message: '请输入' }]">
        <a-select v-model:value="data.ops" mode="multiple" style="width: 100%" placeholder="请选择">
          <a-select-option value="deploy">部署</a-select-option>
          <a-select-option v-if="data.category !== 'ocr'" value="train">{{
            machineLearningList.includes(data.category!) ? '训练' : '精调'
          }}</a-select-option>
          <a-select-option
            v-if="data.category === undefined || ['llm', 'multimodal', 'ocr'].includes(data.category)"
            value="experience"
            >体验</a-select-option
          >
        </a-select>
      </a-form-item>

      <a-form-item
        v-if="data.category && ['llm', 'multimodal'].includes(data.category)"
        label="模型模板"
        name="template"
        :rules="[{ required: true, message: '请选择' }]"
      >
        <a-select v-model:value="data.template" style="width: 100%" placeholder="请选择">
          <a-select-option v-for="item in templates" :key="item.value" :value="item.value">{{
            item.label
          }}</a-select-option>
        </a-select>
      </a-form-item>
    </div>

    <!-- ocr没有推理引擎 -->
    <div v-if="data.ops && data.ops.includes('deploy') && data.category !== 'ocr'">
      <div class="textbefo">模型部署</div>
      <div class="form-item-box">
        <a-form-item label="推理引擎" name="engine" :rules="[{ required: data.category === 'llm', message: '请选择' }]">
          <a-select v-model:value="data.engine" mode="multiple" style="width: 100%" placeholder="请选择">
            <a-select-option value="transformers">Transformers</a-select-option>
            <a-select-option value="vllm">vLLM</a-select-option>
            <a-select-option value="sglang">SGLang</a-select-option>
          </a-select>
        </a-form-item>
        <!-- 暂时注释 -->
        <a-form
          v-if="data.engine?.includes('sglang')"
          ref="sglangRef"
          :model="sglang"
          class="defcourse"
          label-align="right"
          layout="vertical"
          :label-col="{ style: { width: '200px' } }"
          style="width: 100%"
        >
          <div class="form-item-box">
            <a-form-item
              v-if="data.engine?.includes('sglang')"
              label="工具模版 tool-call-parser"
              name="tool_func_tmp"
              :rules="[{ required: true, message: '请输入' }]"
            >
              <a-input v-model:value="sglang.tool_func_tmp" placeholder="请输入" style="width: 100%"></a-input>
            </a-form-item>

            <a-form-item
              label="mem-fraction-static"
              name="mem_fraction_static"
              :rules="[{ required: true, message: '请输入' }]"
            >
              <a-input-number
                v-model:value="sglang.mem_fraction_static"
                :min="0.1"
                :max="0.9"
                :precision="1"
                :step="0.1"
                style="width: 100%"
              ></a-input-number>
            </a-form-item>
          </div>
        </a-form>
        <a-form
          v-if="data.engine?.includes('vllm')"
          ref="vllmRef"
          :model="vllm"
          class="defcourse"
          label-align="right"
          layout="vertical"
          :label-col="{ style: { width: '200px' } }"
          style="width: 100%"
        >
          <div class="form-item-box">
            <a-form-item name="max_model_len" :rules="[{ required: true, message: '请输入' }]">
              <template #label>
                <span style="margin-right: 5px">最大模型长度</span>
                <a-tooltip>
                  <template #title>多模态模型的最大模型长度建议为 8192</template>
                  <question-circle-filled class="span-mg-left" />
                </a-tooltip>
              </template>
              <a-input-number v-model:value="vllm.max_model_len" style="width: 100%" :min="0"></a-input-number>
            </a-form-item>
            <a-form-item name="gpu_memory_utilization" :rules="[{ required: true, message: '请输入' }]">
              <template #label>
                <span style="margin-right: 5px">GPU 内存利用率</span>
                <a-tooltip>
                  <template #title
                    >gpu-memory-utilization，表示 GPU
                    专用内存（如帧缓冲区）当前被使用的百分比，衡量在计算过程中，模型、纹理、张量或中间结果等数据占用 GPU
                    可用内存的比例 。值为 0.1 意味着分配给模型执行的 GPU 内存占总 GPU 内存的 10%</template
                  >
                  <question-circle-filled class="span-mg-left" />
                </a-tooltip>
              </template>
              <a-input-number
                v-model:value="vllm.gpu_memory_utilization"
                style="width: 100%"
                :min="0.1"
                :max="0.9"
                :precision="1"
                :step="0.1"
              ></a-input-number>
            </a-form-item>
          </div>
        </a-form>
      </div>
    </div>

    <!-- ocr没有模型训练 -->
    <div v-if="data.ops && data.ops.includes('train') && !machineLearningList.includes(data.category!)">
      <div class="textbefo">模型训练</div>
      <div class="form-item-box">
        <a-form-item label="训练方式" name="tf" :rules="[{ required: true, message: '请选择' }]">
          <a-tree-select
            v-model:value="data.tf"
            style="width: 100%"
            :tree-data="treeData"
            tree-checkable
            allow-clear
            :show-checked-strategy="TreeSelect.SHOW_CHILD"
            placeholder="请选择"
            tree-node-filter-prop="label"
          />
        </a-form-item>
      </div>
    </div>
    <!-- <div v-if="data.ops && data.ops.includes('experience')">
      <div class="textbefo">模型体验</div>
      <div class="form-item-box">
        <a-form-item label="体验中心模板">
          <a-select v-model:value="data.exp_temp" style="width: 100%">
            <a-select-option value="text_chat">文本对话</a-select-option>
            <a-select-option value="ocr">OCR</a-select-option>
          </a-select>
        </a-form-item>
      </div>
    </div> -->
    <!-- 部署和训练操作需要选镜像 -->
    <div v-if="data.ops && (data.ops.includes('deploy') || data.ops.includes('train'))">
      <div class="textbefo">模型镜像</div>
      <div class="full-width">
        <a-form-item label="镜像选择" name="docker_image_ids" :rules="[{ required: true, message: '请选择镜像' }]">
          <a-select
            v-model:value="data.docker_image_ids"
            style="width: 100%"
            mode="multiple"
            allow-clear
            placeholder="请选择"
          >
            <a-select-option v-for="n in dockerList" :key="n.id" :value="n.id">{{ n.name }}</a-select-option>
          </a-select>
        </a-form-item>
      </div>
    </div>
  </a-form>
</template>

<style scoped lang="less">
  :deep(.ant-form-item) {
    width: calc(50% - 10px);
  }
  .full-width {
    width: 100%;
    :deep(.ant-form-item) {
      width: 100%;
    }
  }
  .form-item-box {
    display: flex;
    width: 100%;
    flex-wrap: wrap;
    justify-content: space-between;
  }
</style>
