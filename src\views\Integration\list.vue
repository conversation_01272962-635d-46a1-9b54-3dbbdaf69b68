<script setup lang="ts">
  import { ref, reactive, onMounted } from 'vue';
  import type { IFormItem } from '@/components/CustomForm/index.vue';
  import type { IPage } from '@/interface';
  import { CustomForm } from '@/components';
  import type { ColumnType, TablePaginationConfig } from 'ant-design-vue/es/table';
  import { TABLE_PAGINATION } from '@/json/common';
  import { convertIsoTimeToLocalTime } from '@/utils/common';
  import type { IFetchIntegrationList, IIntegrationItem, ISearchState } from '@/interface/integration';
  import addModel from './Components/addModel.vue';
  import { deleteIntegration, getIntegrationList } from '@/api/integration';
  import { useRoute, useRouter } from 'vue-router';
  import { message } from 'ant-design-vue';
  const route = useRoute();
  const router = useRouter();
  const spinning = ref(false);
  const open = ref(false);
  const deleteVisible = ref(false);
  const pagination = reactive({ ...TABLE_PAGINATION });
  const pageParame: IPage = reactive({ page: 1, limit: 10 });
  const tableHeight = ref(0);
  const DEFAULT_SEARCHSTATE: ISearchState = {
    name: '',
    method: '',
  };
  const currentRecord = reactive<{ name: string; id: string }>({
    name: '',
    id: '',
  });
  const searchState: ISearchState = reactive({ ...DEFAULT_SEARCHSTATE });
  const dataSource = reactive<IIntegrationItem[]>([]);
  const formConfig: IFormItem[] = [
    {
      field: 'name',
      type: 'input',
      label: '任务名称',
      placeholder: '请输入',
    },
    {
      field: 'method',
      type: 'select',
      label: '集成方式',
      placeholder: '请选择',
      options: [
        { label: 'Blender', value: 'blender' },
        { label: 'P2L', value: 'p2l' },
      ],
    },
    // {
    //   field: 'status',
    //   type: 'select',
    //   label: '状态',
    //   placeholder: '请选择',
    //   options: [{ label: '创建成功', value: 'Success' }],
    // },
  ];
  const columns: ColumnType[] = [
    { title: '任务名称', dataIndex: 'name' },
    { title: '任务ID', dataIndex: 'id' },
    { title: '集成方式', dataIndex: 'method' },
    { title: '创建时间', dataIndex: 'created_at' },
    { title: '更新时间', dataIndex: 'updated_at' },
    { title: '操作', dataIndex: 'operation' },
  ];

  const toggleTable = (_pagination: TablePaginationConfig) => {
    const { current, pageSize } = _pagination;
    Object.assign(pagination, { current, pageSize });
    Object.assign(pageParame, { page: current, limit: pageSize });
    getList();
  };

  const onFinish = (values: Record<string, string>) => {
    const searchConfig: { [key: string]: string } = {};
    for (const key in values) {
      searchConfig[key] = values[key];
    }
    Object.assign(searchState, { ...searchConfig });
    pageParame.page = 1;

    getList();
  };

  const onRest = () => {
    Object.assign(pagination, { ...TABLE_PAGINATION });
    Object.assign(pageParame, { page: 1, limit: 10 });
    Object.assign(searchState, { ...DEFAULT_SEARCHSTATE });
    getList();
  };

  const closeOpen = () => {
    open.value = false;
  };
  const getList = async () => {
    spinning.value = true;
    const params: IFetchIntegrationList = { ...pageParame };
    Object.keys(searchState).forEach((key) => {
      if (searchState[key as keyof ISearchState]) {
        params[key as keyof ISearchState] = searchState[key as keyof ISearchState];
      }
    });
    const data: { items: IIntegrationItem[]; total: number } = await getIntegrationList(params);
    const { items, total } = data;
    Object.assign(pagination, { total });
    dataSource.length = 0;
    dataSource.push(...items);
    spinning.value = false;
  };
  const getTableHeight = () => {
    const tableItem = document.querySelector('.container');
    tableHeight.value = (tableItem?.clientHeight as number) - 46;
  };
  const handleAddModel = () => {
    open.value = true;
  };
  const clickInfo = (record: IIntegrationItem) => {
    router.push(`/integration/detail/${record.method}/${record.id}?name=${record.name}`);
  };
  const handleExploration = (record: IIntegrationItem) => {
    router.push(`/integration/detail/${record.method}/${record.id}?name=${record.name}&tab=exploration`);
  };
  const handleDelete = (record: IIntegrationItem) => {
    const { name, id } = record;
    Object.assign(currentRecord, { name, id });
    deleteVisible.value = true;
  };
  const confirmDelete = async () => {
    await deleteIntegration(currentRecord.id);
    deleteVisible.value = false;
    message.success('已删除');
    getList();
  };
  onMounted(() => {
    getList();
    getTableHeight();
  });
</script>

<template>
  <div v-if="route.name === 'integration_detail'" class="h-100%">
    <router-view></router-view>
  </div>
  <a-spin v-else :spinning="spinning">
    <CustomForm :form-items="formConfig" @on-finish="onFinish" @on-rest="onRest" style="margin-bottom: 0" />
    <div class="pb-12px">
      <a-button type="primary" @click="handleAddModel">添加</a-button>
    </div>
    <a-table
      :data-source="dataSource"
      :columns="columns"
      :pagination="pagination"
      :scroll="{ y: tableHeight - 150 }"
      @change="toggleTable"
    >
      <template #bodyCell="{ column, record, text }">
        <div v-if="column.dataIndex === 'operation'" class="operation-box">
          <a @click="handleExploration(record)">体验</a>
          <a class="del-btn" @click="handleDelete(record)">删除</a>
        </div>
        <div v-else-if="column.dataIndex === 'name'">
          <a class="table-a-btn" @click="clickInfo(record)">{{ text }}</a>
        </div>
        <div v-else-if="['created_at', 'updated_at'].includes(column.dataIndex)">
          {{ convertIsoTimeToLocalTime(text) }}
        </div>
        <div v-else>{{ text }}</div>
      </template>
    </a-table>
  </a-spin>
  <addModel :open="open" :close="closeOpen" @refresh="getList" />
  <a-modal
    v-model:open="deleteVisible"
    centered
    :title="`确定删除集成任务“${currentRecord.name}”？`"
    @ok="confirmDelete"
  >
    <p>删除后不可恢复</p>
  </a-modal>
</template>

<style scoped lang="less"></style>
