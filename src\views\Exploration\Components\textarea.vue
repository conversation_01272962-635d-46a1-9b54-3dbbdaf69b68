<script setup lang="ts">
  import { ref, reactive, computed } from 'vue';
  import { AudioOutlined, FileImageOutlined, CloseOutlined, StopOutlined } from '@ant-design/icons-vue';
  import sendBtn from '@/assets/image/base/pictures/sendbtn.png';
  import sendDisableBtn from '@/assets/image/base/pictures/sendDisable.png';
  import Recording from '@/assets/image/base/pictures/voice.gif';
  import { WebSocketClient as webSocketClass } from '@/utils/ws';
  import { recOpen, recStart, recStop } from '@/utils/recorder';
  import config from '@/config';
  import { message } from 'ant-design-vue';
  import { uploadImages } from '@/api/exploration';
  interface IProps {
    defaultValue?: { role: string; content: string; image?: string };
    placeholder?: string;
    disabled: boolean;
    category: string;
  }
  interface asrWsProps {
    answer: string;
    isEnd?: boolean;
    isLast?: boolean;
    isFinal?: boolean;
  }
  const props = withDefaults(defineProps<IProps>(), {
    value: '',
    placeholder: '给模型发送消息',
  });
  const isRecording = ref(false);
  const open = ref(false);
  const stopOpen = ref(false);
  const inputMessage = ref(props.defaultValue?.content || '');
  const isImageing = ref(Boolean(props.defaultValue?.image));
  const image = ref(props.defaultValue?.image);
  const fileInputRef = ref();
  const forbidden = computed(() => props.disabled || (!inputMessage.value && !state.start));
  const state: Record<string, boolean> = reactive({
    focus: false,
    loading: false,
    start: false, // 是否开始回答
  });
  const emits = defineEmits<{
    (event: 'send', value: Record<string, string>[]): void;
    (event: 'stop'): void;
  }>();
  // const handleClick = (event: Event) => {
  //   if (state.start) {
  //     handleStop();
  //   } else {
  //     handleSend(event);
  //   }
  // };
  // const handleSend = (event: Event) => {
  //   event.preventDefault();
  //   (event.target as HTMLElement).blur();
  //   state.loading = true;
  //   state.start = true;
  //   emits('send', inputMessage.value);
  //   inputMessage.value = '';
  // };

  // 发送消息
  const handleSend = async () => {
    const value = inputMessage.value;
    if (!value || !value.trim()) {
      open.value = true;
      return;
    }
    if (state.start) {
      stopOpen.value = true;
      return;
    }
    const src = image.value ? JSON.parse(JSON.stringify(image.value)) : '';
    const msg = JSON.parse(JSON.stringify(inputMessage.value));
    const content = src
      ? [
          { type: 'image_url', image_url: { url: src } },
          { type: 'text', text: msg },
        ]
      : [{ type: 'text', text: msg }];
    // @ts-expect-error
    emits('send', content);
    image.value = '';
    inputMessage.value = '';
    isImageing.value = false;
  };

  const changeState = (key: string, bool: boolean) => {
    state[key] = bool;
  };

  const uploadProps = {
    // @ts-expect-error
    beforeUpload: (file: UploadProps['fileList'][number]) => {
      const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/jpg';
      if (!isJpgOrPng) {
        message.warn('上传的图片格式不支持！');
        return false;
      }
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        message.error('上传的图片太大！');
        return false;
      }
      return isJpgOrPng && isLt10M;
    },
    customRequest: async (detail: { file: File }) => {
      const file = detail.file;
      const formData = new FormData();
      formData.append('files', file);
      const res: { url: string }[] = await uploadImages(formData);
      isImageing.value = true;
      image.value = res[0].url;
    },
    multiple: false,
    fileList: [],
    accept: 'image/png,image/jpg,image/jpeg',
    showUploadList: false,
  };
  const handleChange = () => {
    if (inputMessage.value.trim()) {
      open.value = false;
      stopOpen.value = false;
    }
  };
  // 停止生成
  const stopGeneration = () => {
    emits('stop');
    state.start = false;
    stopOpen.value = false;
    // if (abortController.value) {
    //   abortController.value.abort();
    //   abortController.value = null;
    // }
    // isLoading.value = false;
    // if (messages.length && !messages[messages.length - 1].content) {
    //   messages[messages.length - 1].content = '已停止';
    // }
    // clearTimeout(typingInterval.value);
    // typingInterval.value = null;
  };
  const getAudioToTxt = (data: asrWsProps) => {
    let tempText = data?.answer?.replace(/no/, '');
    inputMessage.value = tempText;
  };
  // 语音识别 WS
  const asrWs = new webSocketClass(config.asr, (data: asrWsProps) => {
    if (data?.answer) {
      getAudioToTxt(data);
    }
  });
  const handleRecorder = () => {
    asrWs.connect();

    recOpen(
      () => {
        recStart();
        isRecording.value = true;
      },
      (res: string) => {
        console.error(res || '获取录音权限失败！');
        message.error('获取录音权限失败！');
      },

      (res) => {
        asrWs?.send(res);
      },
    );
  };

  const stopRecording = () => {
    recStop((res) => {
      asrWs?.send(res);
    });
    isRecording.value = false;
  };

  const cancelRecording = () => {
    recStop(() => {
      inputMessage.value = '';
      asrWs?.close();
    });
    isRecording.value = false;
  };

  // @ts-expect-error
  const handleFileChange = async (e) => {
    const file = e.target.files[0];
    if (file) {
      // 这里可以添加文件验证逻辑
      if (file.size > 10 * 1024 * 1024) {
        message.error('文件大小不能超过10MB');
        return;
      }
      const formData = new FormData();
      formData.append('files', file);
      const res: { url: string }[] = await uploadImages(formData);
      image.value = res[0].url;
    }

    // 重置input，以便可以选择同一个文件多次
    e.target.value = null;
  };
  const handleDeleteImage = () => {
    image.value = '';
    isImageing.value = false;
  };
  const handleConfirmUpload = () => {
    console.log(fileInputRef.value);
    fileInputRef.value.click();
  };
  defineExpose({
    changeState,
    state,
  });
</script>

<template>
  <!-- <div class="custom-textarea" :class="{ active: state.focus }">
    <a-textarea
      ref="textareaRef"
      v-model:value="inputMessage"
      :default-value="props.value"
      :rows="3"
      allow-clear
      show-count
      :maxlength="500"
      :auto-size="{ minRows: 2, maxRows: 3 }"
      :placeholder="props.placeholder"
      @focus="state.focus = true"
      @blur="state.focus = false"
      @pressEnter="handleSend"
    />
    <div class="flex flex-justify-end m-t-30px p-b-5px">
      <a-button type="primary" :disabled="forbidden" :loading="state.loading" @click="handleClick">
        <StopOutlined v-if="state.start" />
        <ArrowUpOutlined v-else />
      </a-button>
    </div>
  </div> -->

  <div
    class="chat-box"
    :style="{
      flexDirection: isImageing ? 'column' : 'row',
      padding: isImageing || isRecording ? '10px' : '0 10px',
    }"
  >
    <div v-if="isImageing">
      <div class="img-box" :style="{ backgroundImage: `url(${image || defaultValue?.image})` }">
        <div class="closeIcon" @click="handleDeleteImage">
          <CloseOutlined />
        </div>
      </div>
    </div>
    <div class="flex items-center w-100%" :style="{ flexDirection: isRecording ? 'column' : 'row' }">
      <a-textarea
        v-model:value="inputMessage"
        class="custom-textarea"
        placeholder="请输入你的问题"
        :auto-size="{ minRows: 1, maxRows: 4 }"
        @change="handleChange"
        @press-enter.prevent="handleSend"
      />

      <div v-if="isRecording" class="voice-box">
        <div class="custom-textarea">
          <a-button class="exit-recording" @click="cancelRecording">清空</a-button>

          <div class="recording-status">
            <img class="waveform" :src="Recording" alt="录音波形" />
            <span class="recording-text">录音中</span>
          </div>
          <a-button type="primary" class="exit-recording1" @click="stopRecording">确定</a-button>
        </div>
      </div>

      <div v-else class="flex flex-justify-end items-center">
        <!-- 隐藏的文件输入 -->
        <input ref="fileInputRef" @change="handleFileChange" type="file" style="display: none" />
        <a-popconfirm
          v-if="image"
          title="文件覆盖提示?"
          description="新上传的文件会覆盖原有文件，是否继续上传"
          @confirm="handleConfirmUpload"
        >
          <FileImageOutlined style="font-size: 24px; color: #797979; cursor: pointer" />
        </a-popconfirm>
        <a-upload v-else class="avatar-uploader" v-bind="uploadProps" list-type="text">
          <FileImageOutlined style="font-size: 24px; color: #797979; cursor: pointer" />
        </a-upload>
        <div class="divider m-x-10px"></div>
        <AudioOutlined style="font-size: 24px; color: #797979" @click="handleRecorder" />
        <div class="divider m-x-10px"></div>
        <a-tooltip :arrow-point-at-center="true" :open="open || stopOpen">
          <template #title>{{ open ? '请输入您的问题' : stopOpen ? '请先停止当前回答' : '' }}</template>
          <a-button v-if="state.start" type="primary" shape="circle">
            <StopOutlined @click="stopGeneration" />
          </a-button>
          <template v-else>
            <img v-if="forbidden" class="send-disabled-icon" :src="sendDisableBtn" alt="" :preview="false" />
            <img v-else class="send-icon" :src="sendBtn" alt="" :preview="false" @click="handleSend" />
          </template>
        </a-tooltip>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
  // .custom-textarea {
  //   position: relative;
  //   box-sizing: border-box;
  //   width: 100%;
  //   padding: 0 10px;
  //   overflow: hidden;
  //   cursor: pointer;
  //   border: 1px solid #d9d9d9;
  //   background-color: #fff;
  //   border-radius: 10px;

  //   :deep(.ant-input) {
  //     border: none;

  //     &:focus {
  //       box-shadow: none;
  //     }
  //   }

  //   &:hover {
  //     border-color: #4096ff;
  //     box-shadow: 0 0 0 2px rgb(5 145 255 / 10%);
  //   }
  // }

  // .active {
  //   border-color: #4096ff;
  //   box-shadow: 0 0 0 2px rgb(5 145 255 / 10%);
  // }

  .chat-box {
    position: relative;
    box-sizing: border-box;
    display: flex;
    // align-items: center;
    width: 100%;
    height: auto;
    padding: 0 10px;
    margin-bottom: 10px;
    background-color: #fff;
    border-radius: 6px;
    box-shadow: 0 0 0 2px rgba(5, 145, 255, 0.1);

    :deep(.custom-textarea) {
      position: relative;
      box-sizing: border-box;
      height: auto;
      padding: 10px;
      font-family: PingFangSC, 'PingFang SC';
      font-size: 16px;
      font-weight: 400;
      background: rgb(255 255 255 / 20%);
      border-radius: 6px;
      overflow: hidden; /* 隐藏滚动条 */
      resize: none; /* 禁止用户调整大小 */

      /* 隐藏滚动条 */
      &::-webkit-scrollbar {
        display: none;
      }

      /* 占位符样式 */
      &::placeholder {
        color: #cccccc;
      }
    }
    .divider {
      width: 1px;
      height: 24px;
      background: #646566;
      border-radius: 1px;
    }
    .video-icon,
    .send-icon,
    .send-disabled-icon {
      width: 32px;
      height: 32px;
      cursor: pointer;
    }
    .send-disabled-icon {
      cursor: not-allowed;
    }
    .voice-box {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      height: 48px;
      padding: 0 16px;
      background: rgba(233, 236, 242, 0.7);
      border-radius: 6px;
      backdrop-filter: blur(4px);

      .custom-textarea {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;

        .exit-recording {
          font-size: 14px;
          font-weight: 500;
          color: #666;
          margin-right: 12px;
        }
        .exit-recording1 {
          font-size: 14px;
          font-weight: 500;
          color: #fff;
          margin-right: 12px;
        }

        .recording-status {
          display: flex;
          align-items: center;
          gap: 8px;

          .waveform {
            height: 24px;
            width: auto;
          }

          .recording-text {
            font-size: 14px;
            font-weight: 500;
            color: #1777ff;
          }
        }

        .send-icon {
          width: 32px;
          height: 32px;
          cursor: pointer;
        }
      }
    }
  }

  :deep(.ant-input) {
    border: none;
    &:focus {
      box-shadow: none !important;
    }
  }

  .img-box {
    position: relative;
    width: 100px;
    height: 100px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;

    .closeIcon {
      position: absolute;
      top: 0;
      right: 0;
      cursor: pointer;
    }
  }
</style>
