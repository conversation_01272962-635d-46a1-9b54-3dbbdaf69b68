<script setup lang="ts">
  import { ConfigProvider } from 'ant-design-vue';
  import zhCN from 'ant-design-vue/es/locale/zh_CN';
  import dayjs from 'dayjs';
  import { ref } from 'vue';
  import Layout from '@/layout/Admin.vue';
  dayjs.locale('zh-cn');
  const locale = ref(zhCN);
</script>

<template>
  <config-provider :locale="locale">
    <Layout />
  </config-provider>
</template>

<style lang="less" src="@/assets/css/common.less"></style>
