{"files.eol": "\n", "editor.tabSize": 2, "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "[vue]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.fixAll.stylelint": "explicit"}, "stylelint.validate": ["vue", "less", "css"], "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact", "vue"], "css.validate": false, "less.validate": false, "scss.validate": false, "[dotenv]": {"editor.defaultFormatter": "foxundermoon.shell-format"}, "[groovy]": {"editor.defaultFormatter": "NicolasVuillamy.vscode-groovy-lint"}, "[dockerfile]": {"editor.defaultFormatter": "foxundermoon.shell-format"}, "[ignore]": {"editor.defaultFormatter": "foxundermoon.shell-format"}, "[json]": {"editor.defaultFormatter": "vscode.json-language-features"}, "[properties]": {"editor.defaultFormatter": "foxundermoon.shell-format"}}