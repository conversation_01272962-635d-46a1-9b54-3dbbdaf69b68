import type { IPage } from ".";

export type basics = string | number | boolean;

// @ts-expect-error
export type anyObj = Record<string, basics | anyObj | Array<basics> | Array<anyObj>>;

export interface IClusterDetailResponse {
  name: string; // 工作负载名称
  namespace: string; // 命名空间
  tier: string; // 工作负载分层（通常是业务分层 / 环境分层）
  workload_type: string; // 工作负载类型，固定为 "Deployment"
  description: string; // 工作负载描述
  annotations: { [key: string]: unknown }; // K8s 注解
  labels: { [key: string]: unknown }; // 	K8s 标签
  selector: { [key: string]: unknown }; // 	Pod 选择器
  container_images: string[]; // 所有容器的镜像版本
  update_strategy: string; // 更新策略（一般为 RollingUpdate 或 Recreate）
  max_surge: string;
  max_unavailable: string;
  hpa: { [key: string]: unknown } | null; // 自动伸缩策略（如有 HPA，返回详情，否则为 None）
}

export interface IClusterPosItem {
  name: string; // 容器组名称
  phase: string; // 状态
  node_name: string; // 所在节点名称
  node_ip: string; // 节点 IP
  pod_ip: string; // Pod IP
  creation_time: string; // 创建时间
  start_time: string; // 启动时间
  restart_count: number; //重启次数
  runtime: number; // 运行时长
}

export interface IClusterServiceItem {
  service_name: string;
  type: string;
  cluster_ip: string;
  ports: Ports[];
  creation_time: string;
}

export interface Ports {
  port: number;
  target_port: number;
  protocol: string;
  name: string;
}

export interface IClusterHistory {
  replicaset_name: string;
  images: string[];
  desired: number;
  current: number;
  ready: number;
  creation_time: string;
  revision: string;
}
export interface IClusterEvent {
  type: string;
  reason: string;
  message: string;
  count: number;
  first_timestamp: string;
  last_timestamp: string;
  source: string;
}

export interface IFetchSystemMetrics {
  deployment: string;
  pod: string;
  container: string;
  time_unit?: 'minute' | 'hour' | 'day';
  start_at: string;
  end_at: string;
}
export type IFetchSystemMetricsWithoutContainer = Omit<IFetchSystemMetrics, 'container' | 'deployment'>;

export interface IFetchSystemMetricsResponse {
  time: string;
  cpu_percent: number;
  memory_percent: number;
  gpu_util_percent: number;
  gpu_mem_percent: number;
  [key: string]: unknown; // 允许额外的动态键值对
}

export interface IPodsList extends IPage {
  name?: string;
  status?: string;
}

export interface IPodListResponse {
  name: string;
  ready: string;
  node: string;
  pod_ip: string;
  phase: string;
  container_status: string;
  restart_count: number;
  creation_time: string;
}

export interface IPodContainerItemResponse {
  args: string[];
  command: string[];
  volumes: Volumes[];
  name: string;
  image: string;
  image_pull_policy: string;
  gpu_limit: string;
  cpu_request: string;
  memory_limit: string;
  memory_request: string;
  restart_count: number;
  status: string;
  env: Env[];
  volume_mounts: Volume_mounts[];
  ports: Ports[];
}

export interface Volumes {
  name: string;
  host_path: HostPath;
}

export interface Env {
  name: string;
  value: string;
}

export interface Volume_mounts {
  name: string;
  mount_path: string;
  read_only: null | boolean;
}

export interface Ports {
  container_port: number;
  protocol: string;
}

export interface HostPath {
  path: string;
  type: string;
}

export interface IPodsContainerLogs {
  pod_name: string;
  container_name: string;
  tail_lines?: number;
}

export interface IServiceList extends IPage {
  name?: string;
}
export interface IServiceItemResponse {
  name: string;
  service_name: string;
  type: string;
  cluster_ip: string;
  ports: Ports[];
  creation_time: string;
}

export interface IServiceDetailResponse {
  namespace: string;
  name: string;
  creation_time: string;
  type: string;
  cluster_ip: string;
  session_affinity: string;
  annotations: Record<string, string>;
  labels: Labels;
  label_selector: Label_selector;
  ingress: Ingress;
}

export interface Labels {
  name: string;
  [key: string]: unknown;
}

export interface Label_selector {
  app: string;
  [key: string]: unknown;
}

export interface Ingress {
  ingress_name: string;
  routes: Routes[];
}

export interface Routes {
  host: string;
  path: string;
  path_type: string;
  service_name: string;
  service_port: number;
  protocol: string;
}
export interface ISearchState {
  name: string;
  // type: string;
  // status: string
}
export interface IKubernetesList {
  items: IKubernetesItem[];
  limit: number;
  page: number;
  total: number;
}

export interface IKubernetesItem {
  available_replicas: number;
  creation_time: string;
  images: string[];
  last_update_time: string;
  name: string;
  replicas: number;
  runtime_duration: number;
  status: string;
  type: string;
  unavailable_replicas: number;
}
