import { defineConfig, presetAttributify, presetMini } from 'unocss';

// 刚使用unocss的朋友，可以借助这个工具： https://to-unocss.netlify.app

export default defineConfig({
  presets: [
    presetAttributify,
    presetMini({
      dark: {
        dark: '.van-theme-dark',
        light: '.van-theme-light',
      },
    }),
  ],
  shortcuts: {
    // shortcuts to multiple utilities
    btn: 'py-2 px-4 font-semibold rounded-lg shadow-md',
  },
});
