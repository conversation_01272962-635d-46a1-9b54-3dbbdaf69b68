import { getEnv } from '@/utils';

export const goToLogin = (redirect: string) => {
  const { VITE_CAS_APPID, VITE_CAS_HOST } = getEnv();
  // const appId = getEnv(VITE_CAS_APPID, VITE_CN_CAS_APPID);
  const queryObj: any = { appId: VITE_CAS_APPID };
  if (redirect) {
    queryObj.redirect = redirect;
  }
  if (import.meta.env.MODE === 'development') {
    queryObj.path = location.origin + location.pathname;
  }
  let url = `${VITE_CAS_HOST}/#/login`;
  Object.keys(queryObj).forEach((e) => {
    const value = encodeURIComponent(queryObj[e]);
    if (!url.includes('?')) {
      url += `?${e}=${value}`;
    } else {
      url += `&${e}=${value}`;
    }
    console.log('url', url);
  });
  location.href = url;
};

export const JumpToCasLogin = (path: string = '') => {
  goToLogin(path);
};
