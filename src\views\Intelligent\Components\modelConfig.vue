<script setup lang="ts">
  import { ref, reactive, watch, onMounted, onUnmounted } from 'vue';
  import { DownOutlined, InfoCircleOutlined, SettingOutlined } from '@ant-design/icons-vue';
  import { fetchServiceList, fetchServiceListByUser } from '@/api/exploration';
  import type { IOptions } from '@/interface';
  import type { ICallParams } from '@/interface/exploration';
  import { DEFAULT_CALLPARAMS } from '..';
  import { Empty } from 'ant-design-vue';

  interface IService {
    id: string;
    model_category: string;
    model_name: string;
    service_name: string;
  }
  interface IProps {
    deployId: string | undefined;
    generationConfig?: ICallParams;
  }

  const emits = defineEmits<{
    (event: 'change', deploy_id: string, value?: ICallParams): void;
  }>();
  const props = defineProps<IProps>();
  const formState = reactive<ICallParams>(
    props.generationConfig ? JSON.parse(JSON.stringify(props.generationConfig)) : {},
  );
  const visible = ref(false);
  const isOpen = ref(false);
  const serviceDropdownRef = ref();
  const activedKey = ref('manage');
  const selectedService = ref<IOptions>();
  const presetServices = ref<IOptions[]>([]);
  const manageServices = ref<IOptions[]>([]);
  const userServices = ref<IOptions[]>([]);
  const category = reactive([
    { label: '预制服务', value: 'manage' },
    { label: '我的服务', value: 'user' },
  ]);

  const toggleDropdown = () => {
    isOpen.value = !isOpen.value;
  };

  const handleChangeCategory = (category: string) => {
    activedKey.value = category;
  };

  const handleClickOutside = (event: { target: any }) => {
    if (serviceDropdownRef.value && !serviceDropdownRef.value.contains(event.target)) {
      isOpen.value = false;
    }
  };

  const confirmChange = () => {
    console.log(selectedService.value);
    emits(
      'change',
      String(selectedService.value ? selectedService.value.value : ''),
      props.generationConfig ? formState : undefined,
    );
    visible.value = false;
  };
  onMounted(async () => {
    if (activedKey.value) {
      const manageData: IService[] = await fetchServiceList({ model_category: ['llm', 'multimodal'] });
      const userData: IService[] = await fetchServiceListByUser({ model_category: ['llm', 'multimodal'] });
      manageServices.value = manageData.map((item) => {
        return {
          label: `${item.model_name}(${item.service_name})`,
          value: item.id,
        };
      });
      userServices.value = userData.map((item) => {
        return {
          label: `${item.model_name}(${item.service_name})`,
          value: item.id,
        };
      });
      selectedService.value = props.deployId
        ? [...manageServices.value, ...userServices.value].find((item) => item.value === props.deployId)
        : manageServices.value[0];
    }
    emits(
      'change',
      String(selectedService.value ? selectedService.value.value : ''),
      props.generationConfig ? formState : undefined,
    );
    document.addEventListener('click', handleClickOutside);
    onUnmounted(() => {
      document.removeEventListener('click', handleClickOutside);
    });
  });

  watch(
    [() => activedKey.value, () => manageServices.value, () => userServices.value],
    async ([key]) => {
      if (key) {
        presetServices.value = key === 'manage' ? manageServices.value : userServices.value;
      }
    },
    { deep: true, immediate: true },
  );
</script>

<template>
  <div ref="dropdownRef" class="custom-dropdown">
    <div class="dropdown-trigger" @click.stop="visible = true">
      <div class="default-trigger flex">
        <div>{{ selectedService?.label }}</div>
        <SettingOutlined />
      </div>
    </div>
  </div>

  <a-modal
    v-model:open="visible"
    width="50%"
    centered
    title="模型配置"
    :mask-closable="false"
    @ok="confirmChange"
    @cancel="confirmChange"
  >
    <!-- <div class="m-y-10px">模型配置</div> -->
    <div ref="serviceDropdownRef" class="service-dropdown">
      <div class="dropdown-header" @click="toggleDropdown">
        <div>{{ selectedService?.label }}</div>
        <span class="arrow" :class="{ 'arrow-up': isOpen }"><DownOutlined /></span>
      </div>
      <div v-if="isOpen" class="dropdown-content flex">
        <div class="category">
          <div
            v-for="item in category"
            :key="item.value"
            class="category-title"
            :class="{ actived: item.value === activedKey }"
            @click="handleChangeCategory(item.value)"
          >
            {{ item.label }}
            <span class="category-arrow">></span>
          </div>
        </div>
        <div class="service">
          <div v-if="presetServices.length">
            <div
              v-for="service in presetServices"
              :key="service.value"
              class="service-item"
              :class="{ actived: service.value === selectedService?.value }"
              @click="selectedService = service"
            >
              {{ service.label }}
            </div>
          </div>
          <div v-else class="flex items-center justify-center h-100%">
            <a-empty :image="Empty.PRESENTED_IMAGE_SIMPLE" description="服务准备中，敬请期待... " />
          </div>
        </div>
      </div>
    </div>
    <a-form
      v-if="props.generationConfig"
      v-model="formState"
      autocomplete="off"
      v-bind="{
        labelCol: { span: 4 },
        wrapperCol: { span: 21 },
      }"
    >
      <a-form-item>
        <template #label>
          <span style="margin-right: 10px">最大 Token 数</span>
          <a-tooltip>
            <template #title>大模型返回答案的最大 token 数（范围：1-16384）。</template>
            <InfoCircleOutlined class="span-mg-left" />
          </a-tooltip>
        </template>
        <div class="flex w-100%">
          <a-slider v-model:value="formState.max_tokens" :min="1" :max="16384" style="flex: 1" />
          <a-input-number
            v-model:value="formState.max_tokens"
            :min="1"
            :max="16384"
            class="right-input"
            style="width: 120px; margin-left: 10px"
          />
        </div>
      </a-form-item>
      <a-form-item>
        <template #label>
          <span style="margin-right: 10px">随机度</span>
          <a-tooltip>
            <template #title
              >控制模型输出的随机性（范围 0-1，精度为 0.1）；值越高，输出就越随机，而值越低，输出就越确定。</template
            >
            <InfoCircleOutlined class="span-mg-left" />
          </a-tooltip>
        </template>
        <div class="flex w-100%">
          <a-slider v-model:value="formState.temperature" :min="0" :max="1" :step="0.1" style="flex: 1" />
          <a-input-number
            v-model:value="formState.temperature"
            :min="0"
            :max="1"
            :precision="1"
            :step="0.1"
            class="right-input"
            style="width: 120px; margin-left: 10px"
          />
        </div>
      </a-form-item>
      <a-form-item>
        <template #label>
          <span style="margin-right: 10px">采样率</span>
          <a-tooltip>
            <template #title>指定模型输出的多样性（范围 0-1，精度为 0.1）。值越高，输出的就越发散。</template>
            <InfoCircleOutlined class="span-mg-left" />
          </a-tooltip>
        </template>
        <div class="flex w-100%">
          <a-slider v-model:value="formState.top_p" :min="0" :max="1" :step="0.1" style="flex: 1" />
          <a-input-number
            v-model:value="formState.top_p"
            :min="0"
            :max="1"
            :precision="1"
            :step="0.1"
            class="right-input"
            style="width: 120px; margin-left: 10px"
          />
        </div>
      </a-form-item>
      <a-form-item>
        <template #label>
          <span style="margin-right: 10px">去重强度</span>
          <a-tooltip>
            <template #title
              >通过惩罚已经频繁使用的单词来降低模型行中出现重复单词的可能性（-2 到 2，精度 0.1）。</template
            >
            <InfoCircleOutlined class="span-mg-left" />
          </a-tooltip>
        </template>

        <div class="flex w-100%">
          <a-slider v-model:value="formState.frequency_penalty" :min="-2" :max="2" :step="0.1" style="flex: 1" />
          <a-input-number
            v-model:value="formState.frequency_penalty"
            :min="-2"
            :max="2"
            :precision="1"
            :step="0.1"
            class="right-input"
            style="width: 120px; margin-left: 10px"
          />
        </div>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<style scoped lang="less">
  .custom-dropdown {
    width: 500px;
    position: relative;
    display: inline-block;
  }
  .dropdown-trigger {
    cursor: pointer;
    outline: none;
  }

  .default-trigger {
    min-height: 32px;
    padding: 8px 16px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    background-color: #fff;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    > div {
      max-width: calc(100% - 20px);
      overflow: hidden; /* 超出部分隐藏 */
      white-space: nowrap; /* 强制不换行 */
      text-overflow: ellipsis; /* 超出部分显示省略号 */
    }
    // min-width: 120px;
  }

  .default-trigger:hover {
    border-color: #4096ff;
    color: #4096ff;
  }

  .default-trigger:focus {
    border-color: #409eff;
  }
  .service-dropdown {
    position: relative;
    display: inline-block;
    width: 100%;
    margin-bottom: 20px;
    // min-height: 300px;
  }

  .dropdown-header {
    padding: 8px 12px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    background-color: #f9f9f9;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
    color: #333;
  }

  .arrow {
    margin-left: 10px;
    transition: transform 0.3s ease;
  }

  .arrow-up {
    transform: rotate(180deg);
  }

  .dropdown-content {
    position: absolute;
    background-color: #fff;
    width: 100%;
    box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.1);
    z-index: 1;
    border-radius: 4px;
    overflow: hidden;
    margin-top: 5px;
  }

  .category {
    width: 20%;
    // border-bottom: 1px solid #e0e0e0;
    border-right: 1px solid #e0e0e0;
    .category-title {
      cursor: pointer;
      &:hover {
        color: #1890ff;
        // #0060d4
      }
    }
    .actived {
      color: #0060d4;
    }
  }

  .category:last-child {
    border-bottom: none;
  }

  .category-title {
    padding: 10px 12px;
    font-size: 14px;
    color: #666;
    display: flex;
    align-items: center;
    background-color: #f9f9f9;
  }

  .category-arrow {
    margin-left: 10px;
  }

  .service {
    width: 80%;
  }
  .service-item {
    padding: 8px 12px 8px 24px;
    cursor: pointer;
    font-size: 14px;
    color: #333;
    width: 100%;
  }

  .actived {
    color: #0060d4;
  }

  .service-item:hover {
    // background-color: #f0f0f0;
    color: #1890ff;
  }
</style>
