# Changelog

All notable changes to this project will be documented in this file.

## [unreleased]

### 🚀 Features

- 验证一下merge事件是否被触发 
- 提交changelog功能 
- 测试fix是否被过滤 
- 虚拟形象大使中一键生成页面开发，以及包括文件上传、图片上传功能点 
- 虚拟课堂 
- 虚拟课堂1 
- 虚拟课堂枚举 
- 注释虚拟课堂 
- 剑门关 
- Merg剑门关 
- 【前端】虚拟形象大使中“我的作品”页面 开发，以及包括删除、本地下载等功能 
- 【前端联调】虚拟形象大使页面所有接口联调 
- 【前端联调】虚拟形象大使 “我的作品”接口联调 
- 【前端】虚拟形象大使 “我的作品” 页面的交互效果开发以及样式 
- 【前端】虚拟形象大使中一键生成页面 生成图片的成功、失败、加载交互和整体页的交互效果 
- 剑门关页面 
- 一键生成，缩小时，功能框调出外面 
- Tab页面切换的缓存校验 
- 修改源分支取值逻辑 
- 修改在线pre-commit分支名获取逻辑 
- 完善手动构建源分支取值逻辑 
- 修正合并id查询不到的问题 
- 修改添加合并链接议题评论判断条件 
- 修改邮件发送条件 
- 设置从远程分支更新代码时-不校验提交信息 
- 完善从远程合并的情况 
- 完善通过UI工具合并时的校验 
- 优化一键生成 结果图片的样式 
- 测试一下多项目议题id校验 
- 修复名字重复的校验bug 
- 工作负载列表页和详情页 #222 
- 工作负载容器组页面 
- 工作负载服务与路由 & 历史记录静态页面 #225 
- 工作负载事件静态页面 
- 历史记录接口联调 
- 事件接口联调 
- 工作负载监控静态页面 
- 容器组列表静态页面 
- 容器组 - 容器静态页面 
- 容器组 - 事件静态页面 
- 服务与路由列表静态页面 
- 服务与路由容器组、事件静态页面 
- 服务与路由详情静态页面 
- 工作负载事件页面 
- 监控部分接口 
- 合并master代码 
- 同步master 
- 监控部分接口联调 
- 时间转换 
- 容器组列表页 
- 容器组容器详情页面 
- 容器组监控详情 
- 容器组事件页面 
- 容器组列表新增状态搜索 
- 服务与路由列表页 & 分页参数优化 
- 时间格式优化 
- 服务与路由详情页 
- 剑门关 -- 接口联调 （对话接口和历史id接口） 
- 剑门关接口联调和实现交互样式 #321 
- 返回文字的markdown格式处理 
- 服务与路由容器组页面 
- 服务与路由事件页面 
- 监控详情时间维度与时间范围联动
- 优化剑门关答案的样式 
- 剑门关添加初始欢迎语 
- 【剑门关知识问答】追问问题点击发送后，追问选项按钮应自动隐藏 
- 【剑门关知识问答】“正在思考”中时，快捷指令按钮未置灰禁用 
- 隐藏“虚拟课堂”路由 
- 测试 
- 测试一下 
- Husky执行顺序测试 
- 验证多项目添加议题关联功能 #321 
- 验证多项目添加议题关联功能 #321
- 更新清理本地和远程分支的shell脚本 
- 优化公共变量管理 
- 提交清除历史无用tag脚本逻辑 
- 延长查询镜像制品接口的时间 
- 修改清理docker任务的定时时间 
- 修改清理docker镜像jq是否安装判断条件 
- 修改webhook自动构建canary逻辑  
- 打印删除docker的地址 
- 修改delete接口路径 
- 修改tag保留数量 
- 添加git-cliff配置文件以生成变更日志 
- 添加分支名和提交信息检查阶段以支持自动构建条件 
- 移除分支名和提交信息检查阶段 
- 重构获取提交信息和合并请求ID的逻辑 
- 解析git提交信息并更新分支和提交信息处理逻辑 
- 更新构建描述信息，添加合并提交者或启动者信息 
- 更新合并提交信息解析逻辑，添加凭证处理以支持远程分支获取 
- 虚拟课堂首页 
- 虚拟课堂样式 
- 虚拟课堂选择数字人 
- 虚拟课堂预览 
- 虚拟课空态 
- 虚拟课堂上传接口 
- 虚拟课堂上传PPT解析接口 
- 虚拟课堂编辑 
- 虚拟课堂问答接口 
- 虚拟课堂音频 
- 换模型下来列表接口 
- 模型优化静态页面 
- 添加/编辑模型接口联调 & 模型广场列表接口联调 
- 编辑更新列表 
- 修改判断逻辑 
- 新增模型默认值 
- 合并feat/233-虚拟课堂的内容 
- 模型标签优化 
- 修改模型部署/训练的初始命令 
- 【虚拟课堂】预览功能前端页面开发（按照设计稿的布局进行页面开发，且支持课程全部内容的预览以及视频的播放等功能） 
- 修改bug 
- 【虚拟课堂】预览功能页面的全部接口联调 
- 添加模型类型优化 
- 更新pnpm-lock.yaml文件 
- 修改ocr校验规则 
- 模型广场详情换接口 
- 模型详情优化 
- 编辑模型接口路径变成v2 
- Text 
- 修改图片生成的提示文案 
- 【虚拟课堂】课堂查看功能的前端页面开发 
- 【虚拟课堂】视频生成文案修改 
- 【虚拟课堂】课堂播放页面前端页面开发 
- 【虚拟课堂】课堂播放开发播放器公共组件 
- 【虚拟课堂】课堂查看与播放页面交互逻辑和样式优化 
- 【虚拟课堂】修改失败文案 
- “文案设置”和“答疑设置”都添加气泡提示的效果 
- 添加跳转详情的交互判断 
- 训练详情新增镜像和初始命令 
- 部署详情新增镜像和初始命令 
- 新增删除模型接口 
- 更新 
- 排查dev分支构建报错 
- 修复模型名称bug 
- 模型类型为ocr时特殊处理 
- 【虚拟课堂】课堂播放页面接口联调 
- 处理缺少文案提示(居中）问题 
- 【UI】虚拟形象大使和图片风格转化：生成失败的图标展示被裁了的问题修复 
- 优化失败图标裁剪的问题以及文案设置不换行 
- 修复创建新课堂时，由于异步获取不到projectId的问题导致接口请求失败的问题 
- 开发视频生成的文案与样式 
- 添加视频编辑的tab页入口 开发 
- 【视频生成】前端页面的开发（视频生成要支持上传图片；支持图片的重新上传即为替换） 
- 【生成视频】页面的交互逻辑和样式优化 
- 合并【虚拟课堂】功能 
- 处理流水线发版失败的问题-修改图标名称 
- 知识库列表 
- 知识库文件列表 
- 编辑知识库 
- 创建知识库 
- 创建知识库上传文件 
- 导入文件库 
- 知识库列表页 创建知识库接口联调 
- 知识库列表 删除知识库接口联调 
- 文件列表接口联调 
- 知识库详情接口联调 
- 编辑知识库接口联调 
- 创建知识库提示用户未保存逻辑 
- 导入文件接口联调 
- 删除文件接口联调 
- 【虚拟课堂】首页复制并编辑功能接口对接联调 
- 提交测试用例 
- 修复bug 
- 【视频生成】前端页面的接口联调 
- 添加上传图片删除的功能；视频生成中设置禁用；发送问题后，清空输入的全部文字和图片等内容 
- 模型部署初始命令添加参数 
- 虚拟课堂】首页重新生成功能接口对接联调 
- 数字人音色切换-配合后端修改描述字段的判断展示效果 
- 更新 Cypress 配置，修改支持文件路径并添加登录功能 
- 优化预览的播放逻辑：默认选择第一节分镜，暂停状态，点击播放btn后则开始播放视频；当播放完一个分镜内容后，则自动切换到下一个分镜内容进行播放，直到最后一个播放完，则暂停 
- 预览功能实现鼠标hover分镜选项会出现“播放/暂停btn”；并且点击分镜选项后，则关联切换上方视频内容，并且可播放当前选中的分镜视频，当再次点击该分镜选项，则可暂停视频 
- 添加预览内容为空的是校验 
- 查看页面的交互优化（鼠标hover分镜选项会出现“播放/暂停btn”；点击分镜选项后，则关联切换上方视频内容，并且可播放当前选中的分镜视频，当再次点击该分镜选项，则可暂停视频） 
- 更新 Jenkinsfile，添加 SonarQube 报告功能并优化报告内容  
- 答疑首页面开发，题目进度提示组件开发 #407 
- Test 
- 作答中的组件样式开发 
- 组件样式开发 
- Answer列表接口请求，样式 
- 答疑模式中的前端页面开发 
- 完成答题 & 放弃答题的页面开发 #412 
- 视频模块完成 #413 
- 答疑页面串联 #415 
- PPT查看 #408 
- 一些初始化改动 
- 一些边界改动 
- 修改tab页文案 
- 【生成视频】页面的交互逻辑和样式优化，可支持只有图片生视频 
- 测试SonarQube 扫描结果 
- 【视频生成】优化优化，修复提示文案 #418 
- 【视频生成】优化交互效果，修改文案 #418 
- Commit 
- 智能体列表静态页面 
- 智能体列表页动态加载 
- 创建智能体页面 
- 创建智能体记忆 
- 模型配置组件开发 
- 创建智能体接口联调 
- 发布智能体接口联调 
- 保存新增防抖 
- 预览调试智能体 
- 智能体体验 
- 添加生成失败的判断处理 #475
- 优化按钮样式 
- 交互优化：图片和文字必须上传起码其一 #418
- 记忆变量新增master_id 
- 二次确认弹窗 
- 智能体列表切换分页 
- 重新答题逻辑 
- 刷新页面 
- System改成assistant 
- 添加项目名称更新功能，支持失去焦点时自动保存 
- Test一下 
- 【虚拟课堂】创建课程页面，“合成课程”编辑名称后，“取消”关闭弹窗，课程名称更改联动生效 
- 合成课程按钮校验 
- 浏览器缓存原因，使用304协商缓存了。我使用时间戳作为标识，避免了使用协商缓存，保证都是请求都调用了新接口 
- 点击“视频合成”时添加答疑对为空的校验 
- 优化Docker标签生成，清洗分支名中的非法字符并修复提交信息处理 
- 【虚拟课堂】创建课程，预览课程时，关闭了“预览”窗口，语音播报仍在继续 
- 【虚拟课堂】创建课程，复制并编辑课程，分镜中“答疑”数据未复制成功 #503
- 修复图标被压缩的问题 #512
- 增加提示文案 
- 添加试听的交互逻辑 #517
- 添加试听在进行中，打开预览弹窗时，先暂停试听的音频的交互逻辑 #517
- 修复试听过程中无法切换分镜的问题；添加tts接口请求失败的的判断处理 #449#446
- 修复【虚拟课堂】创建课件页面，“复制并编辑”课件时，保存后，回显了之前打开的其他课件的分镜内容的问题 #517
- Message提示 
- 智能体切片 
- 虚拟课堂列表样式交互 
- 修改模型实例接口路径 
- 处理只上传图片在浏览器做了强缓存的问题 #478
- 修复课件上传后的内容是固定的，切换选择分镜不会影响我的课件的内容，目前切换分镜会同步更换我的课件的内容的问题 #517
- 答题卡选中状态 
- 暂时关闭智能体入口 
- 查看PPT锁定 
- 修复上传文件，解析ppt不成功的问题 
- 修改解析提示文案 
- 智能体入口开放 
- 智能体对话打字效果 
- 修改字段 
- 集群管理页面新增轮询 
- 集群管理页面查看按钮优化 
- 工作负载、容器组、服务与路由互相跳转 
- 容器组状态加标签 
- 集群管理新增运行模式 
- 模型相关接口替换成v2版本 #429 
- 模型详情新增发布按钮 
- 模型广场管理模型状态加红点 
- 集群管理标签颜色优化 
- 模型增加SGLang 引擎并和模型部署方式联动 
- 智能体表格切片展示 
- 智能体loading状态优化 
- 【虚拟课堂】不禁用数字人的选择 
- 修复选中不了数字人3D的问题 
- 【UX】虚拟课堂-查看视频：数字人没有展示，且无法进行播放 
- 【UX】虚拟课堂-新建课程：使用AI生成，二次确认弹窗点击“确定”后，应该弹窗消失，然后出现loading，而不是弹窗与loading同时存在，需要调整 
- 修复问题-目前配音文案在分镜1使用AI生成后loading阶段，若切换分镜2，则AI文案会以分镜2来填充；理应AI生成是由分镜1处触发，生成的文案应在分镜1中 
- 【虚拟课堂】创建课程页面，“配音文案”和“答疑设置”内容为空时，使用“AI生成”无需弹窗确认 
- 不可重命校验 
- 模型名称去除disabled 
- 【虚拟课堂】创建课程页面，“答疑设置”新增答疑，选项内容过长时无法查看全部 
- 生产环境3D数字人加载不成功，禁用3D数字人的选择 
- 更新Husky钩子脚本，优化配置和参数读取 
- 模型详情操作优化 
- 模型名称重名限制 
- 随机字符变成10位 
- 模型引擎选择sglang时新增工具模版字段 
- 模型回显sglang参数 
- 模型训练方式动态展示，新增模型训练框架 
- 服务与路由字段调整 
- 时间格式修改 
- 按照设计稿修改按钮名称 
- 【虚拟形象大使】1. 名称的交互优化 
- 【虚拟形象大使】3.页面布局按照设计稿优化 
- 按照设计图重新调整前端页面的布局以及添加模型加载的按钮 
- 【数字人对话】物料选择区的的交互开发 
- 【数字人对话】开发模型还未加载的空态页面以及交互逻辑 
- 【数字人对话】模型加载的按钮以及相关的逻辑功能的开发 
- 【数字人对话】背景不支持直播过程切换，按照目前逻辑是要提前预设选择好响应的物料信息 
- 开发Icon公共组件，并且修改数字人对话的图标 
- 【UX】虚拟课堂- 新建课程：目前上传课件还没上传成功就toast“上传成功”，需要调整 
- 【虚拟课堂】编辑功能时点击提示区域不能退出编辑功能 
- 【虚拟课堂】初始页面的背景颜色调整 
- 训练隐藏初始命令 
- 部署支持sglang引擎 
- 【虚拟课堂】创建课程页面，调整标题样式并移除未使用的图标 
- 【虚拟课堂】上传组件样式调整，移除未使用的图标并添加自定义样式 
- 【虚拟课堂】首页课件单元模块的操作按钮，未按照设计原型规则实现 
- 智能体状态优化 
- 智能体搜索假防抖 
- 鼠标移入图标显示蒙板 
- 修复音色不是全局应用的问题 #499
- 【虚拟课堂】创建课程页面，课程名称被清空后，未按需求原型恢复最近一次编辑的命名内容 #460
- 知识库回显文件信息 
- 在新建智能体时点击创建知识库新开一个页面 
- 记忆变量整体提交 
- 查看知识库回显知识库名称 
- 记忆变量校验 
- 知识库向量模型置灰 
- 【虚拟课堂】保存操作都会调到第一个分镜的问题修复 
- 【虚拟形象大使】保存形象图片时，名称为空时，前端作拦截处理 
- 【虚拟课堂】创建课程页面，上传的课件按名称进行分类排列展示 #467
- 【虚拟课堂】生成失败的课程，点击“重新生成”后， 重新刷新页面 
- 【虚拟课堂】创建课程页面，上传的课件第二次上传时会清空第一次上传文件的名字 #467
- 数字人模型加载逻辑根据沟通的结果优化修改 
- 【数字人对话】支持切换背景和音色功能开发
- 智能体首页未发布应用新增红字提示 
- 重名校验做防抖 
- 模型训练输出名称添加重名校验 
- 【虚拟形象大使】保存形象图片，名称输入“#”号时，接口参数有误，添加特殊符号的校验 
- 移除目前尚不成熟的rolldown-vite配置 
- 清洗docker tag中的中文字符以符合命名规范，优化提交信息获取逻辑 
- 清洗docker tag中的非法字符，确保符合命名规范 
- 添加sonarQube报告生成功能 
- 更新Jenkinsfile，添加SONAR_HOST环境变量并优化pre-commit脚本调用 
- 更新VSCode和SonarQube配置，添加json和properties格式支持，优化项目属性注释 
- 【数字人】添加tab页面，以及调整原先的数字人对话页面的布局和样式 
- 整理【数字人】的文件结构 
- 【数字人管理】开发整体的页面 #587
- 【数字人形象管理页面】我的数字人要支持创建数字人功能的弹窗开发，头像上传之前只支持点击上传，要添加拖拽上传的功能 
- 开发声音选择的弹窗以及交互逻辑 #597
- 【数字人管理】按照数字人训练进度的展示对应的样式交互 
- 【数字人形象管理页面】创建数字人的接口新字段的接口联调 
- 模型列表静态页面 
- 添加模型集成任务 
- 模型集成列表接口联调 
- 添加模型集成任务接口联调 
- 模型集成详情 
- 模型集成历史记录 
- 模型集成体验 
- 新建重名校验 & 删除接口联调 #263 
- 添加模型集成任务时过滤服务 
- 模型训字段优化 
- 模型训练字段调整 
- 编辑智能体时只传改变了的参数 
- 回答过程中不允许再次提问 
- 智能体添加语音输入功能 
- 【数字人形象管理页面】生成完成的数字人要支持数字人的音色，动作的预览和试听的弹窗开发功能 
- 【数字人管理】训练成功、失败以及训练中的不同状态对应不同操作权限的交互开发；联调“重新生成”和“删除”数字人的接口 
- 完成 成功数字人支持删除功能，并且完成接口联调，删除完成会重新调用列表接口，刷新数字人列表 
- 开发重命名的弹窗名字确认以及完成接口联调 
- 删除功能需要设置二次确认的弹窗 
- 添加删除确认弹窗功能，优化数字人删除操作体验 
- 开发音色管理页面，添加音频播放功能和标签筛选 
- 【音色管理】合并所有的方言 
- 多模态适配 
- 添加数字人生成失败的重新生成和删除功能，优化加载组件样式 
- 更新人物角色选择逻辑，支持从人物库和我的人物来源选择，并传递角色信息 
- 添加角色重新生成时的参数信息，优化重新生成的训练逻辑 
- 增加角色选择组件的索引和来源参数，优化角色选择逻辑 
- Unsolth 训练方式回显 
- 【数字人】创建数字人，合成预览形象后修改配置参数重新“合成预览”，右侧预览界面未显示“加载中”效果 
- Unsloth 训练任务详情 
- 训练任务新增GPU 显卡字段 
- 部署任务新增GPU 显卡字段 
- 添加空状态展示，优化数字人列表为空时的用户体验 
- 优化加载组件样式，更新加载提示信息和指示器 
- 添加数字人名称属性和删除确认弹窗，优化加载组件 
- 【声音管理页面】我的声音页面布局按照UI设计图开发（包括空态条件下的页面开发） 
- 【声音管理】克隆声音的弹窗页面开发（1. 支持上传音频和录制音频两种功能 2. 支持随便切换某一段预设文案的提示） 
- 添加音频上传功能，支持wav格式文件上传并展示上传状态，同时新增建议朗读文案列表 #669
- 完成上传音频的交互效果开发 
- 添加录音功能，支持实时录音和音频上传，优化用户交互体验 #675
- 添加声音克隆功能，支持通过音频ID启动克隆操作，并优化用户交互体验 
- 添加recBlobStop函数以优化录音结束处理，更新AddModal以使用新录音功能 
- Mock添加获取声音列表功能，优化声音选择界面，支持播放音频 
- 声音列表的接口联调 
- 增加声音列表项的加载状态显示，支持失败和处理中状态的用户反馈 
- 添加声音删除和重命名功能，二次弹窗确认优化用户交互体验 #661
- 优化加载组件和声音管理功能，支持多种失败状态提示及删除操作 
- 更新加载组件样式并优化声音状态提示，增加失败和处理中状态的图标显示 
- 增加声音列表功能，支持获取和管理用户声音，优化声音选择界面 #661 
- 添加创建声音功能，增加弹窗以支持用户创建新声音 
- 增加音频文件上传和录音时长校验功能，确保音频时长在5-30秒之间 #679#718
- 增加说话人选择功能，支持通过ID和类型选择说话人，优化声音选择逻辑 #679
- 添加声音克隆操作后触发刷新说话人列表 #679
- 在声音克隆操作中添加加载状态，关闭弹窗刷新列表，优化用户交互体验 #679
- 添加名称已存在的错误提示，优化用户反馈体验 
- 添加录音图标悬停效果，优化用户交互体验 
- 更新视频控件属性，禁止下载和播放速率调整 
- 添加空态声音提示和创建声音按钮，优化用户体验 #677
- 放开p2l 
- 隐藏模型集成 
- 隐藏融合模型 
- 模型关联镜像 
- 智能体追问页面 
- 我的模型 
- 添加模型去除体验模版 & 部署训练接口联调 #609  
- 0711优化  
- 添加模型去除体验模版  
- 删除我的模型用post方法 
- 删除成功关闭弹窗 
- 智能体新增追问功能 
- 优化调用追问接口时机 
- 我的模型部署 
- 我的模型列表 
- 机器学习训练详情 #592 
- 添加名称输入错误提示，优化用户反馈体验 
- 动态调整说话人容器最大高度，优化界面适应性 

### 🐛 Bug Fixes

- 修复镜像遗留的bug 
- 容器组详情换字段 
- 【虚拟形象大使】材料文件上传后被移除，“立即合成”时，传参中还是带上了被“移除”的文件 
- 【虚拟形象大使】形象手稿上传图片后再重新上传替换，“立即合成”时传参中上传了2个文件 
- 修复容器组详情中字段 
- 修改容器组监控详情参数 
- 【虚拟形象大使】文件超大上传失败时，页面出现多条异常提示 
- 【虚拟形象大使】我的作品中的数据，未按需求按生成时间作倒序排序 
- 【剑门关知识问答】发送问题并生成回答后，对话气泡未自动下扩 
- 【虚拟形象大使】形象合成失败后，页面出现多条异常提示 
- 【文生视频】修改请求地址 
- 【剑门关知识问答】提问快捷指令问题“剑门关景区介绍”和“剑门关历史背景”问题时，tts接口报500 
- 欢迎语播放 
- 处理开发过程中后端协商接口都使用dev环境变量，但上生产环境访问不了dev的服务 
- 处理语音中断逻辑，添加队列清空条件 
- “文生图”的页面文案修改 
- 【一键生成】tab页切换的文案修改 
- 切换不同页面路由或者关闭页面把语音中止播放 
- 移除条件中的gitlab用户名检查以简化逻辑 
- 修正安装命令中的多余空格 
- 修复新增编辑后关闭弹窗异常bug 
- 修改默认值 
- 删除多余代码 
- 概览图表 
- 集群概览接口对接 
- 集群概览路由跳转 
- 集成概览问题修复 
- 集群概览优化 
- 集群概览样式优化 
- 修正 Cypress 运行命令的名称 
- 将 Cypress 运行命令的浏览器更改为 Chrome 
- 解决冲突 #413 
- 修复模型部署初始命令bug 
- 集群概要优化 
- 修改文件上传异常问题 
- 修改回退bug 
- 上传文件数量限制 
- 播报结束展示第一帧 
- 集群健康 优化 
- 语音播报 
- 修正 README.md 中 commitlint.config.js 文件路径格式 
- 修复不能提交代码的认证 
- 样式修改 
- 浏览器视频播放策略,操作按钮隐藏 #509 
- 集群健康修改 
- 集群排版修改 
- 知识库文件状态搜索可清空 
- 训练方式必填 
- 模型草稿隐藏模型实例页面 
- 修复bug #496 #455 #468 
- 新增提示文案 
- 发布时间修改 
- 禁用数字人选择 
- 修改调用接口名称 
- 集群概览文案修改 
- 【虚拟课堂】创建课程页面标题显示成了“创建课堂” 
- AI生成失败的情况需要弹出提示语句，并且loading状态恢复为初始状态 
- 【视频生成】发送图片或描述，接口正常返回音频链接后，前端未展示对应视频内容 
- 【虚拟课堂】创建课程，上传文件，接口请求（文件上传）过程中，页面没有任何动态效果，感知不到是在上传 
- 【虚拟课堂】创建课程页面，课件上传后内容在分镜中显示被“拉伸”了 
- 负载列表随机顺序问题 
- 文案修改 
- 修正 README.md 中 npm 安装命令的格式 
- 模型配置新增图标 
- 修改模型配置弹窗文案 
- 智能体状态筛选加清除按钮 
- 修改智能体列表空状态文案 
- 数字人对话锁定至最新内容处 
- 数字人加载失败-重新加载btn需要居中 
- 生产环境禁用3D数字人选择 
- 编辑模型不需要校验重名 
- 解决冲突 
- 修复答题卡UI问题 
- 一键清空 
- 模型配置默认值修改 
- 已发布状态的智能体禁止发布 
- 修复自动保存失败问题 
- 未选择模型时禁止发布 
- 角色指令添加提示文案 
- 智能体空桩体文案优化 
- 发布状态搜索 
- 键盘‘回车’发送后，内容未自动清空 
- 提问者和回答者的气泡相反了 
- 修复知识库搜索无效问题 
- 知识库弹窗二次回显 
- 移除知识库触发保存 
- 移除知识库bug修复 
- 背景图片添加删除功能 
- 调试与预览做适配 
- 模型部署问题修复 
- 训练名称统一 
- 记忆变量问题修复 
- 记忆变量校验逻辑修复 
- 触发保存时优化，只在修改名称时校验重名 
- 解决发版样式不生效的问题以及删除无效的方法代码 
- 【数字人对话】解决线上样品不生效的问题 
- 图片风格转换问题 
- 文生图自动显示最新内容 
- 切换部署方式异常修复 
- 打开 transformers 
- 修复集群管理页面接口报错问题 
- 创建课程页面，草稿保存后的提示内容 
- 【虚拟课堂】新增分镜时，ppt内容为空时要禁用AI生成的按钮 
- 隐藏3D数字人 
- 禁用3d数字人 
- 智能体调试根据情况传入知识库参数 
- 修改文字颜色 
- 【数字人对话】左侧的人物选中状态 
- 【数字人对话】左侧的音色选中状态 
- 模型模版在模型类型为vllm时必填 
- 【数字人对话】人物、背景和音色选中状态保持一致的处理 
- 概览样式优化 
- 智能体对话状态优化 
- 修复REGISTRY_HOST的URL格式，优化合并提交信息解析逻辑 
- 修复pre-commit脚本参数传递，确保使用正确的分支名称 
- 移除重复的源分支信息，并更新邮件通知收件人列表 
- 更新Jenkinsfile中的凭证ID以使用正确的jenkins-ci凭证 
- 优化邮件发送条件判断逻辑，确保在正确的环境和分支下发送邮件 
- 更新SonarQube报告字段，修复提交ID显示和API调用中的指标名称 
- 修复SonarQube报告中的可维护性问题指标，确保正确显示相关数据 
- 更新eslint检查文件类型，支持tsx格式并注释掉原始JSON响应打印 
- 编辑模型名称去除disabled 
- 【虚拟课堂-预览视频】修复预览的播放逻辑，暂停后点击播放则应该接着播放，而不是重新播报该分镜的内容 
- 【虚拟课堂】修改文案内容 
- 【虚拟课堂】修复新增和删除分镜时，自动跳到第一分镜的问题 
- 【虚拟课堂】预览视频，修改hover的图标 
- 【虚拟课堂】点击【合成课程】显示的那个提示“已添加到我的视频”，改为“课程正在合成中” 
- 【虚拟课堂】修复合成按钮点击没反应的问题 
- 【虚拟课堂】合成视频的提示文案修改 
- 文生图样式优化 
- 视频模型 
- 虚拟形象 模型显示 
- 剑门关模型 
- 数字人模型显示及样式优化 
- 修复模型集成bug #555 #556 
- 智能体bug修复 
- 文件显示错误信息 
- 模型准备中 
- Unsloth 训练方式显示问题 
- Tooltip 语法不对，使用a-tooltip 
- 修复切换我的服务是数据不更新问题 
- 模型集成历史记录新增状态字段 
- 切换tab更新历史记录 
- 多模态只输入文字提问报错bug修复 
- 智能体报错回显 
- 编辑模型发布时间异常问题修复 
- 智能体回答过程中不允许再提问 
- 多模态模型不传图片是正常提问 
- 添加模型名称去除前后空格 
- 体验中心没传图片bug修复 
- 智能体未配置模型时不能发布 
- 停止状态不需要disabled 
- 录音取消时清空文字 
- 修复训练开始后未立即更新列表的问题，添加延迟调用getList() 
- 调整组件高度和标签文本，优化布局和样式 
- 添加延迟调用getList()以确保列表在操作后更新 
- 更新选中说话人逻辑，确保默认说话人正确设置 
- 更新空人物图像和提示文本，优化创建数字人按钮样式 
- 隐藏p2l 
- 注释掉训练成功提示信息和关闭模态框的调用，优化RolesChange和DigitalImage组件的代码结构 
- 注释“视频编辑”的入口 
- 修复录音权限获取的提示 
- 优化音频上传和录制提示，改善用户体验 
- 模型广场按钮显示异常 & 编辑报错 #610 
- 修复页面不滚动问题 
- 修复模型集成体验提示文案一直存在的问题 
- 追问开关默认关闭 
- 修复ocr模型体验跳转异常问题 
- 最大模型运行参数跟随模型类型切换 
- 体验中心文本对话新增多模态类型 
- 优化智能体详情接口调用时机 
- 修改bug #643 #657 
- P2l 体验新增较模型 

### 🚜 Refactor

- 更新背景视频样式，优化视频居中显示和交互属性 
- 移除背景更换成功提示的延迟显示逻辑 
- 修改RolesChange组件的props定义方式，优化代码结构；注释掉handleCloseModal调用，更新训练成功提示信息 
- 完成接口联调，并且优化声音数据传递和状态管理 

### 📚 Documentation

- 修复README.md中的格式和空格问题 
- 修复README.md中npm安装命令的格式 
- Auto-generate changelog [ci-skip] 

### 🎨 Styling

- 一键生成 样式优化 
- 面包屑样式优化 
- 【UI】虚拟形象大使：我的作品-下载和删除图标需要调整 
- 【UI】虚拟形象大使：我的作品-为空图标更新 
- 优化剑门关UI样式 
- 样式优化 
- 模型广场样式优化 
- 智能体首页样式 
- 文案优化 
- 智能体详情样式优化 
- 操作成功的提示文案改为已xx 
- 模型配置样式问题优化 
- 重命文案优化 
- 编辑智能体样式问题修复 #482 
- 角色指令样式优化 
- 创建知识库样式优化 
- 知识库相关样式问题 #497 #502 #503 
- 导入文件样式优化 
- 添加变量样式优化 
- 智能体样式调整 
- 智能体、知识库样式调整 #475 
- 知识库样式调整 
- 添加知识库、记忆变量新增气泡提示 
- 文本中心样式调整 
- 编辑智能体样式优化 
- 模型广场样式调整 
- 【虚拟课堂】优化tabs样式和素材文档标题样式 
- 首页按钮样式优化 
- 重名去除前后空格 
- 【创建数字人】优化弹窗样式以及加载的效果 
- 操作功能的下拉按钮样式优化 
- 优化数字人形象的hover效果 
- 添加数字人名称的margin样式，优化布局 
- 更新AvatarChat声音组件背景样式，调整hover效果注释 
- 增加加载指示器图标大小，优化用户体验 
- 增加说话人名称的上边距，优化布局 
- 注释掉speaker选择 
- 体验中心文本对话样式优化 
- 修改AvatarChat组件中的图标触发方式和样式，优化用户交互体验 
- 更新AvatarChat组件的提示文本和样式，优化用户体验 
- 调整AvatarChat组件中的样式，去除上传提示文本的下边距，优化视觉效果 
- 更新AvatarChat组件中的头像图标样式，调整宽度保证图片不被压缩 

### 🧪 Testing

- 测试逻辑修改之后的邮件发送信息获取是否正确 
- 添加课程数据加载成功的断言注释 

### ⚙️ Miscellaneous Tasks

- *(release)* 0.0.2
- *(release)* 0.0.3
- *(release)* 0.0.4
- *(release)* 0.0.5
- *(release)* 0.0.6
- Update Jenkinsfile，test和canary环境部署才代码扫描，发邮件通知
- 升级构建工具vite 
- 删除cliff.toml配置文件 

## [0.0.1] - 2025-05-20

### 🚀 Features

- Init
- 交互式配置页面开发
- 计算资源设置 & 样式优化
- 优化代码
- 面包屑可跳转
- 封装 request
- 注释代码
- 任务列表菜单
- 接口联调
- 新增删除任务功能
- 新增处理时间函数
- 模型训练任务列表接口联调
- 修改超参数
- Update vite.config.ts
- Update package.json
- 优化参数配置
- Vite.config.ts
- 路由切换hash模式
- 新增菜单图标
- 解决@ant-design/icons-vue引起的运行报错
- 修改gitlab-ci cache设置#AI-513
- 提取build_deploy.sh中的公共逻辑#AI-513
- 文件临时重命名
- 文件重命名
- 更新pnpm-lock.yaml文件
- 修改prettier格式化规则
- 修改命名规范#AI-513
- 删除临时命名文件
- 优化from样式
- 模型指标监控轮询
- 修改接口转发规则
- 添加样式校验规则#AI-513
- 提交项目VSCode配置文件
- 补充超参数说明
- 优化指标监控
- 加空格
- 更换logo
- 提交测试用例
- 验证测试报告生成功能
- 添加预览测试用例功能AI-535
- 备份工作文档
- 给项目添加vitest测试框架功能
- 给项目添加生成测试覆盖率依赖包
- 模型部署页面 #DATAFOX-345
- 部署列表 & 详情页静态页面 #DATAFOX-341 #DATAFOX-326
- 联调接口 #DATAFOX-344
- 联调接口 #DATAFOX-348
- 创建中无法调试
- 请求新增Request_id
- 服务调试页面优化 #DATAFOX-462 #DATAFOX-463 #DATAFOX-464
- Canary打包测试#AI-629
- 项目代码格式初始化 #AI-699
- 错误提示优化
- 错误码优化
- 触发重新构建
- 禁用gitlab-ci流水线
- 提交eslint代码检查运行修复之后的结果 #AI-699
- 修改保持时自动化格式规则
- 调试登录
- 登录逻辑
- 没有token跳转cas登录
- 登录bug修复
- 标签页增改查
- 拖拽优化
- 添加环境变量
- 编辑标签优化
- 添加Jenkinsfile文件 #DATAFOX-686
- 修改gitlab仓库地址
- 打包构建测试  #DATAFOX-686
- 移除提交代码自动打包功能 #DATAFOX-686
- 验证Jenkins部署功能是否生效  #DATAFOX-686
- 恢复gitlab-ci构建
- 验证Jenkins打包是否生效
- 合并特性分支代码
- 恢复现有构建逻辑
- 测试webhook
- 注释掉gitlab-ci
- 添加自动构建配置
- 提交推送release分支时jenkins自动构建test环境功能
- 验证一下release分支部署test环境是否生效
- 测试构建分支是否正确
- 修改triggers定义
- 修改jenkinsfile文件配置
- 调试分支获取
- 测试是否触发构建
- 测试为什么不触发
- 测试webhook推送分支名
- 配置监听分支
- 推送测试
- 调试获取分支名
- 测试release动态分支构建
- 提交Jenkins改动
- 将环境变量修改为动态判断 #DATAFOX-672
- 测试拉取分支是否正确
- 测试拉代码次数
- 验证/确保只执行一次代码检出
- 注释掉镜像构建约束条件
- 开发环境测试构建
- 测试release发布dev环境
- 修改部署逻辑
- 测试手动部署逻辑
- 查看环境变量
- 提交hotfix分支自动构建功能
- 调试合并参数
- 测试是否可以获取环境变量
- 修改部署条件
- 禁用gitlab-ci
- 分支初始化
- 更新Jenkinsfile配置文件
- 恢复canary环境gitlab-ci配置
- 使用release/20250423部署test
- 锁定依赖
- 解决startsWith引起的报错
- 测试环境部署是否正确
- 部署canary环境验证
- 修改部署构建标识
- 测试release分支构建
- 自动构建测试
- 触发构建
- 触发构建测试
- 拉取最新代码测试
- 拉取代码测试
- Jenkinsfile文件缓存清理测试
- 添加清理工作区功能
- 触发版本更新
- 触发一次自动部署
- 测试拉取hash是否正确
- Hash值测试
- Jenkins配置更新测试
- 打印判断条件
- 自动触发更新
- 修正脚本错误
- 测试构建
- 添加调试打印
- 查看webhook合并变量
- 注释调试语句
- 更新Jenkinsfile hash取值
- 修改hotfix手动合并判断条件
- 添加禁止并发构建功能
- 合并测试
- 修改merged通过事件
- 修改镜像tag地址取值方式
- 修改项目命令空间命名
- 修改项目名称取值
- 提交Jenkins配置修改
- 修改eslint配置
- Merge release测试1
- 查看日志改为500行
- 验证hotfix合并master是否触发自动化构建
- 验证hotfix是否会触发构建
- 验证hotfix合并构建测试
- Hotfix合并master测试
- 查看手动同意合并时的webhook变量
- 更新Jenkinsfile 获取提交hash值取值名称
- 修改hotfix合并master分支判断条件
- 查看merge事件
- 测试哪种事件会被推送过来
- 触发真实构建master
- 推送测试2
- 推送测试3
- Hotfix合并master测试1
- 解决冲突
- 添加调试打印语句
- 打印appId
- 修正生产跳转地址
- 指定Jenkins构建机器
- 触发master节点构建
- 修改生产环境变量配置条件
- 修改eslint 校验规则
- 修改sonarQube使用的检验报告
- 提交代码增量检查功能
- 部署canary测试
- 修改手动部署获取逻辑
- 修改判断条件
- 修改prepare阶段功能
- 给镜像加上短hash
- 修改命名规则
- 修改tag获取逻辑
- 部署更新测试
- 修改部署信息
- 恢复Jenkins输出信息
- 修改Jenkins中部分不符合规范的语法
- 脚本告警修复测试
- 测试git仓库是否配置在本地就可以了
- 测试是否将仓库配置在
- 修改使用的分支是否正确
- 只为触发构建
- 设置制定拉取分支代码功能
- 触发更新
- 提交最新修改
- 添加清理空间功能
- 修改拉取代码的顺序
- 缓存清理
- 修改checkout执行顺序
- 清除缓存测试
- 添加获取构建者逻辑
- 调试代码无法检出问题
- 排查无法检出代码的原因
- 触发校验
- 测试git仓库为什么拉取不下来
- 让husky通过校验，否则提交不了
- Ocr静态页面 Ref 6-ai-service-platform
- 文本对话静态页面优化 Ref algorithm/6-ai-service-platform
- 文本对话
- 文本对话功能
- 文本对话 & 历史记录
- 历史记录
- 优化滚动
- 触发husky校验
- 提交Jenkins的发邮件功能
- 提交Jenkins文件修改
- 复制镜像
- 前端还原上下线
- 体验中心跳转
- 管理员可以看到所有菜单
- K8s静态页面
- 关闭会报错的几条规则
- 恢复sonar配置
- 测试
- Test
- 恢复Jenkins文件命名 #DATAFOX-672
- 测试一下同一套构建产物部署两个环境
- 注释环境变量打印语句
- 提交Jenkins最新的构建流程
- 触发Jenkins自动构建
- 测试推送
- 测试是否触发Jenkins构建
- Dev环境构建测试
- 调试Jenkins侧边栏显示
- 修复Jenkins脚本报错
- 测试一下build信息定义
- 修复脚本报错
- 修改Jenkins侧边栏信息
- 修改获取启动者脚本
- 添加抛出错误逻辑
- 修改获取启动者方法
- 打印环境变量
- 修改启动者获取方式
- 测试合并是否触发更新
- 测试合并事件
- 修改镜像名称
- 测试二次构建是否刷新
- 构建刷新测试
- 第一次合并测试
- 修改脚本
- 第一次修改，查看是否生效
- 修改镜像拉取策略
- 排查镜像不更新问题
- 持续更新测试
- 第二次合并测试
- 特性分支合并测试
- 特性分支合并复测
- 构建测试
- 测试是否使用的是devJenkins
- 部署验证
- 开发数字人对话首页和处理前端跨域问题，以及完成部分接口联调
- 修复图片路径
- 切换背景联调
- 开发创建数字人的的整个流程以及联调接口，大致调了一下样式
- 开发公共的loading组件以及联调“人物库”/“我的人物"等列表的接口，调整样式
- 提交git忽略设置文件
- 提交数字人创建功能
- 同步创建数字人功能
- 开发websocket连接、断开等功能以及详情内容的展示与样式优化
- 开发问题气泡展示；详情列表添加滚动条以及样式优化
- 提交husky规划
- 忽略扫描产生文件
- 开发直播流组件以及联调文字返回和语音播报接口
- 测试husky检查范围
- 修改校验规则
- 开发音色切换功能
- 开发加载中和重新加载的样式
- 解决WS请求跨域的问题
- 直播流优化
- 换背景接口添加contextId传参
- 开发音色选择的试听功能
- 查看merged状态
- 请求审核状态获取
- Merge审批状态获取
- 设置检测到手动刷新的时候断开WS，等待重新加载完成时再次连接WS
- 修复环境变量不匹配的问题
- 移除loading动画
- 放开标准部署方式
- 将任意字符串转换为合法的 Kubernetes 名称（RFC 1123 label 格式）
- 提交Jenkins更新
- 提交Jenkins修改
- 提交调试通过的Jenkins文件
- 恢复eslint功能
- 修改eslint上报步骤位置
- 修改手动部署分支切换逻辑
- 修复lock文件报错
- 优化branchName取值
- 提交选择镜像部署功能
- 调试添加评论功能
- 添加评论功能
- 测试一下邮件发送
- 测试发送邮件
- 添加docker忽略文件
- 优化docker构建上下文
- 修改Jenkins变量值写法
- 修改部署环境变量
- 打印发送邮件的条件
- 打印一下环境变量
- 修改发送邮件的条件
- 删除dockerignore文件
- 修改test合并请求判断逻辑
- 集中定义环境变量
- 修改方法定义
- 修复bash -c语法报错
- 添加提取合并源分支的逻辑
- 修改邮件发送条件
- 修改邮件发送人 Related to 
- 测试一下  research-and-development/algorithm
- 模型训练列表字段修改
- 去除模型部署页面计算资源列
- 添加写入评论列表判空条件
- 添加部署阶段部署镜像不能为空的执行条件
- 提交分支名和提交语校验功能 
- 修复打包构建问题 
- Eslint测试 
- 兼容feature分支命名 
- 修改分支名提示语 
- 添加提交信息关联议题id操作说明  
- 修改readme文档 
- 提交定时清理docker镜像脚本 
- 修改harbor账号密码取值方式 
- 添加清理脚本执行权限 
- Feat: 修改jq下载逻辑  
- 修改jq使用写法 
- 修改jq使用语法 
- 测试corn job是否生效 
- 分支名测试 
- 分支名测试-添加执行权限 
- 分支名测试-修改分支名取值 
- 查看环境变量 
- 修改npx路径写法 
- 添加node版本切换逻辑 
- 修改提交语校验逻辑 
- 修改提交信息调用写法 
- 将提交语书写到文件中完成校验 
- 完善提交语校验逻辑 
- 提交多个议题校验功能 #260
- 提交删除所有无用的docker-tag脚本 
- 验证一个议题id关闭情形 #194
- 屏蔽ocr识别菜单
- 开发录音功能的前端页面以及实现静态交互功能
- 测试合并
- 优化
- 添加模型优化
- 模型部署/训练计算资源优化
- 模型广场优化
- K8s集群管理分页和搜索
- 部分模型url调整
- Ocr模型列表
- 用户选择 paddleocr 模型、GOT-ocr-2 模型进行部署页面开发 & 模型观察新增发布时间
- 删除多余代码
- 修改字段
- Ocr调试
- Orc体验部分功能
- OCR体验
- 体验中心模板修复
- 跨项目提交测试 
- 提交语添加到议题评论列表测试
- 修复录音转文字的功能 
- 处理部署失败问题 
- 验证提交语 
- 开发图片风格转换的前端页面以及联调
- 实现文生图接口返回不同状态下对应的前端交互 
- 放开开发中不需要加载模式的注释 
- 新增错误码 
- 【AI中台】图片风格转换，合成过程中，按钮未置灰 
- 图片风格转化的Form样式按照设计图优化 
- 处理预览失败的bug 
- 添加接口报错提示 
- 同步master分支代码 
- 提交在线分支名和提交信息，议题id校验功能 
- 修改husky/commit-msg所需文件生成路径 
- 调试合并时的提交信息 
- 测试合并消息提取 
- 合并推送消息提取测试 
- 添加本地分支存在检查逻辑 
- 合并时源分支提交信息提取测试
- 开发调试合并时从源分支获取提交消息 
- 合并信息解析调试 
- 调试密码获取不到问题 
- Commit-msg测试 
- Jq安装调试 
- Jq安装脚本未更新测试 
- Issue-check脚本更新测试 
- 对自动更新发生的合并信息不进行校验 
- 仅为触发合并 
- 同步代码 
- 修改git拉取远程分支代码写法 
- 修改密码格式 
- 修改commit-msg文件生成方式  
- 同步修改 
- 添加jq下载功能 
- 解决合并冲突 
- 仅为触发自动构建 
- 触发合并
- 触发流水线构建
- 测试获取代码是否正确
- 触发多分支构建
- 验证并发构建
- Dev push 测试1
- Merge dev测试1
- 修改sonar配置
- 修改Jenkins流水线配置
- 修复pnpm-lock报错
- 修改Jenkins中SonarQube Scan任务
- 提交调试通过的Jenkinsfile文件
- 获取提交信息测试
- 测试自动推送是否能通过分支名校验 
- 合并测试 

### 🐛 Bug Fixes

- 集市
- Bug
- 模型详情
- 模型列表接口
- 任务详情
- 添加 echarts
- 样式优化
- Echarts 修改
- ModelTarget
- Sols any
- Build
- 日志修改
- Log 触底
- 搜索
- Run_time
- 任务监控
- Echarts 实例问题
- Market search
- Empty
- 任务详情优化
- 测试关联pingcode #DATAFOX-210
- 详情优化
- 任务 isEmpty
- 修改样式 #DATAFOX-260
- 计算配置 lable
- 样式修改
- V-for 加key
- 详细信息 排版
- 查询重置分页参数
- 计算资源 修改
- 问题修复
- 部署详情
- 静态页面编写 #DATAFOX-326 DATAFOX-330 DATAFOX-329
- 部署
- 日志联调
- 页面优化
- 修改文件名
- 修复部署bug
- 删除测试代码
- 修改部署默认值
- Disabled
- Bug修复
- 列表图标点击状态
- LogStr 500
- 添加登录代码 #DATAFOX-488
- 优化 #DATAFOX-467
- 模型集成 #DATAFOX-521
- 监控联调 #DATAFOX-340
- Api路径修改
- 镜像模块 #DATAFOX-528
- 一些功能优化
- 删除测试数据
- 时间修复
- Canary
- 图表自适应
- 0403迭代  #DATAFOX-516 #DATAFOX-479 #DATAFOX-465 #DATAFOX-475
- 过长船舰删除功能
- 镜像滚动
- 部署名称
- 模型集成
- 镜像校验
- X-Trace-ID
- Llm-blender 修改
- Scrollbar
- 模型集成功能
- 模型集成修改
- P2l 生产地址修改
- 修改token名称
- 缓存用户信息
- 模型列表修改
- Ci 构建
- Env.d.ts
- 修复dev bug
- Md-editor-v3 注释
- 服务名称修改
- 部署列表修改
- 状态枚举
- 体验
- 体验中心
- 标签管理兼容小屏幕
- 生成服务名称逻辑优化
- 修改添加标签校验逻辑
- 融合模型
- 添加 canary uuid
- 文案优化 & 跑马灯
- 优化资源列表
- 我的服务添加disabled
- Jenkinsfile
- 构建
- 修复直播流被动静音的问题
- 优化样式
- 优化模型简介逻辑
- 修复bug
- 获取集群列表
- 修改路由名称
- 放开注释
- 修复flv播放问题
- 修改初始命令
- 删除debugger
- 修复回答样式异常问题
- 修改服务名称生成逻辑
- 会话标题长度限制
- 修复背景页面图片展示不全的问题
- 增加创建失败枚举
- 优化模型简介
- 创建失败可删除
- 加状态
- 修复音色模版展示不全的问题
- 修复直播muted值
- 修复tag的样式问题
- 修复音色播放问题
- 修复直播流bug
- 修复详情组件数据无法实时更新的问题。修复音色列表展示不全的问题
- 修复输入框按钮的样式问题。设置最小长度为1行，最多4行，超过4行设置滚动条，并且将滚动条隐藏
- 优化模型名称重复用户体验问题
- 修改request.ts
- 模型训练名称字段修改
- 模型训练部署弹窗自动打开
- 更新 pnmp-lock.yaml
- 修复体验跳转错误的bug
- 上线状态的模型禁止删除
- : 修改字段 & 屏蔽ocr入口
- 模型部署去除跑马灯
- 修复文本对话bug
- 数字人，获取录音权限，授权拒绝后的提示文案应改为：获取录音权限失败 
- 体验中心选中默认服务bug修复 
- 按钮disabled 

### 💼 Other

- 删除标签页icon
- Text husky

### 📚 Documentation


### 🎨 Styling

- 样式调整
- 改样式

### ⚙️ Miscellaneous Tasks

- *(release)* 0.0.1

<!-- generated by git-cliff -->
