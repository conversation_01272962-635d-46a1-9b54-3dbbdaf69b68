#全局变量，仓库名与账号信息根据实际更改
#test
variables:
  IMAGE_TAG: 'reg.shukeyun.com:9088/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME:$CI_PIPELINE_ID'
  Mirror_warehouse: 'reg.shukeyun.com:9088'
  ##这是之前创建的token
  TAG: $CI_PIPELINE_ID
  NS: prod
  KUBECONFIG: /etc/deploy/config
  PROJECT_NAME: $CI_PROJECT_NAME
  KUBECONFIG2PROD: /etc/deploy/kube2prod
  KUBECONFIG2CANARY: /etc/deploy/kube2canary

include:
  #- local: deep1.yaml
  - project: 'maintenance/ci'
    ref: test
    file: 'k8s_deploy.yaml'
cache:
  untracked: true
  paths:
    - .pnpm-store/
  policy: pull-push # 让 GitLab CI 复用缓存

image: node:latest
stages:
  - package
  - build
  - deploy

workflow:
  rules:
    - if: |
        $CI_COMMIT_BRANCH == "dev" ||
        $CI_COMMIT_BRANCH == "test" ||
        $CI_COMMIT_BRANCH == "canary" ||
        $CI_COMMIT_BRANCH == "master"

# 打包
package:
  stage: package
  image: node:20.17.0
  tags:
    - node-runner
  before_script:
    #    - yarn config set cache-folder .yarn-cache
    - echo "✅ 设置 npm 和 pnpm 镜像源"
    - npm config set registry "https://registry.npmmirror.com"
      # 避免每次安装 pnpm，先检查是否已安装
    - if ! command -v pnpm >/dev/null; then npm i -g pnpm; fi
    - pnpm config set store-dir .pnpm-store
    - pnpm config set registry "https://registry.npmmirror.com"
  script:
    #- pwd
    #- ls  vue.config.js
    #  - yarn install --cache-folder .yarn-cache
    - pnpm install
    - pnpm run build:$CI_COMMIT_BRANCH
    - tar czf $CI_PROJECT_NAME.tar.gz dist/
  artifacts:
    paths:
      - $CI_PROJECT_NAME.tar.gz
    expire_in: 1 week
