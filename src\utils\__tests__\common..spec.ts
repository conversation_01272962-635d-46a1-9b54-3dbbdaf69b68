import { describe, it, expect, vi } from 'vitest';
import { getRandomCharacters, copyText, formatTime } from '@/utils'; // 确保路径正确
import { message } from 'ant-design-vue';

// 由于 copyText 依赖 message.success，我们需要 mock message.success
vi.mock('ant-design-vue', () => ({
  message: {
    success: vi.fn(),
  },
}));

describe('getRandomCharacters', () => {
  it('should return a string of given length', () => {
    expect(getRandomCharacters(5)).toHaveLength(5);
    expect(getRandomCharacters(10)).toHaveLength(10);
  });

  it('should return only alphanumeric characters', () => {
    const result = getRandomCharacters(20);
    expect(result).toMatch(/^[A-Za-z0-9]+$/);
  });

  it('should return an empty string if length is 0', () => {
    expect(getRandomCharacters(0)).toBe('');
  });
});

describe('copyText', () => {
  it('should copy text to clipboard and show success message', () => {
    document.execCommand = vi.fn().mockReturnValue(true); // mock execCommand
    const mockInput = document.createElement('input');
    document.createElement = vi.fn().mockReturnValue(mockInput);

    copyText('Hello World');

    expect(document.body.contains(mockInput)).toBe(false); // 输入框应已删除
    expect(document.execCommand).toHaveBeenCalledWith('Copy'); // 检查是否调用了复制命令
    expect(message.success).toHaveBeenCalledWith('复制成功'); // 检查消息提示
  });
});

describe('formatTime', () => {
  it('should format seconds correctly', () => {
    expect(formatTime(0)).toBe('0秒');
    expect(formatTime(30)).toBe('30秒');
    expect(formatTime(60)).toBe('1分');
    expect(formatTime(3600)).toBe('1时');
    expect(formatTime(86400)).toBe('1天');
    expect(formatTime(90061)).toBe('1天1时1分1秒');
  });

  it('should return "--" for null or undefined input', () => {
    expect(formatTime(null as any)).toBe('--');
    expect(formatTime(undefined as any)).toBe('--');
  });
});
