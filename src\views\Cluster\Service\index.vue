<script setup lang="ts">
  import { ref, reactive, onMounted, onUnmounted, watch } from 'vue';
  import type { ColumnType, TablePaginationConfig } from 'ant-design-vue/es/table';
  import { RedoOutlined } from '@ant-design/icons-vue';
  import { useRoute, useRouter } from 'vue-router';
  import { fetchPodServiceList, fetchServiceYamlConfig, fetchServicesList } from '@/api/k8s';
  import type { IServiceItemResponse, IServiceList } from '@/interface/kubernetes';
  import { copyText, formatTime, getRandomCharacters, convertIsoTimeToLocalTime } from '@/utils/common';
  import { VAceEditor } from 'vue3-ace-editor';
  import 'ace-builds/src-noconflict/theme-chrome';
  import 'ace-builds/src-noconflict/theme-monokai.js';
  import 'ace-builds/src-noconflict/mode-python.js';
  import 'ace-builds/src-noconflict/mode-sh.js';
  import type { IAceOptions, IPage } from '@/interface';
  import type { IFormItem } from '@/components/CustomForm/index.vue';
  import { CustomForm } from '@/components';
  import yaml from 'js-yaml';
  import { TABLE_PAGINATION } from '@/json/common';

  const router = useRouter();
  const route = useRoute();
  const loading = ref(false);
  const pollingInterval = ref();
  const countInterval = ref();
  const count = ref(10);

  const pagination = reactive({ ...TABLE_PAGINATION });
  const pageParame: IPage = reactive({ page: 1, limit: 10 });
  const tableHeight = ref(0);
  interface IServiceSearchState {
    name?: string;
  }
  const DEFAULT_SEARCHSTATE: IServiceSearchState = {
    name: '',
  };
  const searchState: IServiceSearchState = reactive({ ...DEFAULT_SEARCHSTATE });
  const dataSource = reactive<IServiceItemResponse[]>([]);
  const YAMLState: { visible: boolean; value: string; title: string; spinning: boolean } = reactive({
    visible: false,
    title: 'Deployment',
    spinning: false,
    value: '',
  });
  const columns: ColumnType[] = [
    { title: '服务名称', dataIndex: 'name' },
    { title: '运行模式', dataIndex: 'mode' },
    { title: '类型', dataIndex: 'type', width: 120 },
    { title: 'Cluster IP', dataIndex: 'cluster_ip' },
    { title: '端口', dataIndex: 'ports' },
    { title: '创建时间', dataIndex: 'creation_time' },
    { title: '操作', dataIndex: 'operation', width: 120 },
  ];
  const formConfig: IFormItem[] = [
    {
      field: 'name',
      type: 'input',
      label: '服务名称',
      placeholder: '请输入',
    },
  ];

  const aceOptions: IAceOptions = {
    enableBasicAutocompletion: true,
    enableSnippets: true,
    enableLiveAutocompletion: true,
    tabSize: 4,
    enableEmmet: true,
    fontSize: 15,
    showPrintMargin: false, // 去除编辑器里的竖线
    highlightActiveLine: true,
    useWorker: true,
    readOnly: false,
  };
  const fetchList = async () => {
    loading.value = true;
    // const
    let data: IServiceItemResponse[] = [];
    if (route.name === 'service') {
      const params: IServiceList = { ...pageParame };
      Object.keys(searchState).forEach((key) => {
        if (searchState[key as keyof IServiceSearchState]) {
          params[key as keyof IServiceSearchState] = searchState[key as keyof IServiceSearchState];
        }
      });
      const res: { items: IServiceItemResponse[]; total: number } = await fetchServicesList(params);
      const { total, items } = res;
      data = items;
      Object.assign(pagination, { current: pageParame.page, total: total });
    }
    if (route.name === 'pod-detail') {
      data = await fetchPodServiceList(String(route.params.name));
    }
    dataSource.length = 0;
    dataSource.push(...data);
    loading.value = false;
  };
  const fetchYamlConfigReq = async (record: IServiceItemResponse) => {
    YAMLState.visible = true;
    YAMLState.spinning = true;
    const data = await fetchServiceYamlConfig(record.name);
    YAMLState.value = yaml.dump(data);
    YAMLState.spinning = false;
  };
  const jumpDetail = (record: IServiceItemResponse) => {
    router.push(`/cluster-manage/service/${record.name || record.service_name}`);
  };

  const toggleTable = (_pagination: TablePaginationConfig) => {
    const { current, pageSize } = _pagination;
    Object.assign(pagination, { current, pageSize });
    Object.assign(pageParame, { page: current, limit: pageSize });
    handleRedo();
  };
  const onFinish = (values: Record<string, string>) => {
    const searchConfig: { [key: string]: string } = {};
    for (const key in values) {
      searchConfig[key] = values[key];
    }
    Object.assign(searchState, { ...searchConfig });
    pageParame.page = 1;
    handleRedo();
  };

  const onRest = () => {
    Object.assign(searchState, { ...DEFAULT_SEARCHSTATE });
    handleRedo();
  };

  // 开始轮询
  const startPolling = async () => {
    // 先立即获取一次数据
    await fetchList();
    // 设置定时轮询
    pollingInterval.value = setInterval(async () => {
      await fetchList();
    }, 11000);
    countInterval.value = setInterval(() => {
      count.value > 0 ? count.value-- : (count.value = 10);
    }, 1000);
  };

  // 停止轮询
  const stopPolling = () => {
    if (pollingInterval.value) {
      clearInterval(pollingInterval.value);
      pollingInterval.value = null;
    }
    if (countInterval.value) {
      clearInterval(countInterval.value);
      countInterval.value = null;
    }
  };

  // 刷新
  const handleRedo = () => {
    stopPolling();
    startPolling();
    // 重置倒计时
    count.value = 10;
  };

  const getTableHeight = () => {
    const tableItem = document.querySelector('.container');
    tableHeight.value = (tableItem?.clientHeight as number) - 46;
  };

  onMounted(() => {
    startPolling();
    getTableHeight();
  });

  onUnmounted(() => {
    stopPolling();
  });

  watch(
    () => route.path,
    (path) => {
      if (path !== '/cluster-manage/service') {
        stopPolling();
      }
    },
    { deep: true },
  );
</script>

<template>
  <router-view v-if="route.name === 'service-detail' && route.params.name" />
  <a-spin v-else :spinning="loading">
    <CustomForm
      v-if="!route.params.name"
      :form-items="formConfig"
      @on-finish="onFinish"
      @on-rest="onRest"
      style="margin-bottom: 0"
    />
    <div class="refresh">
      <a-button style="padding: 0">
        <div class="refresh-count">{{ count }}s</div>
        <div class="refresh-icon" @click="handleRedo">
          <RedoOutlined />
        </div>
      </a-button>
    </div>
    <a-table
      :data-source="dataSource"
      :columns="columns"
      :pagination="pagination"
      :scroll="{ y: tableHeight - 170 }"
      @change="toggleTable"
    >
      <template #bodyCell="{ column, text, record }">
        <div v-if="column.dataIndex === 'operation'">
          <a @click="fetchYamlConfigReq(record)">YAML</a>
        </div>
        <div v-else-if="['creation_time', 'start_time'].includes(column.dataIndex)">
          {{ convertIsoTimeToLocalTime(text) }}
        </div>
        <div v-else-if="column.dataIndex === 'name'">
          <a class="table-a-btn" @click="jumpDetail(record)">{{ text || record.service_name }}</a>
        </div>
        <div v-else-if="column.dataIndex === 'mode'">
          <a-tag>{{ (record.name || record.service_name).startsWith('ai-platform') ? '中台托管' : '其他' }}</a-tag>
        </div>
        <div v-else-if="column.dataIndex === 'runtime_seconds'">
          {{ formatTime(Math.floor(text)) }}
        </div>
        <div v-else-if="column.dataIndex === 'ports'">
          <div v-for="port in record.ports" :key="port + getRandomCharacters()">
            <span class="bg-#234883 text-#fff p-x-5px rounded-l-[10px]">{{ record.ports[0].port }}</span>
            <span class="bg-#ff8807 text-#fff p-x-5px">{{ record.ports[0].target_port }}</span>
            <span class="bg-#657fa8 text-#fff p-x-5px rounded-r-[10px]">{{ record.ports[0].protocol }}</span>
          </div>
        </div>
        <div v-else>{{ text }}</div>
      </template>
    </a-table>
  </a-spin>
  <a-modal
    v-model:open="YAMLState.visible"
    :title="YAMLState.title"
    width="60%"
    centered
    :footer="false"
    @cancel="
      YAMLState.visible = false;
      YAMLState.value = '';
    "
  >
    <div class="h-800px overflow-scroll">
      <a-spin :spinning="YAMLState.spinning">
        <div class="w-100% h-32px flex flex-justify-end">
          <a @click="copyText(YAMLState.value)"> 复制到剪切板 </a>
        </div>
        <v-ace-editor
          v-model:value="YAMLState.value"
          lang="sh"
          theme="monokai"
          :options="aceOptions"
          style="height: calc(100% - 32px); border: 1px solid #dbd3d3"
        />
      </a-spin>
    </div>
  </a-modal>
</template>

<style scoped lang="less">
  @import url('../index.less');
</style>
