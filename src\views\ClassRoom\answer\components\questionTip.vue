<script setup lang="ts">
  import { useAnswer } from '@/stores/anster';

  interface IProps {
    list: any[];
    canSelected: boolean;
  }

  const props = defineProps<IProps>();

  const { jumpAnswer, state, setInPreBack } = useAnswer();

  function handleClick(idx: number) {
    if (!props.canSelected) return;
    // console.log(idx, props.list.length, state.currentIndex);
    if (idx + 1 === props.list.length) setInPreBack(false);
    else setInPreBack(true);
    if (state.currentIndex !== idx) jumpAnswer(idx);
  }
</script>

<template>
  <div class="w-full h-full flex items-center">
    <div class="question-list">
      <div
        v-for="(item, index) in props.list"
        :key="index"
        class="question-item flex justify-center items-center"
        :class="{ canSelected: canSelected, active: index === state.currentIndex }"
        @click="handleClick(+index)"
      >
        <div class="tip" :class="state.questionList[+index]?.id ? (item.isRight ? 'right' : 'error') : ''">
          <!-- ? true : index === props.list.length - 1 -->
          <!-- {{ item === 1 ? '√' : 'x' }} -->
        </div>
        {{ index + 1 }}
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
  .question-list {
    display: flex;
    // grid-template-columns: 40px;
    grid-gap: 24px;
    .question-item {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: #f0f1f2;
      overflow: hidden;
      position: relative;
      cursor: pointer;
      box-sizing: content-box;
      border: 2px solid rgba(255, 255, 255, 0);
      &.canSelected {
        &:hover {
          border-color: #1777ff;
        }
        &.active {
          border-color: #1777ff;
        }
      }
      .tip {
        content: '';
        display: flex;
        justify-content: center;
        width: 100%;
        height: 100%;
        border-radius: 50%;
        position: absolute;
        transform: translateY(29px);
        line-height: 0.7;
        color: #fff;
        overflow: hidden;
        &.right {
          background-color: #4ecc10;
          &::before {
            content: '';
            position: absolute;
            width: 9px;
            height: 8px;

            left: 50%;
            top: 2px;
            transform: translateX(-50%);
            background: url('@/assets/image/base/answer/tip-icon1.webp');
            background-size: cover;
          }
        }
        &.error {
          background-color: #ff3c3c;
          &::before {
            content: '';
            position: absolute;
            width: 9px;
            height: 8px;

            left: 50%;
            top: 2px;
            transform: translateX(-50%);
            background: url('@/assets/image/base/answer/tip-icon2.webp');
            background-size: cover;
          }
        }
      }
    }
  }
</style>
