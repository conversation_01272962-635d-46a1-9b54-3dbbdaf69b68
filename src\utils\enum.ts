import type { DataItem } from "@/interface/model";

export const serviceStatus: Record<string, { label: string; color: string }> = {
  archived: { label: '已发布', color: 'green' },
  draft: { label: '未发布', color: 'orange' },
};

export const sourceEnum: DataItem[] = [
  { key: '1', GPU: '1 * NVIDIA A100', CPU: 'Dynamic', memory: 'Dynamic', disabled: false },
  { key: '2', GPU: '1 * NVIDIA A100', CPU: '4 vCPU', memory: '32 GiB', disabled: false },
  { key: '3', GPU: '1 * NVIDIA A100', CPU: '8 vCPU', memory: '32 GiB', disabled: true },
  { key: '4', GPU: '1 * NVIDIA A100', CPU: '8 vCPU', memory: '64 GiB', disabled: true },
  { key: '5', GPU: '1 * NVIDIA A100', CPU: '16 vCPU', memory: '64 GiB', disabled: true },
  { key: '6', GPU: '1 * NVIDIA A100', CPU: '16 vCPU', memory: '128 GiB', disabled: true },
  { key: '7', GPU: '1 * NVIDIA A100', CPU: '32 vCPU', memory: '128 GiB', disabled: true },
  { key: '8', GPU: '1 * NVIDIA A100', CPU: '32 vCPU', memory: '256 GiB', disabled: true },
  { key: '9', GPU: '1 * NVIDIA A100', CPU: '64 vCPU', memory: '256 GiB', disabled: true },
  { key: '10', GPU: '1 * NVIDIA A100', CPU: '128 vCPU', memory: '256 GiB', disabled: true },
  { key: '11', GPU: '1 * NVIDIA A100', CPU: '128 vCPU', memory: '512 GiB', disabled: true },
  { key: '12', GPU: '1 * NVIDIA A100', CPU: '128 vCPU', memory: '1 T', disabled: true },
];


export const modelFunStatus: Record<string, { label: string; color: string }> = {
  deploy: { label: '部署', color: 'green' },
  train: { label: '训练', color: 'orange' },
  experience: { label: '体验', color: 'orange' },
};

export const modelSource: Record<string, { label: string }> = {
  third: { label: '第三方' },
  hqsk: { label: '环球数科' },
};

export const administratorIds: string[] = [
  '535327271150555136',
  '535327209028718592',
  '535327077805723648',
  '535326866219864064',
];



export const baseStones = [
  { value: 'xingxing', label: '女声', gender: 'woman' },
  { value: '神里绫人', label: '男声', gender: 'man' },
];
export const toneOptions = [
  ...baseStones,
  { value: '可莉', label: '小孩' },
  { value: '琴', label: '知性' },
  { value: '白术', label: '男主播' },
  { value: '凯瑟琳', label: '女主播' },
  { value: 'sichuan_man', label: '四川-男声' },
  { value: 'sichuan_woman', label: '四川-女声' },
  { value: 'zihan', label: '克隆音色' },
  { value: 'foreign_english_woman', label: '英文-女声' },
  { value: 'foreign_english_woman_v1', label: '英文-女声-v1' },
  { value: 'foreign_spanish_woman', label: '西班牙-女声' },
  { value: 'foreign_french_woman', label: '法国-女声' },
  { value: 'foreign_korean_woman', label: '朝鲜-女声' },
  { value: 'foreign_japanese_woman', label: '日本-女声' },
  { value: 'foreign_chinese_woman', label: '中文-女声' },
];
export const HumanType: { [key: string]: string } = {
  '2d': 'avatar01_2d',
  '3d': 'avatar01_3d',
};
