import type { IPagination } from '.';

export interface IDeployListProps extends IPagination {
  service_name?: string;
}

export interface ICreateDeployTaskProps {
  deploy_method: string;
  model_id: string;
  service_name: string;
  resource: Record<string, string>;
  introduction: string;
  container_count: number;
  init_command: string;
  gpu_number_list: number[];
}

// export interface IBasicCreateProps extends ICreateDeployTaskProps {
//   model_id: string;
// }

// export interface ITrainCreateProps extends ICreateDeployTaskProps {
//   train_id: string;
// }

export interface IBodyProps {
  model?: string;
  messages: { role: string; content: string }[];
  max_tokens: number;
}
export interface IDeployCallProps {
  model_id: string;
  body: IBodyProps;
  headers: Record<string, string>;
}

export interface IFetchDeployDetail {
  model_id: string;
  train_id: string;
  deploy_method: string;
  service_name: string;
  resource: string[];
  introduction: string;
  container_count: number;
  container_id: string;
  init_command: string;
  port: string;
  status: string;
  url: string;
  creator_id: string;
  id: string;
  updated_at: string;
  created_at: string;
  deleted_at: null;
  model_name: string;
  by_trained_model: boolean;
  source_category: string;
  model_category: string;
  run_command: string[];
  docker_image: string;
  [key: string]: unknown; // 允许额外的动态键值对
}
