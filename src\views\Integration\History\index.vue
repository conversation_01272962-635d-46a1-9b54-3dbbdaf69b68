<!--
 * @Author: dengfusheng 
 * @Date: 2025-03-25 17:34:32
 * @LastEditTime: 2025-04-18 14:49:08
 * @LastEditors: <EMAIL>
 * @FilePath: \ai-platform-frontend\src\views\Integration\History\index.vue
 * @Description:  答策中枢  历史记录
-->

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, nextTick, computed } from 'vue';
import { CustomForm } from '@/components';
import type { IFormItem, IPagination } from '@/interface';
import { TABLE_PAGINATION } from '@/json/common';
import type { ColumnType, TablePaginationConfig } from 'ant-design-vue/es/table/interface';
import { useRoute, useRouter } from 'vue-router';
import { fetchDockerList } from '@/api/docker';
import { trainTypes, trainMethods } from '@/views/Model/Train/index.ts';
import { message } from 'ant-design-vue';
import { formatTime, getLocalItem, setLocalItem } from '@/utils/common';
import { deleteTask } from '@/api/model';
import dayjs from 'dayjs';

const route = useRoute();
const router = useRouter();
const DEFAULT_SEARCHSTATE = {
  image_name: undefined,
};
const searchState = reactive({ ...DEFAULT_SEARCHSTATE });
const formConfig: IFormItem[] = [
  // {
  //   field: 'image_name',
  //   type: 'input',
  //   label: '',
  //   placeholder: '请输入',
  // },
];
const columns: ColumnType[] = [
  { title: '任务ID', dataIndex: 'id', fixed: 'left' },
  { title: Number(route.query.ind) ? '模型名称' : '较优模型', dataIndex: 'model_name', ellipsis: true },
  { title: '最终结果', dataIndex: 'final', ellipsis: true },
  { title: '问题', dataIndex: 'question', ellipsis: true },
  { title: '初始答案', dataIndex: 'init_answer', ellipsis: true },
  { title: '创建时间', dataIndex: 'created_at', sorter: (a: any, b: any) => dayjs(a.created_at).unix() - dayjs(b.created_at).unix(), defaultSortOrder: 'descend' },
  { title: '操作', dataIndex: 'operation', width: 240, fixed: 'right' },
];

const pagination = reactive({ ...TABLE_PAGINATION });
const page: any = reactive({ page: 1, limit: 10 });
const loading = ref(false);
const dataSource: any = reactive([]);
const tableHeight = ref(0);

const getTaskListReq = async () => {
  loading.value = true;
  if (getLocalItem('integration')) {
    const { id, ind } = route.query as any;
    const list = JSON.parse(getLocalItem('integration'));
    dataSource.length = 0;
    if (list[ind].length) {
      dataSource.push(...list[ind].map(({ req, res, created_at }: any) => {
        if (!Number(ind)) return {
          id: res.id,
          model_name: res.chosen_model,
          final: res.answer,
          question: req.prompt,
          init_answer: res.answer,
          created_at,
          checked: res.id == id
        };
        else return {
          id: res.id,
          model_name: req.models.join('   '),
          final: res.fused_result[0],
          question: req.contents.join(';'),
          init_answer: res.candidates?.[0]?.join('<br><br>'),
          created_at,
          checked: res.id == id
        }
      }));
    }
    Object.assign(pagination, { current: page.page, total: list[ind].length });
  }
  loading.value = false;
};

const deleteItem = async (id: string) => {
  const { ind } = route.query as any;
  const list = JSON.parse(getLocalItem('integration'));
  list[ind].splice(list[ind].findIndex((e: any) => e.res.id == id), 1)
  setLocalItem('integration', list)
  message.success('删除成功');
  getTaskListReq();
};

onMounted(async () => {
  getTaskListReq();
  await nextTick();
  getTableHeight()
});

const rowClick = (e: any) => {
  return {
    onclick: (ev: any) => {
      const { id, ind } = route.query as any;
      router.push({
        path: '/integration',
        query: { ind, ...route.query, id: e.id },
      });
    }
  }
}

const getTableHeight = () => {
  const tableItem = document.querySelector('.container');
  tableHeight.value = tableItem?.clientHeight as number;
};

const placement = computed(() => (index: any) => {
  const { total, pageSize, current } = pagination
  const row = total <= pageSize ? total : total - pageSize * (current - 1)
  return (index > 2 && !(index < Math.floor(row / 2))) ? 'top' : 'bottom'
})

const toggleTable = (_pagination: TablePaginationConfig) => {
  let { current, pageSize } = _pagination;
  console.log(current, pageSize);
  Object.assign(pagination, { current, pageSize });
  Object.assign(page, { page_index: current, page_size: pageSize });
};

onUnmounted(() => {

});
</script>

<template>
  <!-- <CustomForm :form-items="formConfig" @onFinish="onFinish" @onRest="onRest" /> -->
  <a-table :dataSource="dataSource" :columns="columns" :pagination="pagination" :customRow='rowClick'
    @change="toggleTable" :row-class-name="(_record: any, index: number) => (_record.checked ? 'checked' : null)"
    :loading="loading" :scroll="{ y: tableHeight - 200 }">
    <template #bodyCell="{ column, record, text, index }">
      <div class="operation-box" v-if="column.dataIndex === 'operation'">
        <a-popconfirm title="确定删除这条记录吗?" @confirm="deleteItem(record.id)">
          <a class="del-btn" @click.stop>删除</a>
        </a-popconfirm>
      </div>
      <div style="" v-else-if="['final', 'model_name', 'question', 'init_answer'].includes(column.dataIndex)">
        <a-tooltip arrow-point-at-center :placement="placement(index)" :overlayInnerStyle="{
          WebkitBoxOrient: 'vertical',
          display: '-webkit-box',
          maxHeight: '300px',
          overflowY: 'scroll',
          padding: '10px 5px',
          // scrollbarWidth: 'none'
        }">
          <template #title>
            <div v-html="text"></div>
          </template>
          <div>{{ text }}</div>
        </a-tooltip>
      </div>
      <div v-else>{{ text }}</div>
    </template>
  </a-table>
</template>


<style lang="css" scoped>
:deep(.ant-table-cell-ellipsis  div) {
  overflow: hidden;
  text-overflow: ellipsis;
}

:deep(.checked) td {
  background-color: #6f84ee70;
}
</style>
