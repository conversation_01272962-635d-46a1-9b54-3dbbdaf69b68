import { message } from 'ant-design-vue';

export const videoManager = {
  play: (videoDom: any, callback?: Function) => {
    if (videoDom) {
      videoDom.play &&
        videoDom
          .play()
          .then(() => {
            callback && callback();
          })
          .catch((err: any) => {
            message.error(err.message);
          });
    }
  },
  stop: (videoDom: any) => {
    if (videoDom) {
      videoDom.pause && videoDom.pause();
    }
  },
  hidden: (videoDom: any, isTimeout: boolean = true) => {
    if (videoDom) {
      if (isTimeout) {
        setTimeout(() => {
          videoDom.style.display = 'none';
        }, 300);
      } else {
        videoDom.style.display = 'none';
      }
    }
  },
  show: (videoDom: any) => {
    if (videoDom) {
      videoDom.style.display = 'block';
    }
  },
  init: (videoDom: any) => {
    if (videoDom) {
      // 视频回到第一帧
      videoDom.currentTime = 0;
    }
  },
  setSrc: (videoDom: any, src: string) => {
    if (videoDom) {
      videoDom.src = src;
    }
  },
};
