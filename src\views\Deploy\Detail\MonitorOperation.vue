<!--
 * @Author: dengfusheng 
 * @Date: 2025-03-13 14:16:03
 * @LastEditTime: 2025-04-16 13:53:05
 * @LastEditors: <EMAIL>
 * @FilePath: \ai-platform-frontend\src\views\Deploy\Detail\MonitorOperation.vue
 * @Description: 模型运行监控
-->

<script setup lang="ts">
  import dayjs from 'dayjs';
  import { init } from 'echarts';
  import { CustomForm } from '@/components';
  import { onMounted, reactive, ref, watch } from 'vue';
  import { getSystemResource } from '@/api/model';
  import { useRoute } from 'vue-router';
  import { deployMonitor } from '@/api/deploy';
  import { QuestionCircleFilled } from '@ant-design/icons-vue';

  const props = withDefaults(defineProps<{ created: string }>(), {
    created: dayjs(new Date()).format('YYYY-MM-DD') + ' 00:00:00',
  });

  const route = useRoute();
  const echarts: any = ref(null);
  const echartsInstance: any = [];
  const state = reactive<any>({
    spinning: false,
    options: [
      {
        titleText: '服务调用token数',
        tip: '调用的token数',
        yAxisUnit: ['个', '个'],
        seriesKey: [
          { name: '输入token数', val: 'total_prompt_tokens' },
          { name: '输出token数', val: 'total_completion_tokens' },
        ],
        data: [1, 4, 2, 2.2, 3.5, 3.3],
      },
      {
        titleText: '响应时间',
        tip: '单位时间内成功请求的响应时间平均值（单位：秒）',
        yAxisUnit: 's',
        seriesKey: [{ name: 'AVG', val: 'avg_response_time' }],
        data: [112, 80, 90, 110, 130, 90],
      },
      {
        titleText: '调用量',
        tip: '服务调用总次数',
        yAxisUnit: ['次', '次'],
        seriesKey: [
          { name: '调用成功', val: 'success_count' },
          { name: '调用失败', val: 'failed_count' },
          // { name: '请求token数', val: 'scuss' },
        ],
        data: [8, 9, 2, 15, 10, 21, 11],
      },
      {
        titleText: '调用失败率',
        tip: '调用失败次数/调用总次数',
        yAxisUnit: '%',
        seriesKey: [{ name: '失败率', val: 'failure_rate' }],
        data: [11, 10, 7, 8, 5, 3],
      },
    ],
    search: '',
    timeFilter: 'second', //  second 按秒  minute 按分钟 / hour 按小时
    datas: [],
    isEmpty: false,
    alert: true,
    oldlen: 0,
    customFormValue: { timeFilter: 'second', search: [dayjs(props.created), dayjs(new Date())] },
  });

  const formConfig: any = [
    {
      field: 'timeFilter',
      type: 'select',
      label: '时间精度',
      placeholder: '请输入关键字',
      options: [
        { label: '按时', value: 'hour' },
        { label: '按分钟', value: 'minute' },
        { label: '按秒', value: 'second' },
      ],
    },
    {
      field: 'search',
      type: 'dateRange',
      label: '时间段',
      placeholder: ['请选择', '请选择'],
      options: [
        { label: '按日', value: 'd' },
        { label: '按时', value: 'h' },
        { label: '按分钟', value: 'm' },
      ],
    },
  ];

  const getInfoList = async (
    start_time: string = dayjs(props.created).format('YYYY-MM-DD') + ' 00:00:00',
    end_time: string = dayjs(new Date()).format('YYYY-MM-DD') + ' 23:59:59',
    time_unit: string = 'second',
  ) => {
    const { id } = route.query;
    if (state.datas.length) state.oldlen = state.datas.length;
    const list = await deployMonitor(id as string, { start_at: start_time, end_at: end_time, time_unit });
    // const list = []
    if (list) state.datas = list;
    state.isEmpty = state.oldlen == 0 && !state.datas.length;

    if (echartsInstance.length) {
      const len = state.datas.length;
      echartsInstance.map((e: any, i: number) => {
        const option: any = {};
        if (len) {
          // option.series = {
          //   data: state.datas.map((d: any) => {
          //     if (state.options[i].seriesKey) return d[state.options[i].seriesKey];
          //     else return 0;
          //   }),
          //   type: 'line',
          // };
          option.series = state.options[i].seriesKey.map((e: any, i: number) => {
            return {
              name: e.name,
              data: state.datas.map((n: any) => n[e.val]),
              type: 'line',
            };
          });
          option.xAxis = {
            type: 'category',
            data: state.datas.map((e: any) => e.time),
          };
        } else {
          option.series = state.options[i].seriesKey.map((e: any, i: number) => {
            return {
              name: e.name,
              data: Array.from(Array(state.oldlen), (_, i) => 0),
              type: 'line',
            };
          });
          // option.series = {
          //   data: Array.from(Array(oldlen), (_, i) => 0),
          //   type: 'line',
          // };
        }
        e.setOption(option);
        e.resize();
      });
    } else if (!state.isEmpty) initechart();
  };

  onMounted(async () => {
    state.spinning = true;
    await getInfoList();
    state.spinning = false;
    if (!state.datas.length) state.isEmpty = true;
    if (echarts.value && !state.isEmpty) {
      initechart();
    }
  });

  window.addEventListener('resize', () => {
    if (echartsInstance) echartsInstance.map((e: any) => e?.resize());
  });

  const initechart = () => {
    echarts.value.map((e: any, i: number) => {
      const { titleText, seriesKey, yAxisUnit, data } = state.options[i];
      const echartInstance = init(e);
      echartInstance.setOption({
        xAxis: {
          type: 'category',
          // data: Array.from(Array(6), (_, i) =>
          //   dayjs(new Date())
          //     .subtract(i + 1, 'hour')
          //     .format('YYYY-MM-DD hh:mm:ss'),
          // ).reverse(),
          data: state.datas.map((e: any) => e.time),
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            formatter: (value: any) => `${value} ${Array.isArray(yAxisUnit) ? '' : yAxisUnit}`,
          },
        },
        tooltip: {
          show: true,
          trigger: 'axis', // 或 'item'
          formatter: (params: any) => {
            let content = '',
              str = '';
            params.forEach((item: any) => {
              str += `<div>${item?.seriesName ? item?.seriesName : '值'}: ${item.data} ${Array.isArray(yAxisUnit) ? yAxisUnit[item.seriesIndex] : yAxisUnit}</div>`;
            });
            content = `<div>时间: ${params?.[0].name || ''}<br/>${str}</div>`;
            return content;
          },
        },
        legend: {
          data: seriesKey.map((e: any) => e.name),
        },
        series: seriesKey.map((e: any, i: number) => {
          return {
            name: e.name,
            // data: data.map((e: any) => e + i),
            data: state.datas.map((n: any) => n[e.val]),
            type: 'line',
          };
        }),
      });
      echartsInstance.push(echartInstance);
    });
  };

  const onFinish = async ({ search, timeFilter = 'second' }: any) => {
    if (search)
      await getInfoList(
        dayjs(search[0]).format('YYYY-MM-DD') + ' 00:00:00',
        dayjs(search[1]).format('YYYY-MM-DD') + ' 23:59:59',
        timeFilter,
      );
  };

  const selectChang = (e: any) => {
    state.customFormValue = {
      timeFilter: e,
      // search: [dayjs(props.created), dayjs(new Date())],
      search:
        e != 'second'
          ? [dayjs(new Date()).subtract(7, 'day'), dayjs(new Date())]
          : [dayjs(props.created), dayjs(new Date())],
    };
  };

  watch(
    () => route.query.tab,
    async (val: any) => {
      if (val == 'monitor') await getInfoList();
    },
    { deep: true },
  );
</script>

<template>
  <a-spin :spinning="state.spinning">
    <div class="monitor-operation">
      <CustomForm
        :form-items="formConfig"
        @onFinish="onFinish"
        :universal="{ funName: ['change'], fun: [selectChang] }"
        :value="state.customFormValue"
        @onRest="onFinish({ ...state.customFormValue })"
      />
      <div class="overflow-scroll">
        <div v-for="(n, i) in state.options" :key="i">
          <div style="display: flex; align-items: center">
            <span style="margin-left: 10px; font-size: 18px; font-weight: 600">{{ n.titleText }}</span>
            <a-tooltip>
              <template #title>
                <div>{{ n.tip }}</div>
              </template>
              <question-circle-filled class="span-mg-left" style="margin-left: 8px" />
            </a-tooltip>
          </div>
          <div ref="echarts" class="echarts">
            <a-empty v-if="state.isEmpty" style="margin-top: 15%" />
          </div>
        </div>
      </div>
    </div>
  </a-spin>
</template>

<style lang="less" scoped>
  .monitor-operation {
    display: flex;
    flex-direction: column;
    height: 100%;

    > div:nth-child(1) {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 20px;
    }

    > div:nth-child(2) {
      display: flex;
      flex-wrap: wrap;

      > div {
        width: calc((100% - 20px) / 2);
        margin: 0 0 20px;
        border: 1px solid rgb(0 0 0 / 20%);
      }

      > div:nth-child(2n + 1) {
        margin-right: 20px;
      }

      .echarts {
        height: 350px;
        padding-top: 15px;
        margin-bottom: 20px;
      }

      .echarts:nth-child(2n) {
        margin-left: 20px;
      }
    }
  }
</style>
