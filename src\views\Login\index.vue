<script setup lang="ts">
  import { detail, setCookies } from '@/api/login';
  import { getHashParams, getLocalItem, setLocalItem } from '@/utils/common';
  import { JumpToCasLogin } from '@/utils/jumpToCas';
  import { onMounted } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { useAuth } from '@/stores/auth';
  const route = useRoute();
  const router = useRouter();
  const { state, setUser } = useAuth();

  // 登录逻辑
  const login = async () => {
    const { tk, path } = getHashParams();
    const redirect = '/';
    const token = getLocalItem('HQSK_AI_PLATFORM_FRONTEND_TOKEN');
    if (tk) {
      if (path) {
        // 本地开发：由于接 cas 会跳转到设置的回调地址，需要转到本地地址，比如 http://localhost:3000/
        console.log('path', path);
        let url = `${decodeURIComponent(path as string)}#/?tk=${tk}`;
        location.href = url;
      }
      const res: any = await setCookies(tk as string);
      setLocalItem('HQSK_AI_PLATFORM_FRONTEND_TOKEN', res.token);
      await getDetail();
      router.push('/model');
    }
    // 没有tk 但缓存里有token
    if (token) {
      router.push('/model');
    }
    // 什么都没有直接跳转cas登录
    if (!(tk || token)) {
      const redirectTemp = redirect ? `${redirect}` : '';
      JumpToCasLogin(redirectTemp);
    }
  };

  const getDetail = async () => {
    if (!(state && state.user)) {
      const token = getLocalItem('HQSK_AI_PLATFORM_FRONTEND_TOKEN');
      const data = await detail(token);
      setLocalItem('HQSK_AI_PLATFORM_FRONTEND_USERINFO', data);
      setUser(state, data);
    }
  };
  onMounted(async () => {
    await login();
    // await getDetail();
  });
</script>

<template></template>

<!-- <style scoped lang='less'></style> -->
