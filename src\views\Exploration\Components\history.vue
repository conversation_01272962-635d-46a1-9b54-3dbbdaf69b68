<script setup lang="ts">
  import { ref, reactive, watch } from 'vue';
  import { fetchHistoryList } from '@/api/exploration';
  interface IProps {
    id: string;
    visible: boolean;
    type: 'add' | 'change';
  }
  const props = withDefaults(defineProps<IProps>(), {
    type: 'add',
  });

  const emits = defineEmits<{
    (event: 'cancel'): void;
    (event: 'select', id: string, title: string): void;
  }>();
  const spinning = ref(false);
  const handleCancel = () => {
    emits('cancel');
  };
  interface IHistoryList {
    dialog_id: string;
    title: string;
  }
  const state = reactive<{ list: IHistoryList[]; activeKey: string }>({
    list: [],
    activeKey: '',
  });

  const fetchHistoryListReq = async () => {
    spinning.value = true;
    const data = await fetchHistoryList(props.id);
    state.list = data;
    spinning.value = false;
  };

  const handleJump = (item: IHistoryList) => {
    state.activeKey = item.dialog_id;
    emits('select', item.dialog_id, item.title);
  };
  watch(
    () => props,
    () => {
      const { id, visible } = props;
      if (id && visible) {
        fetchHistoryListReq();
      }
    },
    { deep: true, immediate: true },
  );
</script>

<template>
  <a-drawer
    style="background-color: #f6f6f6"
    :bodyStyle="{ padding: '5px' }"
    :width="400"
    title="历史记录"
    placement="right"
    :open="props.visible"
    @close="handleCancel"
  >
    <a-spin :spinning="spinning" tip="数据加载中,请稍等...">
      <div v-if="state.list.length" class="h-100% overflow-scroll">
        <div
          class="item"
          :class="{ actice: state.activeKey === item.dialog_id }"
          v-for="item in state.list"
          :key="item.dialog_id"
          @click="handleJump(item)"
        >
          <span v-ellipse-tooltip.top>{{ item.title }}</span>
        </div>
      </div>
      <div v-else style="display: flex; align-items: center; justify-content: center; height: 100%">
        <a-empty description="暂无历史记录"></a-empty>
      </div>
    </a-spin>
  </a-drawer>
</template>

<style scoped lang="less">
  .item {
    display: flex;
    align-items: center;
    width: 100%;
    height: 45px;
    padding: 0 15px;
    cursor: pointer;
    border-radius: 6px;

    &:active {
      background-color: #e6f4ff;
    }

    &:hover {
      background-color: #eee;
    }
  }

  .actice {
    color: #1680ff;
    background-color: #e6f4ff;

    &:hover {
      background-color: #e6f4ff;
    }
  }
</style>
