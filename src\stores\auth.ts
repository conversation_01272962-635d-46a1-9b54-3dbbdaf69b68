import { ref, computed } from 'vue';
import { defineStore } from 'pinia';
import { setLocalItem } from '@/utils/common';

interface IAuth {
  token: string;
  user: any
}

export const useAuth = defineStore('auth', () => {
  const state = ref<IAuth>({
    token: '',
    user: null,
  })
  const setLogin = (state: IAuth, data: { isLogin: boolean; token: string }) => {
    if (data.isLogin) {
      state.token = data.token;
      setLocalItem('HQSK_AI_PLATFORM_FRONTEND_TOKEN', data.token);
    } else {
      state.token = '';
      state.user = {};
      setLocalItem('HQSK_AI_PLATFORM_FRONTEND_TOKEN', '');
      // this.commit('auth/setWorkspaceId' as any, null);
    }
  };
  const setUser = (state: IAuth, user: any) => {
    state.user = user;
    setLocalItem('HQSK_AI_PLATFORM_FRONTEND_USERINFO', user);
  }
  return { state, setLogin, setUser };
});
