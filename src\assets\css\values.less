@test-color: red;
@bg-color: #15284f;
@info-color: #3487a5;
@heavy-primary-color: #02f9ff;
@primary-color: #2ac0f5;
@blue-color: #25a8dc;
@line-color: #0b346e;
@success-color: #19be6b;
@error-color: #fb6b69;
@green-color: #b8e986;
@warning-color: #cdae7b;
@alert-color: #d8ff00;
@disabled-color: #c5c8ce;
@gray-color: #808695;
@light-color: #ddd;
@screen-bg-color: #f2f2f2;
@screen-border-color: #efeded;
@border-select-color: #ffff00;
@border-select-color-2: #ff5b00;
@icon-blue-color: #1890ff;
@icon-red-color: #ff4d4f;
@border-color: #f0f0f0;

.flex-mode(@direction:row, @justifyContent: center, @alignItems: center) {
  display: flex;
  flex-direction: @direction;
  align-items: @alignItems;
  justify-content: @justifyContent;
}

.text-over(@column:1) {
  display: -webkit-box;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: @column;
  word-break: break-all;
  -webkit-box-orient: vertical;
}

.flex-wrap (@dir:row) {
  display: flex;
  flex-direction: @dir;
}

.full {
  width: 100%;
  height: 100%;
}

.flex {
  display: flex;
}
.full(@position: static) {
  position: @position;
  width: 100%;
  height: 100%;
}

html {
  // --14px: 0.875rem;
  // --15px: 0.9375rem;
  // --16px: 1rem;
  // --17px: 1.0625rem;
  // --18px: 1.125rem;
  // --19px: 1.1875rem;
  // --20px: 1.25rem;
  // --21px: 1.3125rem;
}

.border-gradual(@wid: 1px) {
  border: @wid solid #096cd4;
  border-image: linear-gradient(to right, #0d57a5, #23b7cb) 1 10;
}

.background-blues {
  background-image: linear-gradient(to right, #4e38ff, #02e0ff);
}

.background-yellows {
  background-image: linear-gradient(to right, #ff5b00, #f7d235);
}

// 优化滚动条
.overflow-scroll {
  &::-webkit-scrollbar-track-piece {
    border-radius: 0;
  }

  &::-webkit-scrollbar {
    width: 5px;
    height: 6px;
  }

  &::-webkit-scrollbar-thumb {
    height: 50px;
    outline-offset: -2px;
    background-color: rgb(144 147 153 / 30%);
    border-radius: 6px;
    -moz-opacity: 0.5;
    -khtml-opacity: 0.5;
    opacity: 0.5;
  }

  &::-webkit-scrollbar-thumb:hover {
    height: 50px;
    background-color: rgb(144 147 153 / 50%);
    border-radius: 6px;
  }
}
