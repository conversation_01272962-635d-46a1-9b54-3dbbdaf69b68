import { message } from 'ant-design-vue';
export function playAudio(audioUrl: string): Promise<void> {
  return new Promise((resolve, reject) => {
    const audio = new Audio(audioUrl);
    audio.onended = () => {
      console.log('音频播放完成');
      resolve();
    };

    audio.onerror = (error) => {
      console.error('播放音频失败：', error);
      reject(error);
    };

    audio
      .play()
      .then(() => {
        console.log('音频播放中');
      })
      .catch((error) => {
        console.error('播放音频失败：', error);
        reject(error);
      });
  });
}

export const getVideoById = (id: string) => {
  return document.getElementById(id) as HTMLVideoElement;
};

export const switchVideo = ({
  show_video_dom,
  hide_video_dom,
}: {
  show_video_dom: HTMLVideoElement;
  hide_video_dom: HTMLVideoElement;
}) => {
  videoManager.show(show_video_dom);
  setTimeout(() => {
    videoManager.hidden(hide_video_dom);
  }, 300);
};

export const videoManager = {
  play: (videoDom: HTMLVideoElement, callback?: Function) => {
    if (videoDom) {
      videoDom.play &&
        videoDom
          .play()
          .then(() => {
            callback && callback();
          })
          .catch((err: Error) => {
            message.error(err.message);
          });
    }
  },
  stop: (videoDom: HTMLVideoElement) => {
    if (videoDom) {
      videoDom.pause && videoDom.pause();
    }
  },
  hidden: (videoDom: HTMLVideoElement, isTimeout: boolean = true) => {
    if (videoDom) {
      if (isTimeout) {
        setTimeout(() => {
          videoDom.style.display = 'none';
        }, 300);
      } else {
        videoDom.style.display = 'none';
      }
    }
  },
  show: (videoDom: HTMLVideoElement) => {
    if (videoDom) {
      videoDom.style.display = 'block';
    }
  },
  init: (videoDom: HTMLVideoElement) => {
    if (videoDom) {
      // 视频回到第一帧
      videoDom.currentTime = 0;
    }
  },
  setSrc: (videoDom: HTMLVideoElement, src: string) => {
    if (videoDom) {
      videoDom.src = src;
    }
  },
};
