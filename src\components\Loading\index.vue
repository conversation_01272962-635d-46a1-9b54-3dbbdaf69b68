<script setup lang="ts">
  import { SyncOutlined } from '@ant-design/icons-vue';
  import Icon from '@/components/Icon/index.vue';

  const props = defineProps({
    state: {
      type: [String, Object],
      default: '',
    },
    name: { // 新增
      type: String,
      default: '',
    },
    handleDelete: {
      type: Function,
      default: () => {},
    },
    handleReGenerate: {
      type: Function,
      default: () => {},
    },
  });
</script>

<template>
  <div class="loading">
    <template v-if="!props.state">
      <span v-if="typeof state === 'string'" class="text">{{ props.state }}</span>
      <span v-else>{{ props.state }}</span>
    </template>
    <template v-else>
      <div v-if="['数字人生成失败，请重新生成', '声⾳⽣成失败，请重新⽣成'].includes(props.state)" class="fail">
        <Icon name="jinggao" :size="32" />
        <p class="fail-text">{{ props.state }}</p>
        <div class="fail-btn">
          <a-button class="btn-item" @click="props.handleReGenerate">重新生成</a-button>
          <a-popconfirm
            :title="`确定删除数字人 “${props.name}”？`"
            ok-text="确定"
            cancel-text="取消"
            @confirm="props.handleDelete"
          >
            <template #description>
              <div style="color: #999;">删除后不可恢复</div>
            </template>
            <a-button class="fail-btn-item">删除</a-button>
          </a-popconfirm>
          <!-- <a-button class="fail-btn-item" style="margin-left: 5px" @click="props.handleDelete"> 删除 </a-button> -->
        </div>
      </div>

      <div v-else>
        <SyncOutlined class="sync-icon" spin />
        <p class="text">{{ props.state }}</p>
      </div>
    </template>
  </div>
</template>

<style lang="less" scoped>
  .loading {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 8px;
    color: #fff;
    // font-size: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    .text {
      font-size: 14px;
    }
    .sync-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto;
      width: 32px;
    }

    .fail {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      width: 100%;
      .fail-icon {
        width: 32px;
        // padding-bottom: 10px;
        // margin-bottom: 10px;
      }
      .fail-text {
        width: 70%;
        font-size: 12px;
        margin-top: 10px;
        text-align: center;
      }
      .fail-btn {
        padding: 0 5px;
        margin: 0;
        width: 100%;
        display: flex;
        justify-content: space-around;
        .btn-item {
          // margin-right: 5px;
          font-size: 12px;
          margin: 0;
          padding: 0 2px;

        .fail-btn-item{
          margin: 0;
          padding: 0;
          font-size: 12px;
        }


          :deep(.ant-btn-default) {
            background: #999999;
            color: #fff;
          }
        }
      }
    }

    :deep(.ant-popover-message-icon) {
      display: none !important;
    }
  }
</style>
