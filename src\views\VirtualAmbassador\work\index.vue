<script setup lang="ts">
  import { onMounted, reactive } from 'vue';
  import { DeleteOutlined, DownloadOutlined } from '@ant-design/icons-vue';
  import { getVirtualAmbassadorList, deleteVirtualAmbassador } from '@/api/virtualAmbassador';
  import { getLocalItem } from '@/utils/common';
  import EmptyImage from '@/assets/image/base/pictures/empty_project.png';

  interface VirtualAmbassadorItemProps {
    id: string;
    name: string;
    image_url: string;
    created_at: string;
    // add other properties if needed
  }

  const state = reactive<{
    list: VirtualAmbassadorItemProps[];
  }>({
    list: [],
  });

  const getListData = async () => {
    const { userId } = JSON.parse(getLocalItem('HQSK_AI_PLATFORM_FRONTEND_USERINFO') || '{}');

    const data = await getVirtualAmbassadorList({
      user_id: userId,
      current_page: 0,
      page_size: 100,
      order_by: 'created_at_desc',
    });

    state.list = data || [];
  };

  const formatDate = (dateStr: string) => {
    if (!dateStr) return '';
    return dateStr.slice(0, 10);
  };

  const downloadImage = async (picture_url: string) => {
    const imageURL = picture_url;
    const response = await fetch(imageURL);
    const blob = await response.blob();
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = 'downloaded_image.jpg'; // 为下载的图片指定一个文件名
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const deleteImage = async (id: string) => {
    console.log(`Deleting image with ID: ${id}`);
    try {
      await deleteVirtualAmbassador({ id });
      state.list = state.list.filter((item) => item.id !== id);
    } catch (error) {
      console.error('形象删除失败', error);
    }
  };

  onMounted(() => {
    getListData();
  });
</script>

<template>
  <div class="virtual-ambassador">
    <template v-if="state.list.length === 0">
      <div class="card-list-empty">
        <img :src="EmptyImage" alt="" class="empty-image" />
        <div class="empty-text">你还没有生成虚拟形象大使哦，快去一键生成吧</div>
      </div>
    </template>

    <div v-else class="virtual-ambassador-content">
      <div class="card-list">
        <div v-for="item in state.list" :key="item?.id" class="card-item">
          <div class="img-box">
            <img :src="item?.image_url" alt="" />
            <div class="card-actions">
              <DownloadOutlined class="action-btn" @click="downloadImage(item?.image_url)" />

              <a-popconfirm
                title="确定要删除该形象吗？"
                ok-text="确定"
                cancel-text="取消"
                @confirm="deleteImage(item?.id)"
              >
                <DeleteOutlined class="action-btn" />
              </a-popconfirm>
            </div>
          </div>
          <div class="card-name">{{ item?.name || '暂未起名字' }}</div>
          <div class="card-date">{{ formatDate(item?.created_at) || '' }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
  .virtual-ambassador {
    width: 100%;
    height: calc(100vh - 940px);
    .card-list-empty {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      height: calc(100vh - 240px);
      overflow: hidden;
      margin-top: -30px;

      .empty-image {
        margin-top: 0;
      }
      .empty-text {
        padding-top: 16px;
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #636466;
        line-height: 22px;
        text-align: right;
        font-style: normal;
      }
    }
    .virtual-ambassador-content {
      width: 100%;
      height: calc(100vh - 240px);
      background: #ffffff;
      display: flex;
      justify-content: flex-start;
      align-items: flex-start;
      padding: 20px;
      overflow-y: auto;

      &:empty {
        overflow: hidden;
      }

      .card-list {
        display: flex;
        flex-wrap: wrap;
        gap: 32px;
        overflow-y: auto;
        &:empty {
          overflow: hidden;
        }

        .card-item {
          width: 210px;
          display: flex;
          flex-direction: column;
          align-items: flex-start;

          .img-box {
            width: 204px;
            height: 204px;
            border-radius: 16px;
            overflow: hidden;
            position: relative;
            background: #fff;
            img {
              width: 204px;
              height: 204px;
              object-fit: contain;
              display: block;
            }
            .card-actions {
              position: absolute;
              width: 204px;
              top: 0;
              right: 0;
              display: flex;
              justify-content: space-between;
              gap: 8px;
              padding: 8px;
              opacity: 0;
              transition: opacity 0.2s;
              .action-btn {
                width: 28px;
                height: 28px;
                border-radius: 6px;
                background: #17181a;
                border-radius: 4px;
                opacity: 0.65;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                color: #ffffff;
                font-size: 16px;
                font-weight: 800;
              }
            }
            &:hover .card-actions {
              opacity: 1;
            }
          }
          .card-name {
            margin-top: 12px;
            font-family:
              PingFangSC,
              PingFang SC;
            font-weight: 500;
            font-size: 16px;
            color: #17181a;
            line-height: 22px;
            text-align: left;
            font-style: normal;
          }
          .card-date {
            margin-top: 4px;

            font-family:
              PingFangSC,
              PingFang SC;
            font-weight: 500;
            font-size: 14px;
            color: #969799;
            line-height: 20px;
            text-align: left;
            font-style: normal;
          }
        }
      }
    }
  }
</style>
