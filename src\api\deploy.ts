import type { ICreateDeployTaskProps, IDeployCallProps } from '@/interface/deploy';
import request from '@/utils/request';

export function fetchDeployList(data: { page: number; limit: number }) {
  return request.$Axios.get('/deploy/list', data);
}

export function singleDeployList({ id, ...data }: { id: string; page: number; limit: number }) {
  return request.$Axios.get(`/deploy/list/by_model/${id}`, data);
}

export function fetchDeployInfo(key: string) {
  return request.$Axios.get(`/deploy/${key}`);
}

export const fetchDeployLogs = (key: string, lines: string) => {
  return request.$Axios.get(`/deploy/logs/${key}`, { lines });
};
export const createDeployTask = (data: ICreateDeployTaskProps) => {
  return request.$Axios.post(`/deploy/llm_basic`, data);
};
export const createOCRDeployTask = (data: Omit<ICreateDeployTaskProps, 'deploy_method'>) => {
  return request.$Axios.post(`/deploy/ocr_basic`, data);
};
export const createTrainDeployTask = (data: ICreateDeployTaskProps) => {
  return request.$Axios.post(`/deploy/llm_trained`, data);
};
// 部署服务操作
export const deploymentOperation = (data: { opration: 'stop' | 'restart' | 'rm'; oid: string }) => {
  return request.$Axios.put(`/deploy/${data.opration}/${data.oid}`, data);
};
export const deployCall = (data: IDeployCallProps) => {
  return request.$Axios.post(`/deploy/api/call/${data.model_id}`, data);
};

export const deployOCRCall = (data: { svc_id: string; url: string }) => {
  return request.$Axios.post(`/model_svc/ocr/image_url/${data.svc_id}`, { url: data.url });
};

//监控
export const deployMonitor = (key: string, data: { start_at: string, end_at: string, time_unit: string }) => {
  return request.$Axios.get(`deploy/api_monitor_data/${key}`, data);
};

// 检查部署名称是否重复
export const checkDeployName = (name: string) => {
  return request.$Axios.post(`/deploy/service_name/check`, { name });
};
