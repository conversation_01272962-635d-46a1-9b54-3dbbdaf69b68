type IFormType = 'input' | 'select' | 'cascader' | 'date' | 'dateRange' | 'inputNumber';

export type IOptions = {
  label: string;
  value: string | number;
  children?: IOptions[];
};

export interface IPagination {
  page_index: number;
  page_size: number;
}

export interface IFormItem {
  field: string; // v-model绑定到对象对应的属性
  label: string;
  placeholder: string | string[];
  type: IFormType; // 表单类型
  options?: IOptions[]; // 下拉框属性 // 其他想要配置也可以配置
  showSearch?: boolean; // 是否可搜索
  dateFormat?: number; //  时间选择方式，0 年 / 1 月 / 2 日 / 3 时分秒
  showTimedefaultValue?: string; //   时分秒选择功能
  loading?: boolean; // loading
}

export interface IPage { page: number; limit: number }

export interface IAceOptions {
  enableBasicAutocompletion: boolean;
  enableSnippets: boolean;
  enableLiveAutocompletion: boolean;
  tabSize: number;
  enableEmmet: boolean;
  fontSize: number;
  /** 去除编辑器里的竖线 */
  showPrintMargin: boolean;
  highlightActiveLine: boolean;
  useWorker: boolean;
  readOnly: boolean;
  wrap?: boolean;
}