.operation-box {
  white-space: nowrap;

  a {
    margin-right: 10px;
  }

  .del-btn {
    color: #ff4d4f !important;

    &:hover {
      color: #ff4d50c7 !important;
    }
  }
}

.table-a-btn {
  margin-right: 10px;
}

.del-btn {
  color: #ff4d4f !important;

  &:hover {
    color: #ff4d50c7 !important;
  }
}

.textbefo {
  position: relative;
  padding: 5px 0 5px 20px;
  // padding-left: 40px;
  margin: 10px 0 20px;
  font-size: 15px;
  font-weight: 600;
}

.textbefo::before {
  position: absolute;
  top: 9px;
  left: 0;
  display: inline-block;
  width: 5px;
  height: 16px;
  content: '';
  background: rgb(38 61 252 / 82%);
}

.section-container {
  .full;
  .flex-mode(column, flex-start, stretch);

  .main-content {
    flex: 1;
    overflow: hidden;

    &-scroll {
      flex: 1;
      overflow-y: auto;
      .overflow-scroll;
    }
  }
}

.ant-spin-nested-loading {
  height: 100%;
}

.ant-spin-container {
  height: 100%;
  .overflow-scroll;
}

.carousel {
  height: 20px;
  margin-bottom: 10px;
  overflow: hidden;

  .carousel-item {
    animation: marquee 10s linear infinite;
  }
}

@keyframes marquee {
  0% {
    transform: translateX(100%);
  }

  100% {
    transform: translateX(-20%);
  }
}
