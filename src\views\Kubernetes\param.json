[{"queries": [{"datasource": {"type": "prometheus", "uid": "deamf410un6rkd"}, "expr": "DCGM_FI_DEV_GPU_TEMP{instance=~\"172.17.0.150:30088\", gpu=~\"(0|1|2|3|4|5|6|7)\"}", "instant": false, "interval": "", "legendFormat": "GPU {{gpu}}", "refId": "A", "exemplar": false, "requestId": "12A", "utcOffsetSec": 28800, "scopes": [], "adhocFilters": [], "datasourceId": 1, "intervalMs": 60000, "maxDataPoints": 933}], "from": "1749152103736", "to": "1749195303736", "key": "GPU温度"}, {"queries": [{"datasource": {"type": "prometheus", "uid": "deamf410un6rkd"}, "editorMode": "code", "expr": "DCGM_FI_DEV_POWER_USAGE{instance=~\"172.17.0.150:30088\", gpu=~\"(0|1|2|3|4|5|6|7)\"}", "interval": "", "legendFormat": "GPU {{gpu}}", "range": true, "refId": "A", "exemplar": false, "requestId": "10A", "utcOffsetSec": 28800, "scopes": [], "adhocFilters": [], "datasourceId": 1, "intervalMs": 60000, "maxDataPoints": 933}, {"datasource": {"type": "prometheus", "uid": "deamf410un6rkd"}, "editorMode": "code", "exemplar": false, "expr": "DCGM_FI_DEV_POWER_MGMT_LIMIT{instance=~\"172.17.0.150:30088\", gpu=~\"(0|1|2|3|4|5|6|7)\"}", "format": "table", "hide": false, "instant": false, "legendFormat": "__auto", "range": true, "refId": "B", "requestId": "10B", "utcOffsetSec": 28800, "scopes": [], "adhocFilters": [], "interval": "", "datasourceId": 1, "intervalMs": 60000, "maxDataPoints": 933}], "from": "1749152103736", "to": "1749195303736", "key": "GPU消耗功率"}, {"queries": [{"datasource": {"type": "prometheus", "uid": "deamf410un6rkd"}, "editorMode": "code", "expr": "DCGM_FI_DEV_FB_USED{instance=~\"172.17.0.150:30088\", gpu=~\"(0|1|2|3|4|5|6|7)\"} * 1024 * 1024", "format": "time_series", "hide": false, "interval": "", "legendFormat": "GPU {{gpu}}", "range": true, "refId": "A", "exemplar": false, "requestId": "40A", "utcOffsetSec": 28800, "scopes": [], "adhocFilters": [], "datasourceId": 1, "intervalMs": 60000, "maxDataPoints": 933}, {"datasource": {"type": "prometheus", "uid": "deamf410un6rkd"}, "editorMode": "code", "exemplar": false, "expr": "DCGM_FI_DEV_FB_TOTAL{instance=~\"172.17.0.150:30088\", gpu=~\"(0|1|2|3|4|5|6|7)\"} * 1024 * 1024", "format": "table", "hide": false, "instant": false, "interval": "", "legendFormat": "__auto", "range": true, "refId": "B", "requestId": "40B", "utcOffsetSec": 28800, "scopes": [], "adhocFilters": [], "datasourceId": 1, "intervalMs": 60000, "maxDataPoints": 933}], "from": "1749152103736", "to": "1749195303736", "key": "显存使用量"}, {"queries": [{"datasource": {"type": "prometheus", "uid": "deamf410un6rkd"}, "disableTextWrap": false, "editorMode": "builder", "expr": "DCGM_FI_DEV_MEM_COPY_UTIL{instance=~\"172.17.0.150:30088\", gpu=~\"(0|1|2|3|4|5|6|7)\"}", "fullMetaSearch": false, "includeNullMetadata": true, "interval": "", "legendFormat": "GPU {{gpu}}", "range": true, "refId": "A", "useBackend": false, "exemplar": false, "requestId": "34A", "utcOffsetSec": 28800, "scopes": [], "adhocFilters": [], "datasourceId": 1, "intervalMs": 60000, "maxDataPoints": 933}], "from": "1749152103736", "to": "1749195303736", "key": "显存带宽使用率"}, {"queries": [{"datasource": {"type": "prometheus", "uid": "deamf410un6rkd"}, "editorMode": "code", "expr": "DCGM_FI_DEV_FB_USED{instance=~\"172.17.0.150:30088\", gpu=~\"(0|1|2|3|4|5|6|7)\"} * 1024 * 1024 / (DCGM_FI_DEV_FB_FREE{instance=~\"172.17.0.150:30088\", gpu=~\"(0|1|2|3|4|5|6|7)\"} * 1024 * 1024 + DCGM_FI_DEV_FB_USED{instance=~\"172.17.0.150:30088\", gpu=~\"(0|1|2|3|4|5|6|7)\"} * 1024 * 1024)", "format": "time_series", "hide": false, "interval": "", "legendFormat": "GPU {{gpu}}", "range": true, "refId": "A", "exemplar": false, "requestId": "41A", "utcOffsetSec": 28800, "scopes": [], "adhocFilters": [], "datasourceId": 1, "intervalMs": 60000, "maxDataPoints": 933}, {"datasource": {"type": "prometheus", "uid": "deamf410un6rkd"}, "editorMode": "code", "exemplar": false, "expr": "DCGM_FI_DEV_FB_TOTAL{instance=~\"172.17.0.150:30088\", gpu=~\"(0|1|2|3|4|5|6|7)\"} * 1024 * 1024", "format": "table", "hide": false, "instant": false, "interval": "", "legendFormat": "__auto", "range": true, "refId": "B", "requestId": "41B", "utcOffsetSec": 28800, "scopes": [], "adhocFilters": [], "datasourceId": 1, "intervalMs": 60000, "maxDataPoints": 933}], "from": "1749152103736", "to": "1749195303736", "key": "显存使用率"}, {"queries": [{"datasource": {"type": "prometheus", "uid": "deamf410un6rkd"}, "editorMode": "builder", "expr": "DCGM_FI_DEV_GPU_UTIL{instance=~\"172.17.0.150:30088\", gpu=~\"(0|1|2|3|4|5|6|7)\"}", "interval": "", "legendFormat": "GPU {{gpu}}", "range": true, "refId": "A", "exemplar": false, "requestId": "6A", "utcOffsetSec": 28800, "scopes": [], "adhocFilters": [], "datasourceId": 1, "intervalMs": 60000, "maxDataPoints": 933}], "from": "1749152103736", "to": "1749195303736", "key": "GPU使用率"}, {"queries": [{"datasource": {"type": "prometheus", "uid": "deamf410un6rkd"}, "disableTextWrap": false, "editorMode": "builder", "exemplar": false, "expr": "DCGM_FI_DEV_DEC_UTIL{instance=~\"172.17.0.150:30088\", gpu=~\"(0|1|2|3|4|5|6|7)\"}", "format": "table", "fullMetaSearch": false, "includeNullMetadata": true, "instant": true, "legendFormat": "__auto", "range": false, "refId": "A", "useBackend": false, "requestId": "30A", "utcOffsetSec": 28800, "scopes": [], "adhocFilters": [], "interval": "", "datasourceId": 1, "intervalMs": 120000, "maxDataPoints": 227}], "from": "1749152103736", "to": "1749195303736", "key": ""}, {"queries": [{"datasource": {"type": "prometheus", "uid": "deamf410un6rkd"}, "disableTextWrap": false, "editorMode": "code", "exemplar": false, "expr": "sum(DCGM_FI_DEV_TOTAL_ENERGY_CONSUMPTION{instance=~\"172.17.0.150:30088\", gpu=~\"(0|1|2|3|4|5|6|7)\"}) / 1000", "format": "table", "fullMetaSearch": false, "includeNullMetadata": true, "instant": true, "legendFormat": "__auto", "range": false, "refId": "A", "useBackend": false, "requestId": "32A", "utcOffsetSec": 28800, "scopes": [], "adhocFilters": [], "interval": "", "datasourceId": 1, "intervalMs": 120000, "maxDataPoints": 306}], "from": "1749152103736", "to": "1749195303736", "key": "GPU消耗功率"}, {"queries": [{"datasource": {"type": "prometheus", "uid": "deamf410un6rkd"}, "editorMode": "code", "expr": "avg(DCGM_FI_DEV_POWER_USAGE{instance=~\"172.17.0.150:30088\", gpu=~\"(0|1|2|3|4|5|6|7)\"})", "interval": "", "legendFormat": "", "range": true, "refId": "A", "exemplar": false, "requestId": "36A", "utcOffsetSec": 28800, "scopes": [], "adhocFilters": [], "datasourceId": 1, "intervalMs": 120000, "maxDataPoints": 227}], "from": "1749152103736", "to": "1749195303736", "key": "GPU平均功率"}, {"queries": [{"datasource": {"type": "prometheus", "uid": "deamf410un6rkd"}, "editorMode": "code", "expr": "avg(DCGM_FI_DEV_GPU_TEMP{instance=~\"172.17.0.150:30088\", gpu=~\"(0|1|2|3|4|5|6|7)\"})", "interval": "", "legendFormat": "", "range": true, "refId": "A", "exemplar": false, "requestId": "14A", "utcOffsetSec": 28800, "scopes": [], "adhocFilters": [], "datasourceId": 1, "intervalMs": 120000, "maxDataPoints": 227}], "from": "1749152103736", "to": "1749195303736", "key": "GPU平均温度"}, {"queries": [{"datasource": {"type": "prometheus", "uid": "deamf410un6rkd"}, "editorMode": "code", "expr": "avg(DCGM_FI_DEV_MEM_COPY_UTIL{instance=~\"172.17.0.150:30088\", gpu=~\"(0|1|2|3|4|5|6|7)\"})", "interval": "", "legendFormat": "", "range": true, "refId": "A", "exemplar": false, "requestId": "35A", "utcOffsetSec": 28800, "scopes": [], "adhocFilters": [], "datasourceId": 1, "intervalMs": 120000, "maxDataPoints": 227}], "from": "1749152103736", "to": "1749195303736", "key": "显存带宽平均使用率"}, {"queries": [{"datasource": {"type": "prometheus", "uid": "deamf410un6rkd"}, "editorMode": "code", "expr": "avg(sum(DCGM_FI_DEV_FB_USED{instance=~\"172.17.0.150:30088\", gpu=~\"(0|1|2|3|4|5|6|7)\"}) / (sum(DCGM_FI_DEV_FB_USED{instance=~\"172.17.0.150:30088\", gpu=~\"(0|1|2|3|4|5|6|7)\"}) + sum(DCGM_FI_DEV_FB_FREE{instance=~\"172.17.0.150:30088\", gpu=~\"(0|1|2|3|4|5|6|7)\"})))", "interval": "", "legendFormat": "", "range": true, "refId": "A", "exemplar": false, "requestId": "37A", "utcOffsetSec": 28800, "scopes": [], "adhocFilters": [], "datasourceId": 1, "intervalMs": 120000, "maxDataPoints": 227}], "from": "1749152103736", "to": "1749195303736", "key": "显存平均使用率"}, {"queries": [{"datasource": {"type": "prometheus", "uid": "deamf410un6rkd"}, "editorMode": "code", "expr": "avg(DCGM_FI_DEV_GPU_UTIL{instance=~\"172.17.0.150:30088\", gpu=~\"(0|1|2|3|4|5|6|7)\"})", "interval": "", "legendFormat": "", "range": true, "refId": "A", "exemplar": false, "requestId": "26A", "utcOffsetSec": 28800, "scopes": [], "adhocFilters": [], "datasourceId": 1, "intervalMs": 120000, "maxDataPoints": 227}], "from": "1749152103736", "to": "1749195303736", "key": "GPU平均使用率"}, {"queries": [{"datasource": {"type": "prometheus", "uid": "deamf410un6rkd"}, "disableTextWrap": false, "editorMode": "builder", "exemplar": false, "expr": "DCGM_FI_DEV_GPU_UTIL{instance=~\"172.17.0.150:30088\", gpu=~\"(0|1|2|3|4|5|6|7)\"}", "format": "table", "fullMetaSearch": false, "includeNullMetadata": true, "instant": true, "legendFormat": "__auto", "range": false, "refId": "A", "useBackend": false, "requestId": "31A", "utcOffsetSec": 28800, "scopes": [], "adhocFilters": [], "interval": "", "datasourceId": 1, "intervalMs": 120000, "maxDataPoints": 227}], "from": "1749152103736", "to": "1749195303736", "key": ""}, {"queries": [{"datasource": {"type": "prometheus", "uid": "deamf410un6rkd"}, "editorMode": "code", "expr": "DCGM_FI_DEV_SM_CLOCK{instance=~\"172.17.0.150:30088\", gpu=~\"(0|1|2|3|4|5|6|7)\"} * 1000000", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "GPU {{gpu}}", "range": true, "refId": "A", "exemplar": false, "requestId": "2A", "utcOffsetSec": 28800, "scopes": [], "adhocFilters": [], "datasourceId": 1, "intervalMs": 120000, "maxDataPoints": 933}, {"datasource": {"type": "prometheus", "uid": "deamf410un6rkd"}, "editorMode": "code", "expr": "DCGM_FI_DEV_MAX_SM_CLOCK{instance=~\"172.17.0.150:30088\", gpu=~\"(0|1|2|3|4|5|6|7)\"} * 1000000", "format": "table", "hide": false, "instant": false, "legendFormat": "__auto", "range": true, "refId": "B", "exemplar": false, "requestId": "2B", "utcOffsetSec": 28800, "scopes": [], "adhocFilters": [], "interval": "", "datasourceId": 1, "intervalMs": 120000, "maxDataPoints": 933}], "from": "1749116328560", "to": "1749202728560", "key": "GPU的SM时钟频率"}, {"queries": [{"datasource": {"type": "prometheus", "uid": "deamf410un6rkd"}, "disableTextWrap": false, "editorMode": "code", "expr": "DCGM_FI_DEV_MEM_CLOCK{instance=~\"172.17.0.150:30088\", gpu=~\"(0|1|2|3|4|5|6|7)\"} * 1000000", "format": "time_series", "fullMetaSearch": false, "includeNullMetadata": true, "interval": "", "intervalFactor": 1, "legendFormat": "GPU {{gpu}}", "range": true, "refId": "A", "useBackend": false, "exemplar": false, "requestId": "19A", "utcOffsetSec": 28800, "scopes": [], "adhocFilters": [], "datasourceId": 1, "intervalMs": 120000, "maxDataPoints": 933}, {"datasource": {"type": "prometheus", "uid": "deamf410un6rkd"}, "editorMode": "code", "expr": "DCGM_FI_DEV_MAX_MEM_CLOCK{instance=~\"172.17.0.150:30088\", gpu=~\"(0|1|2|3|4|5|6|7)\"} * 1000000", "format": "table", "hide": false, "instant": false, "legendFormat": "__auto", "range": true, "refId": "B", "exemplar": false, "requestId": "19B", "utcOffsetSec": 28800, "scopes": [], "adhocFilters": [], "interval": "", "datasourceId": 1, "intervalMs": 120000, "maxDataPoints": 933}], "from": "1749116328560", "to": "1749202728560", "key": "GPU内存时钟频率"}]