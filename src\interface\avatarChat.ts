export interface ChatWsProps {
  code: number; // 状态码
  answer?: string; // 可选字段，表示回答内容
  live_url?: {
    hls_flv: string | undefined; // 视频播放地址
  };
  play_url?: string; // 播放地址
  context_id?: string;
}

export interface ParamsProps {
  type?: string;
  question: string;
  [key: string]: unknown; // 允许额外的动态键值对
}

export interface WordResponseProps {
  length: number;
  [key: string]: unknown; // 允许额外字段
}

export enum Direction_Message {
  QUESTION = 'question',
  ANSWER = 'answer',
  SPECIALANSWER = 'specialAnswer',
  ERROR = 'error',
}

export interface Message {
  type: string;
  text: string;
  picture?: string;
  specialText?: string;
}
