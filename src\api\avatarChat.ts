import request from '@/utils/request';
import { getEnv } from '@/utils';

const { VITE_APP_AVATAR_URL } = getEnv();

const AVATAR = VITE_APP_AVATAR_URL;

// 定义宽松的对象类型
type TLooseObject = Record<string, unknown>;

export function getBackgroundImage(params?: TLooseObject) {
  return request.$Axios.get(`/avatar-back-end/background_image/[${AVATAR}]`, params);
}

export function changeBackground(params: TLooseObject) {
  return request.$Axios.put(`/avatar-back-end/background_image/[${AVATAR}]`, params);
}

export function getSpeakersList(params?: TLooseObject) {
  return request.$Axios.get(`/avatar-back-end/speaker/available_languages[${AVATAR}]`, params);
}

export function getPretrainList(params?: TLooseObject) {
  return request.$Axios.get(`/virtual-classroom-service/face/pretrain/[${AVATAR}]`, params);
}
export function deleteMode(params?: TLooseObject) {
  return request.$Axios.del(`/virtual-classroom-service/face/pretrain/[${AVATAR}]`, params);
}

interface DigitalListProps {
  url: string;
  gender: string;
}
export function getModeDigitals(params?: TLooseObject) {
  return request.$Axios
    .get(`/virtual-classroom-service/face/lecture/[${AVATAR}]`, params)
    .then((data) => data as DigitalListProps[]);
}

export function uploadDigital(params: FormData, config: TLooseObject) {
  return request.$Axios
    .post(`/virtual-classroom-service/face/upload/[${AVATAR}]`, params, config)
    .then((data) => data as string[]);
}
export function strtPretrain(params: TLooseObject) {
  return request.$Axios.post(`/virtual-classroom-service/face/pretrain/[${AVATAR}]`, params);
}
export function getPreview(params: TLooseObject) {
  return request.$Axios.post(`/virtual-classroom-service/face/generate/[${AVATAR}]`, params);
}
export function getCartoonPreview(params: TLooseObject) {
  return request.$Axios.post(`/virtual-classroom-service/face/cartoon/generate/[${AVATAR}]`, params);
}

export function preload(params: TLooseObject) {
  return request.$Axios.post(`/virtual-classroom-service/face/preload/[${AVATAR}]`, params);
}

// export function deleteMode(params?: TLooseObject) {
//   return request.$Axios.del(`/virtual-classroom-service/face/pretrain/[${AVATAR}]`, params);
// }

export function reName(params: TLooseObject) {
  return request.$Axios.put(`/virtual-classroom-service/face/pretrain/[${AVATAR}]`, params);
}

export function getVoiceList(user_id: string) {
  return request.$Axios.get(`/avatar-back-end/voice/${user_id}[${AVATAR}]`);
}

export function deleteVoice(id: string) {
  return request.$Axios.del(`/avatar-back-end/voice/${id}[${AVATAR}]`);
}

export function renameVoice(params: TLooseObject) {
  // 拼接 query 参数
  const query = `?new_name=${encodeURIComponent(String(params.new_name))}&user_id=${encodeURIComponent(String(params.user_id))}`;
  return request.$Axios.put(`/avatar-back-end/voice/${params.voice_id}/name${query}[${AVATAR}]`);
}

export function UploadVideo(params: FormData, config: TLooseObject) {
  return request.$Axios
    .post(`/avatar-back-end/voice/upload[${AVATAR}]`, params, config)
    .then((data) => data as string[]);
}

export function StartCloning(id: string) {
  return request.$Axios.post(`/avatar-back-end/voice/${id}/clone[${AVATAR}]`);
}


