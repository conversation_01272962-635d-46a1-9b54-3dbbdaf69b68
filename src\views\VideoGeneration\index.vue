<script lang="ts" setup>
  import { ref } from 'vue';
  import VideoGeneration from './VideoGeneration/index.vue';
  // import StyleTransfer from './styleTransfer/index.vue';

  const activeKey = ref('1');
</script>

<template>
  <a-tabs v-model:active-key="activeKey" :tab-bar-gutter="0">
    <a-tab-pane key="1" tab="图文生视频">
      <VideoGeneration v-if="activeKey === '1'">
        <template #modelInfo="model">
          <div class="model-info">
            <div>{{ model.value.name }} 基本信息</div>
            <div>
              <a-tag v-for="(t, i) in model.value.tags" :key="i" color="cyan">{{ t }}</a-tag>
            </div>
            <div>{{ model.value.description }}</div>
          </div>
        </template>
      </VideoGeneration>
    </a-tab-pane>
    <!-- <a-tab-pane key="2" tab="视频编辑" disabled force-render>视频编辑</a-tab-pane> -->
  </a-tabs>
</template>

<style lang="less" scoped>
  :deep(.ant-tabs-nav) {
      margin-bottom: 0 !important;
    }
  :deep(.ant-tabs-nav::before) {
    right: -12px !important;
    left: -12px !important;
    position: absolute;
    border-bottom: 1px solid rgba(5, 5, 5, 0.06);
    content: '';
  }
  :deep(.ant-tabs-tab) {
    padding: 8px 0 !important;
    > .ant-tabs-tab-btn {
      text-align: center;
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 500;
      font-size: 16px !important;
      color: #afb0b3 !important;
      line-height: 22px;
      font-style: normal;
    }
  }
  :deep(.ant-tabs-tab-active > .ant-tabs-tab-btn) {
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 500;
    font-size: 16px !important;
    color: #17181a !important;
    line-height: 22px;
    text-align: left;
    font-style: normal;
  }
  :deep(.ant-tabs-tab:nth-child(1)) {
    margin: 0 30px;
  }

  .model-info {
    color: #000 !important;
    > div:nth-child(1) {
      white-space: nowrap;
      padding: 0 20px 0 0;
      font-size: 16px;
      font-weight: 600;
      line-height: 24px;
      color: #333333;
      letter-spacing: 0;
      margin-bottom: 10px;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    > div:nth-child(2) {
      margin-bottom: 10px;
    }
    > div:nth-child(3) {
      width: 100%;
      height: 60px;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 5;
      overflow: hidden;
      text-overflow: ellipsis;
      font-size: 12px;
      font-weight: 400;
      line-height: 20px;
      color: grey;
      letter-spacing: 0;
    }
  }
</style>
