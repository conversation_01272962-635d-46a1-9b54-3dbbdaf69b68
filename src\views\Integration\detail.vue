<script setup lang="ts">
  import { ref, onMounted, reactive } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { LeftOutlined } from '@ant-design/icons-vue';
  import History from './Components/history.vue';
  import Exploration from './Components/exploration.vue';
  import { getIntegrationDetail } from '@/api/integration';
  import type { IIntegrationDetail } from '@/interface/integration';
  const router = useRouter();
  const route = useRoute();
  const activeKey = ref<string>(route.query.tab ? String(route.query.tab) : 'detail');
  // @ts-expect-error
  const formState = reactive<IIntegrationDetail>({});

  const handleChange = (key: string) => {
    activeKey.value = key;
    router.push({
      path: route.path,
      query: { ...route.query, tab: key },
    });
  };
  onMounted(async () => {
    const data: IIntegrationDetail = await getIntegrationDetail(String(route.params.id));
    Object.assign(formState, data);
  });
</script>

<template>
  <div class="h-100%">
    <div class="text-xl m-b-20px">
      <LeftOutlined @click="router.push('/integration')" />
      <span class="m-l-10px">{{ route.query.name || '--' }}</span>
    </div>
    <a-tabs v-model:active-key="activeKey" type="card" @change="handleChange">
      <a-tab-pane key="detail" tab="任务详情">
        <a-form ref="modelRef" :model="formState" label-align="right" :label-col="{ style: { width: '100px' } }">
          <div class="textbefo">基本信息</div>
          <a-form-item label="任务名称">{{ formState.name }}</a-form-item>
          <a-form-item label="集成方式">
            <div>
              {{ formState.method }}
              <span class="text-12px text-#797979 ml-5px">{{
                formState.method === 'p2l'
                  ? '精准剖析用户提问，快速为不同部署服务评分排序，直接调用排名第一的优质模型给出精准答案。'
                  : '博采众长，巧妙地将多个部署服务所给出的答案加以融合优化，从而精心产出更优质、更全面且更令人满意的结果。'
              }}</span>
            </div>
          </a-form-item>

          <div class="textbefo">集成服务</div>
          <a-form-item label="已选服务">
            <div></div>
            <div class="select-box">
              <div v-for="item in formState.svc_list" :key="item">{{ item }}</div>
            </div>
          </a-form-item>
          <div v-if="formState.method === 'blender'">
            <div class="textbefo">排序配置</div>
            <a-form-item label="排序模型">{{ formState.config.sort_model }}</a-form-item>
            <div class="textbefo">融合配置</div>
            <a-form-item label="融合模型">{{ formState.config.fusion_model }}</a-form-item>
            <a-form-item label="top_k">{{ formState.config.top_k }}</a-form-item>
          </div>
        </a-form>
      </a-tab-pane>
      <a-tab-pane key="exploration" tab="体验">
        <Exploration :activeKey="activeKey" :info="formState" />
      </a-tab-pane>
      <a-tab-pane key="history" tab="历史记录">
        <History :active-key="activeKey" />
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<style scoped lang="less">
  :deep(.ant-tabs) {
    height: calc(100% - 50px);
  }
  :deep(.ant-tabs-content-holder) {
    overflow: auto;
  }

  :deep(.ant-tabs-content) {
    height: 100%;
  }
  .select-box {
    display: flex;
    flex-wrap: wrap;
    > div {
      background-color: #eef3fe;
      padding: 5px;
      display: flex;
      justify-content: space-between;
      margin: 0 10px 10px 0;
      cursor: pointer;
    }
  }
</style>
