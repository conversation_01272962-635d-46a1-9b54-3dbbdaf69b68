export interface IServiceList {
  published_id?: string
}

export interface IPostServiceSetting {
  deploy_id: string;
  system_prompt: string;
  call_params: ICallParams
}
export interface ICallParams {
  max_tokens: number;
  temperature: number;
  top_p: number;
  frequency_penalty: number;
}
export interface IServiceItem {
  id: string;
  service_name: string;
  model_category: string
}

export interface ISendMessageProps {
  dialog_id: string;
  deploy_id: string;
  headers: object;
  body: ISendMessageBody;
}

export interface ISendMessageBody {
  // model: string;
  messages: { role: string, content: string }[];
  temperature: number;
  top_p: number;
  n: number; // 回答几条消息
  max_tokens: number;
  stream: boolean;
  frequency_penalty: number
}

export interface IUpdateDialogTitleProps {
  dialog_id: string;
  title: string;
}


export interface IMessage {
  role: string;
  content: string
}
export interface IServices {
  id: string;
  service_name: string;
  model_category: string;
  uuid: string;
  messageList: { role: string; content: string, image?: string }[];
  finished?: boolean;
}[]
export interface ISendMessageResponse {
  response?: {
    choices: Array<{
      message: {
        content: string;
      };
    }>;
  };
  dialog_id: string;
}
export interface IDialogContent {
  role: string; // 角色，如 'user' 或 'assistant'
  content: string; // 对话内容
}

// 定义 DialogItem 接口
export interface DialogItem {
  dialog_id: string; // 对话的唯一标识
  contents: IDialogContent[]; // 对话内容数组
}

// 定义 fetchDialogList 的返回值类型
export type IDialogList = DialogItem[];

export interface IModelListResponse {
  creator_id: string;
  model_source_id: string;
  name: string;
  size: string;
  description: string;
  detail: string;
  seq: number;
  status: string;
  published_at: null;
  exp_temp: string;
  id: string;
  updated_at: string;
  created_at: string;
  deleted_at: null;
  tags: Tags[];
}

export interface Tags {
  id: string;
  name: string;
}


export interface IOCRByImage {
  ocr_category: string;
  deploy_id: string;
  image: FormData
}
export interface IOCRByUrl {
  ocr_category: string;
  deploy_id: string;
  url: string
}

export interface IOCRByResponse {
  id: string;
  choices: Choices[];
  created: number;
  model: string;
  object: string;
  service_tier: string;
  system_fingerprint: string;
  usage: Usage;
  prompt_logprobs: string;
}

export interface Usage {
  completion_tokens: number;
  prompt_tokens: number;
  total_tokens: number;
  completion_tokens_details: string;
  prompt_tokens_details: string;
}

export interface Choices {
  finish_reason: string;
  index: number;
  logprobs: string;
  message: Message;
  stop_reason: string;
}

export interface Message {
  content: string;
  refusal: string;
  role: string;
  annotations: string;
  audio: string;
  function_call: string;
  tool_calls: string[];
}