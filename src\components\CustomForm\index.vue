<!--
 * @FilePath: \ai-platform\ai-platform-frontend\src\components\CustomForm\index.vue
 * @Author: chentianya<PERSON>
 * @Date: 2023-07-04 10:23:13
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2025-04-11 14:46:23
 * Copyright: 2023 xxxTech CO.,LTD. All Rights Reserved.
 * @Descripttion: 
-->
<script setup lang="ts">
import { computed, reactive, ref, watch } from 'vue';
import { DownOutlined, UpOutlined, SearchOutlined, SyncOutlined } from '@ant-design/icons-vue';
import type { FormInstance } from 'ant-design-vue';
import locale from 'ant-design-vue/es/date-picker/locale/zh_CN';

import { Spin, Row, Col, Button, Form, Input, Select, Cascader, DatePicker, InputNumber } from 'ant-design-vue';
import type { ShowSearchType } from 'ant-design-vue/lib/vc-cascader';
import type { DefaultOptionType, ValueType } from 'ant-design-vue/lib/vc-cascader/Cascader';
import dayjs from 'dayjs';

type IFormType = 'input' | 'select' | 'cascader' | 'date' | 'dateRange' | 'inputNumber';

export type IOptions = {
  label: string;
  value: string | number;
  children?: IOptions[];
};

export interface IFormItem {
  field: string; // v-model绑定到对象对应的属性
  label: string;
  placeholder: any;
  type: IFormType; // 表单类型
  options?: IOptions[]; // 下拉框属性 // 其他想要配置也可以配置
  showSearch?: boolean; // 是否可搜索
  dateFormat?: number; //  时间选择方式，0 年 / 1 月 / 2 日 / 3 时分秒
  showTimedefaultValue?: any; //   时分秒选择功能
  loading?: boolean; // loading
}
interface IProps {
  formItems: IFormItem[];
  value?: Record<string, any>;
  labelWidth?: string;
  itemStyle?: any;
  disabled?: boolean;
  universal?: any; //  {funName:['change','click'],fun:[()=>{},()=>{}]}  对应 formItems 的下标
}
const props = withDefaults(defineProps<IProps>(), {
  labelWidth: '100px',
  itemStyle: { padding: '0 0 0 8px', width: '100%' },
  disabled: false,
  // itemStyle: { width: '100%' },
});
const formState = ref<Record<string, any>>({});
const expand = ref(false);
const emits = defineEmits<{
  (event: 'onFinish', val: any): void;
  (event: 'onRest'): void;
  (event: 'cascaderChange', value: ValueType): void;
}>();

const formRef = ref<FormInstance>();
const cascaderRef = ref();

const state: any = reactive<any>({
  universalFun: {}, // 通用属性
});

const layout = {
  // labelCol: { span: 6 },
  // wrapperCol: { span: 18 },
};
const searchSpan = computed(() => {
  // 暂时把展开去掉，说不定以后会加上，代码先注释
  // if (expand.value) {
  //   return 24 - (props.formItems.length % 4) * 6;
  // } else {
  //   if (props.formItems.length >= 3) {
  //     return 6;
  //   } else {
  //     return 24 - (props.formItems.length % 3) * 6;
  //   }
  // }
  return 24 - (props.formItems.length % 4) * 6;
});
const calclabelWidth = (label: string) => {
  if (!label) return 0;
  const canvas = document.createElement('canvas');
  const context = canvas.getContext('2d');
  context!.font = '14px PingFang SC';
  const metrics = context!.measureText(label);
  return metrics.width + 20;
};
const handleResetClick = () => {
  formRef.value!.resetFields();
  formState.value = {};
  emits('onRest');
};
const onFinish = () => {
  emits('onFinish', formState.value);
};

const filter: ShowSearchType['filter'] = (inputValue, path) => {
  return path.some((option) => option.label.toLowerCase().indexOf(inputValue.toLowerCase()) > -1);
};

const filterOption = (input: any, option: any, options?: IOptions[]) => {
  const lable = options!.find((item) => item.value === option.key)?.label;
  return `${lable}`.toLowerCase().includes(`${input || ''}`.toLowerCase());
};
const change = (field: string, value: string) => {
  formState.value[field] = value;
};
const cascaderChange = (value: ValueType, _selectOptions: DefaultOptionType[] | DefaultOptionType[][]) => {
  emits('cascaderChange', value);
};

watch(
  () => props.universal,
  (val: any) => {
    if (val) {
      const { funName, fun } = val;
      state.universalFun = funName.map((e: any, i: number) => ({ [e]: fun[i] }));
    }
  },
  { deep: true, immediate: true },
);

watch(
  () => props.value,
  () => {
    if (props.value) {
      formState.value = props.value as Record<string, any>;
    }
  },
  {
    immediate: true,
    deep: true,
  },
);

defineExpose({
  change,
  handleResetClick,
});
</script>

<template>
  <div class="form-container">
    <Form ref="formRef" name="advanced_search" class="ant-advanced-search-form" v-bind="layout" :model="formState"
      @finish="onFinish">
      <Row :gutter="24" align="middle">
        <template v-for="(item, i) in formItems" :key="item.label">
          <Col :span="6">
          <div class="wrapper">
            <Form.Item :label="item.label" :style="{
              ...itemStyle,
              '--label-length': item.type === 'select' ? calclabelWidth(item.label) + 'px' : undefined,
              '--search-length': item.type === 'select' ? calclabelWidth(item.label) - 10 + 'px' : undefined,
            }" :name="item.field" :class="{ 'form-select': item.type === 'select' }">
              <!-- 暂时只做了输入框和下拉框，以后有时间再加其他的 -->
              <template v-if="item.type === 'input'">
                <Input v-model:value="formState[`${item.field}`]" allow-clear autocomplete="off"
                  :placeholder="item.placeholder" v-on="{ change: () => { } }"></Input>
              </template>
              <template v-if="item.type === 'inputNumber'">
                <InputNumber v-model:value="formState[`${item.field}`]" allow-clear :placeholder="item.placeholder"
                  v-on="state.universalFun[i]">
                </InputNumber>
              </template>
              <template v-else-if="item.type === 'select'">
                <Select ref="select" v-model:value="formState[`${item.field}`]" dropdown-class-name="select" allow-clear
                  :show-search="item.showSearch" :mode="item?.mode || 'combobox'"
                  :filter-option="(input, option) => filterOption(input, option, item.options)"
                  :placeholder="item.placeholder" :loading="item.loading" v-on="state.universalFun[i]">
                  <template v-if="item.loading" #notFoundContent>
                    <Spin size="small" />
                  </template>
                  <Select.Option v-for="option in item.options" :key="option.value" :value="option.value">
                    {{ option.label }}
                  </Select.Option>
                </Select>
              </template>
              <template v-if="item.type === 'cascader'">
                <Cascader ref="cascaderRef" v-model:value="formState[`${item.field}`]" :options="item.options"
                  :show-search="item.showSearch ? { filter } : false" :placeholder="item.placeholder" change-on-select
                  @change="cascaderChange" />
              </template>
              <template v-if="item.type === 'date' || item.type === 'dateRange'">
                <template v-if="item.dateFormat === 3">
                  <DatePicker.RangePicker ref="datePickRef" v-model:value="formState[`${item.field}`]"
                    :placeholder="item.placeholder" format="YYYY-MM-DD HH:mm:ss"
                    :show-time="item.showTimedefaultValue ? item.showTimedefaultValue : true"
                    v-on="state.universalFun[i]" />
                </template>
                <template v-else-if="item.dateFormat === 0 || item.dateFormat === 1">
                  <DatePicker.RangePicker ref="datePickRef" v-model:value="formState[`${item.field}`]"
                    :placeholder="item.placeholder" :picker="item.dateFormat === 0 ? 'year' : 'month'"
                    :style="{ width: '100%' }" v-on="state.universalFun[i]" />
                </template>
                <template v-else>
                  <DatePicker.RangePicker ref="datePickRef" v-model:value="formState[`${item.field}`]"
                    format="YYYY/MM/DD" :placeholder="item.placeholder" :style="{ width: '100%' }"
                    v-on="state.universalFun[i]" />
                </template>
              </template>
            </Form.Item>
          </div>
          </Col>
        </template>
        <Col :span="searchSpan" :style="{ textAlign: 'right', marginBottom: '10px' }">
        <Button :disabled="disabled" style="margin: 0 8px" @click="handleResetClick">重置</Button>
        <Button :disabled="disabled" type="primary" html-type="submit">查询</Button>
        <!-- <a
            style="font-size: 12px; margin-left: 8px"
            v-if="formItems.length > 3"
            @click="expand = !expand"
          >
            <template v-if="expand"> <UpOutlined /> 收起 </template>
            <template v-else> <DownOutlined /> 展开 </template>
          </a> -->
        </Col>
      </Row>
    </Form>
  </div>
</template>

<style scoped lang="less">
// :deep(.ant-form-item-label) {
//   text-align-last: right !important;
// }
.form-container {
  width: 100%;
  margin-bottom: 20px;

  .ant-advanced-search-form {
    width: 100%;
  }
}

.ant-form-item {
  margin-bottom: 0;
}

:deep(.ant-form-item-label) {
  >label {
    color: #000000d9 !important;
  }
}

:deep(.ant-select-selection-placeholder){
  left: calc(var(--search-length) + 12px);
}

.form-select {
  :deep(.ant-form-item-label) {
    position: absolute !important;
    z-index: 1;
  }

  :deep(.ant-select-selector) {
    padding-left: var(--label-length) !important;
  }

  :deep(.ant-select-selection-search) {
    padding-left: var(--search-length) !important;
  }
}

.wrapper {
  position: relative;
  margin-bottom: 12px;
  border: 1px solid #d9d9d9;
  border-radius: 2px;

  &:hover {
    border-color: #40a9ff;
  }
}

.ant-input-affix-wrapper {
  border: none !important;
  box-shadow: 0 0 0 2px rgb(24 144 255 / 0%) !important;

  &:hover {
    border: none !important;
  }

  &:focus {
    border: none !important;
  }
}

:deep(.ant-select-selector) {
  border: none !important;
  box-shadow: 0 0 0 2px rgb(24 144 255 / 0%) !important;

  &:hover {
    border: none !important;
  }

  &:focus {
    border: none !important;
  }
}

:deep(.ant-picker) {
  border: none !important;
  box-shadow: 0 0 0 2px rgb(24 144 255 / 0%) !important;
}

:deep(.ant-input-number) {
  width: 100% !important;
  border: none !important;
  box-shadow: 0 0 0 2px rgb(24 144 255 / 0%) !important;
}

:deep(.ant-row) {
  margin-right: 0 !important;
  margin-left: 0 !important;
}

:deep(.ant-col) {
  padding-left: 0 !important;

  &:last-child {
    padding-right: 0 !important;
  }
}

:deep(.ant-col-6) {
  &:nth-child(4n) {
    padding-right: 0 !important;
  }
}
</style>
