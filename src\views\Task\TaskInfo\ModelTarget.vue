<script setup lang="ts">
  import { getModelMonitor } from '@/api/model';
  import type { EChartsType } from 'echarts';
  import { init } from 'echarts';
  import { onUnmounted, reactive, ref, watch, onMounted } from 'vue';
  import { useRoute } from 'vue-router';
  import { debounce } from 'lodash-es';

  const props = defineProps<{ activeKey: number }>();
  const route = useRoute();
  const EvalEcharts = ref<HTMLDivElement[]>([]);
  const TrainEcharts = ref<HTMLDivElement[]>([]);
  const timer = ref();
  const chartInstances: EChartsType[] = reactive([]);
  interface IState {
    activeKey: number[];
    train_options: IMonitorOptions[];
    eval_options: IMonitorOptions[];
  }
  interface IMonitorOptions {
    titleText: string;
    key: string;
    seriesData: [];
    xAxisData: [];
    yAxisUnit?: string;
  }
  const state = reactive<IState>({
    activeKey: [1, 2],
    train_options: [
      {
        titleText: 'train/loss',
        key: 'loss',
        seriesData: [],
        xAxisData: [],
      },
      {
        titleText: 'train/learning_rate',
        key: 'learning_rate',
        seriesData: [],
        xAxisData: [],
      },
      {
        titleText: 'train/grad_norm',
        key: 'grad_norm',
        seriesData: [],
        xAxisData: [],
      },
      {
        titleText: 'train/epoch',
        key: 'epoch',
        seriesData: [],
        xAxisData: [],
      },
    ],
    eval_options: [
      {
        titleText: 'eval/steps_per_second',
        key: 'eval_steps_per_second',
        seriesData: [],
        xAxisData: [],
      },
      {
        titleText: 'eval/samples_per_second',
        key: 'eval_samples_per_second',
        seriesData: [],
        xAxisData: [],
      },
      {
        titleText: 'eval/runtime',
        key: 'eval_runtime',
        seriesData: [],
        xAxisData: [],
      },
      {
        titleText: 'eval/loss',
        key: 'eval_loss',
        seriesData: [],
        xAxisData: [],
      },
    ],
  });

  const renderEcharts = (
    instanceRef: HTMLDivElement[],
    options: IMonitorOptions[],
    data: Record<string, string>[],
    config: { epochs: number; steps: number },
  ) => {
    if (instanceRef) {
      const { epochs, steps } = config;
      instanceRef.map((e, i: number) => {
        const { titleText, yAxisUnit, key } = options[i];
        const yData: number | string[] = [];
        const xData: number | string[] = [];
        data.forEach((item, index: number) => {
          // @ts-expect-error
          const res = !epochs || !steps ? index + 1 : ((item.epoch / epochs) * steps).toFixed(0);
          yData.push(item[key]);
          xData.push(res as string);
        });
        if (yData.length) {
          const echartInstance = init(e);
          chartInstances.push(echartInstance);
          echartInstance.setOption({
            xAxis: {
              type: 'category',
              name: 'train/global_step',
              nameLocation: 'middle',
              nameGap: 30,
              data: xData,
            },
            yAxis: {
              type: 'value',
              axisLabel: {
                formatter: (value: string) => `${value} ${yAxisUnit || ''}`,
              },
            },
            title: {
              text: titleText,
              left: 'center',
              top: 10,
              textStyle: {},
            },
            tooltip: {
              show: true,
              trigger: 'axis',
            },
            series: [
              {
                data: yData,
                type: 'line',
              },
            ],
          });
          echartInstance.resize();
        }
      });
    }
  };

  const fetchDataRR = () => {
    timer.value = setInterval(async () => {
      fetchData();
    }, 10000);
  };
  const fetchData = async () => {
    const { taskid } = route.query;
    const res = await getModelMonitor(taskid as string);
    const { eval_monitor_metric, monitor_metric, epochs, steps } = res;
    const config = { epochs, steps };
    renderEcharts(EvalEcharts.value, state.eval_options, eval_monitor_metric || [], config);
    renderEcharts(TrainEcharts.value, state.train_options, monitor_metric || [], config);
  };

  watch(
    () => props.activeKey,
    (key) => {
      if (key !== 4) {
        clearInterval(timer.value);
      } else {
        fetchData();
        fetchDataRR();
      }
    },
    { deep: true, immediate: true },
  );

  const resizeObserver = new ResizeObserver(
    // @ts-expect-error
    debounce((entries) => {
      for (const _ of entries) {
        chartInstances.forEach((item: EChartsType) => {
          item.resize();
        });
      }
    }, 300),
  );

  onMounted(() => {
    resizeObserver.observe(document.body);
  });

  onUnmounted(() => {
    clearInterval(timer.value);
  });
</script>

<template>
  <div class="model-target">
    <a-collapse v-model:active-key="state.activeKey" ghost>
      <a-collapse-panel :key="1" header="Train">
        <div v-for="(_, i) in state.train_options" :key="i" ref="TrainEcharts" class="echart-box">
          <a-empty style="margin-top: 15%" />
        </div>
      </a-collapse-panel>
      <a-collapse-panel :key="2" header="Eval">
        <div v-for="(_, i) in state.eval_options" :key="i" ref="EvalEcharts" class="echart-box">
          <a-empty style="margin-top: 15%" />
        </div>
      </a-collapse-panel>
    </a-collapse>
  </div>
</template>

<style lang="less" scoped>
  .model-target {
    height: 100%;
  }

  :deep(.ant-collapse) {
    height: 100%;
    overflow-y: scroll;
    .overflow-scroll;
  }

  :deep(.ant-collapse-content-box) {
    display: flex;
    flex-wrap: wrap;

    > div {
      width: calc((100% - 20px) / 2);
      height: 350px;
      margin-bottom: 20px;
    }

    > div:nth-child(2n + 1) {
      margin-right: 20px;
    }
  }

  .echart-box {
    border: 1px solid #ccc;
  }
</style>
