<script setup lang="ts">
  import QuestionTip from './questionTip.vue';
  import { useAnswer } from '@/stores/anster';
  import { useRouter } from 'vue-router';

  const router = useRouter();
  const { state } = useAnswer();

  const emits = defineEmits<{
    (event: 'changeStep'): void;
  }>();

  function handleBackClass() {
    router.push(`/ClassRoom/${router.currentRoute.value.query.modelId}`);
  }

  function handleReAnswer() {
    emits('changeStep');
  }
</script>

<template>
  <div class="w-full h-full give-up-answer">
    <h2>课堂测验未完成</h2>
    <p>你已放弃本次答题，你可以点击下方按钮重新开始答题或者回到上课模式进行学习。</p>
    <div class="h-7.5 w-full flex items-center mt-4">
      已完成{{ state.questionList.length }}题，答对<span style="color: #4ecc10; margin: 0 2px">{{
        state.questionList.filter((it) => it.isRight).length
      }}</span
      >题，答错<span style="color: #ff3c3c; margin: 0 2px">{{
        state.questionList.filter((it) => !it.isRight).length
      }}</span
      >题
    </div>
    <div class="flex mt-22px">
      <QuestionTip :list="state.questionList" class="flex" />
    </div>
    <div class="mt-58px">
      <a-button class="h-10 mr-5" @click="handleBackClass">回到上课模式</a-button>
      <a-button type="primary" class="h-10" @click="handleReAnswer">恢复答题</a-button>
    </div>
  </div>
</template>

<style lang="less" scoped>
  .give-up-answer {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 0 240px;
    h2 {
      font-size: 24px;
      margin-bottom: 16px;
      font-weight: 600;
      color: #18191a;
    }
    p {
      font-weight: 400;
      font-size: 16px;
      color: #636466;
      line-height: 30px;
      margin: 0;
      text-align: left;
      width: 100%;
    }
    div {
      font-size: 16px;
      color: #17181a;
    }
  }
</style>
