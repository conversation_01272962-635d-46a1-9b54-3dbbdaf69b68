import { ref } from 'vue';
import { defineStore } from 'pinia';

export interface IQuestion {
  id: number; // 题目id
  rightAnswer: any; // 正确答案
  selectAnswer: any; // 选中的答案
  isRight: boolean; // 是否选择正确
}

interface IState {
  questionList: Array<IQuestion>; // 已做答题目列表
  currentIndex: number; // 当前题目下标
  allQuestionList: Array<any>; // 全部题目列表
  inPreBack: boolean; // 是否在预览已做答的题目
}

export const useAnswer = defineStore('answer', () => {
  const state = ref<IState>({
    questionList: [],
    allQuestionList: [],
    currentIndex: 0,
    inPreBack: false,
  });
  const pushAnswerQuestion = (question: IQuestion) => {
    state.value.questionList.push(question);
  };
  const setAllQuestionList = (data: any[]) => (state.value.allQuestionList = data);
  const nextAnswer = () => (state.value.currentIndex = state.value.currentIndex + 1);
  const jumpAnswer = (index: number) => {
    state.value.currentIndex = index;
  };
  const setInPreBack = (val: boolean) => {
    state.value.inPreBack = val;
  };
  const resetAnswerStore = () => {
    state.value.questionList = [];
    // state.value.allQuestionList = [];
    state.value.currentIndex = 0;
    state.value.inPreBack = false;
  };
  return { state, pushAnswerQuestion, nextAnswer, jumpAnswer, setAllQuestionList, setInPreBack, resetAnswerStore };
});
