import pluginVue from 'eslint-plugin-vue';
import { defineConfigWithVueTs, vueTsConfigs } from '@vue/eslint-config-typescript';
import pluginVitest from '@vitest/eslint-plugin';
import skipFormatting from '@vue/eslint-config-prettier/skip-formatting';

export default defineConfigWithVueTs(
  {
    name: 'app/files-to-lint',
    files: ['src/**/*.{ts,mts,tsx,vue}'],
  },

  {
    name: 'app/files-to-ignore',
    ignores: [
      'dist',
      'html/**',
      'coverage/**',
      'node_modules/**',
      'src/assets/font/*.js',
      'public/**',
      '*.config.ts',
      '**/*.min.js',
      '**/*.d.ts',
      'cypress/**',
      'e2e/**',
      'playwright.config.ts',
      'test-setup.ts',
    ],
  },

  pluginVue.configs['flat/recommended'], // 更严格的 Vue 规则
  vueTsConfigs.recommended,

  {
    ...pluginVitest.configs.recommended,
    files: ['src/**/__tests__/*'],
  },

  {
    rules: {
      // TypeScript 相关规则
      '@typescript-eslint/no-explicit-any': 'off',
      '@typescript-eslint/no-unused-vars': ['warn', { argsIgnorePattern: '^_' }],
      '@typescript-eslint/consistent-type-imports': 'error',
      // 'vue/no-v-html': [
      //   'error',
      //   {
      //     allow: {
      //       sanitized: true, // 允许通过DOMPurify处理的内容
      //     },
      //   },
      // ],
      'vue/multi-word-component-names': 'off',
      'vue/require-default-prop': 'off',
      '@typescript-eslint/ban-ts-comment': 'off',
      '@typescript-eslint/no-unused-expressions': 'off',
      '@typescript-eslint/no-unsafe-function-type': 'off',
      'prefer-const': 'off',
      // '@cspell/spellcheck': ['error'],
    },
  },
  skipFormatting,
);
