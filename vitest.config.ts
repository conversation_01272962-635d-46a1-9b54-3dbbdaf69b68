import { fileURLToPath } from 'node:url';
import { mergeConfig, defineConfig, configDefaults } from 'vitest/config';
import { resolve } from 'node:path'; // 确保正确解析路径
import viteConfig from './vite.config';

export default mergeConfig(
  viteConfig,
  defineConfig({
    test: {
      setupFiles: ['./test-setup.ts'],
      reporters: ['default', 'html'],
      coverage: {
        reporter: ['text', 'json', 'html', 'lcov'],
        include: ['src/utils/'],
        reportsDirectory: 'coverage/unit',
      },
      environment: 'jsdom',
      exclude: [...configDefaults.exclude, 'e2e/**'],
      root: fileURLToPath(new URL('./', import.meta.url)),
    },
  }),
);
