<script setup lang="ts">
  import { ref, onMounted } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { getCourse } from '@/api/classRoom';
  import { message } from 'ant-design-vue';
  import { LeftOutlined } from '@ant-design/icons-vue';
  import playIcon from '@/assets/image/base/pictures/play1.png';
  import answerIcon from '@/assets/image/base/pictures/answerIcon.png';
  import onplayIcon from '@/assets/image/base/pictures/播放2.png';
  import play from '@/assets/image/base/pictures/播放1.png';
  import pause from '@/assets/image/base/pictures/暂停1.png';

  const route = useRoute();
  const router = useRouter();
  const courseId = ref(route.params.id);
  const courseDetails = ref<any>(null);
  const loading = ref<boolean>(false);
  const currentDetailIndex = ref(0);
  const isPlaying = ref(false);
  const currentVideo = ref<HTMLVideoElement | null>(null);

  const fetchCourseDetails = async () => {
    try {
      loading.value = true;
      const res = await getCourse({ id: courseId.value });
      if (res) {
        // 处理视频URL
        if (res.images && Array.isArray(res.images)) {
          const updatedImages = res.images.map((image: any, index: number) => ({
            ...image,
            video_url: res.images[index].videos.video_url,
            video_srt: res.images[index].videos.video_srt,
          }));
          courseDetails.value = { ...res, images: updatedImages };
        } else {
          courseDetails.value = res;
        }
        console.log(courseDetails.value, 'courseDetails');
      } else {
        message.error('获取课程详情失败');
      }
    } catch (error) {
      message.error('获取课程详情失败');
      console.error('Error fetching course details:', error);
    } finally {
      loading.value = false;
    }
  };

  const goBack = () => {
    router.push('/ClassRoom');
  };

  const changeImage = (index: number) => {
    if (currentDetailIndex.value === index) {
      // If clicking the same image, toggle video play/pause
      const video = document.querySelector('.digital-human-video') as HTMLVideoElement;
      if (video) {
        if (video.paused) {
          video.play();
          isPlaying.value = true;
        } else {
          video.pause();
          isPlaying.value = false;
        }
      }
    } else {
      // If clicking a different image, switch to it and start playing
      currentDetailIndex.value = index;
      // Small timeout to ensure the new video is loaded
      setTimeout(() => {
        const video = document.querySelector('.digital-human-video') as HTMLVideoElement;
        if (video) {
          video.play();
          isPlaying.value = true;
          currentVideo.value = video;
        }
      }, 100);
    }
  };

  const togglePlay = () => {
    const video = document.querySelector('.digital-human-video') as HTMLVideoElement;
    if (!video) return;

    if (!isPlaying.value) {
      video.play();
      isPlaying.value = true;
      currentVideo.value = video;
    } else {
      video.pause();
      isPlaying.value = false;
    }
  };

  const handleVideoEnded = () => {
    // When current video ends, play next scene if available
    if (courseDetails.value?.images && currentDetailIndex.value < courseDetails.value.images.length - 1) {
      changeImage(currentDetailIndex.value + 1);
    } else {
      // If it's the last video, stop playing
      isPlaying.value = false;
    }
  };

  const handleAnswer = () => {
    router.push(`/ClassRoom/answer?modelId=${courseId.value}`);
  };

  onMounted(() => {
    fetchCourseDetails();
  });
</script>

<template>
  <div class="course-details-page">
    <!-- 顶部导航 -->
    <div class="nav-header">
      <div class="back-button" @click="goBack">
        <LeftOutlined />
        <span>{{ courseDetails?.name || '课程详情' }}</span>
      </div>
      <div class="actions">
        <a-button @click="handleAnswer">
          <template #icon>
            <img :src="answerIcon" alt="answerIcon" style="width: 20px; height: 20px; margin-right: 4px" />
          </template>
          答疑
        </a-button>
      </div>
    </div>

    <!-- 主要内容区 -->
    <div v-if="courseDetails && !loading" class="main-content">
      <!-- 左侧内容区 -->
      <div
        v-if="courseDetails.images && courseDetails.images.length"
        class="content-left"
        :style="{
          backgroundImage: `url(${courseDetails?.images?.[currentDetailIndex]?.image_url})`,
          backgroundSize: 'contain',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat',
        }"
      ></div>

      <div class="content-right">
        <div class="digital-human">
          <video
            v-if="courseDetails.images[currentDetailIndex].video_url"
            :src="courseDetails.images[currentDetailIndex].video_url"
            class="digital-human-video"
            @ended="handleVideoEnded"
          ></video>
        </div>
      </div>
    </div>
    <div class="palyBtn" @click="togglePlay">
      <img :src="isPlaying ? onplayIcon : playIcon" alt="icon" style="cursor: pointer" />
    </div>
    <div class="footer-content">
      <!-- 缩略图列表 -->
      <div v-if="courseDetails?.images && courseDetails?.images?.length > 1">
        <div
          v-for="(item, index) in courseDetails.images"
          :key="index"
          class="footer-item"
          :class="{ active: currentDetailIndex === index }"
          :style="{
            backgroundImage: `url(${item.image_url})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            border: currentDetailIndex === index ? '2px solid #1777FF' : '1px solid #edeff2',
          }"
          @click="changeImage(index)"
        >
          <!-- 添加hover时显示的播放/暂停图标 -->
          <div class="hover-play-icon">
            <img
              :src="currentDetailIndex === index && isPlaying ? play : pause"
              alt="icon"
              @click.stop="changeImage(index)"
            />
          </div>
        </div>
      </div>
    </div>
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-state">
      <a-spin size="large" />
    </div>
  </div>
</template>

<style scoped lang="less">
  .course-details-page {
    height: calc(100vh - 360px);
    background: #f7f8fa;
    padding: 0;

    .nav-header {
      height: 60px;
      background: #fff;
      padding: 0 24px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);

      .back-button {
        display: flex;
        align-items: center;
        cursor: pointer;
        font-size: 16px;

        .anticon {
          margin-right: 8px;
        }
      }
    }

    .main-content {
      height: 505px;
      display: flex;
      justify-content: space-around;
      border-radius: 6px;
      overflow: hidden;
      margin: 30px 166px 0 166px;
      margin-bottom: -40px;
      background: #fff;

      .content-left {
        width: 75%;
        height: 465px;
        background-repeat: no-repeat;
        background-size: contain;
        background-position: center;
        border-radius: 6px 0px 0px 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #fff;
      }

      .content-right {
        width: 25%;
        background: #fff;
        border-radius: 8px;
        height: 465px;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;

        .digital-human {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;

          .digital-human-video {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
      }
    }

    .palyBtn {
      width: 100%;
      display: flex;
      justify-content: center;
      padding: 20px 62px;
      background: #f7f8fa;

      img {
        width: 48px;
        height: 48px;
        cursor: pointer;
        transition: all 0.3s;

        &:hover {
          transform: scale(1.1);
        }
      }
    }

    .footer-content {
      width: 100%;
      display: flex;
      overflow-x: auto;
      justify-content: flex-start;
      padding: 20px;
      background: #ffffff;
      white-space: nowrap;

      // 设置滚动条样式
      &::-webkit-scrollbar {
        height: 6px;
        background-color: #f5f5f5;
      }

      &::-webkit-scrollbar-thumb {
        background-color: #ddd;
        border-radius: 3px;
      }

      &::-webkit-scrollbar-track {
        background-color: #f5f5f5;
        border-radius: 3px;
      }

      .footer-item {
        flex: 0 0 138px;
        height: 78px;
        min-width: 138px;
        margin-right: 12px;
        border-radius: 6px;
        background-repeat: no-repeat;
        cursor: pointer;
        transition: all 0.3s;
        display: inline-block;
        position: relative; // 添加相对定位

        // 添加hover图标样式
        .hover-play-icon {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: rgba(0, 0, 0, 0.5);
          display: none;
          justify-content: center;
          align-items: center;
          border-radius: 6px;

          img {
            width: 24px;
            height: 24px;
            transition: transform 0.2s;
            cursor: pointer;

            &:hover {
              transform: scale(1.1);
            }
          }
        }

        &:hover {
          .hover-play-icon {
            display: flex;
          }
        }

        &:last-child {
          margin-right: 0;
        }

        &.active {
          border: 2px solid #1777ff;
        }
      }
    }

    .loading-state {
      height: calc(100vh - 64px);
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
</style>
