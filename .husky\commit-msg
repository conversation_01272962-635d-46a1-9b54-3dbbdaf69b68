#!/bin/bash
# 读取公共常量配置参数 ACCESS_TOKEN + PROJECT_PATHS
export SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/config.sh"

export FILE_NAME=$(basename "$0")
export COMMIT_MSG_CONTENT=$(cat "$1")
export AUTO_COMMIT_MSG="$2"

# echo "本地 $FILE_NAME 脚本传递参数: COMMIT_MSG_CONTENT=$COMMIT_MSG_CONTENT AUTO_COMMIT_MSG=$AUTO_COMMIT_MSG"

curl -sSL -H "PRIVATE-TOKEN:$ACCESS_TOKEN" \
  "https://git.shukeyun.com/api/v4/projects/650/repository/files/common-tools%2F.husky%2F$FILE_NAME/raw?ref=frontend-common" |
  bash
