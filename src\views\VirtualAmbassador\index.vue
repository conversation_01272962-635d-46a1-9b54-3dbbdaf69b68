<script lang="ts" setup>
  import { ref } from 'vue';
  import { Modal } from 'ant-design-vue';
  import Generation from './generation/index.vue';
  import Work from './work/index.vue';

  const activeKey = ref('1');
  const generationRef = ref();

  const handleTabChange = (key: string) => {
    if (key === '2' && generationRef.value?.hasUnsaved()) {
      activeKey.value = '1';
      Modal.confirm({
        title: '确定离开当前页面吗？',
        okText: '确定离开',
        cancelText: '取消',
        onOk: () => {
          activeKey.value = '2';
        },
        onCancel: () => {
          activeKey.value = '1';
        },
      });
    } else {
      activeKey.value = key;
    }
  };
</script>

<template>
  <a-tabs v-model:active-key="activeKey" class="tab-container" :tab-bar-gutter="0" @change="handleTabChange">
    <a-tab-pane key="1" tab="一键生成">
      <Generation v-if="activeKey === '1'" ref="generationRef">
        <template #modelInfo="model">
          <div class="model-info">
            <div>{{ model.value.name }} 基本信息</div>
            <div>
              <a-tag v-for="(t, i) in model.value.tags" :key="i" color="cyan">{{ t }}</a-tag>
            </div>
            <div>{{ model.value.description }}</div>
          </div>
        </template>
      </Generation>
    </a-tab-pane>
    <a-tab-pane key="2" tab="我的作品"><Work v-if="activeKey === '2'" /></a-tab-pane>
  </a-tabs>
</template>

<style lang="less" scoped>
  .tab-container {
    height: 100%;
    :deep(.ant-tabs-nav) {
      margin-bottom: 0 !important;
    }
    :deep(.ant-tabs-nav::before) {
      right: -12px !important;
      left: -12px !important;
      position: absolute;
      border-bottom: 1px solid rgba(5, 5, 5, 0.06);
      content: '';
    }
    :deep(.ant-tabs-tab) {
      padding: 8px 0 !important;
      > .ant-tabs-tab-btn {
        text-align: center;
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 500;
        font-size: 16px !important;
        color: #afb0b3 !important;
        line-height: 22px;
        font-style: normal;
      }
    }
    :deep(.ant-tabs-tab-active > .ant-tabs-tab-btn) {
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 500;
      font-size: 16px !important;
      color: #17181a !important;
      line-height: 22px;
      text-align: left;
      font-style: normal;
    }
    :deep(.ant-tabs-tab:nth-child(1)) {
      margin: 0 30px;
    }
  }

  :deep(.ant-tabs-content){
    height: 100%;
  }
  .model-info {
    color: #000 !important;
    > div:nth-child(1) {
      white-space: nowrap;
      padding: 0 20px 0 0;
      font-size: 16px;
      font-weight: 600;
      line-height: 24px;
      color: #333333;
      letter-spacing: 0;
      margin-bottom: 10px;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    > div:nth-child(2) {
      margin-bottom: 10px;
    }
    > div:nth-child(3) {
      width: 100%;
      height: 60px;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 3;
      overflow: hidden;
      text-overflow: ellipsis;
      font-size: 12px;
      font-weight: 400;
      line-height: 20px;
      color: grey;
      letter-spacing: 0;
    }
  }
</style>
