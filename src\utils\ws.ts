interface IMsg {
  code: number;
  msg: string;
  data: string;
  answer: string;
  isFinal: boolean;
}

interface ISendMsg {
  wav_base64?: string;
  isLast?: boolean;
  isStart?: boolean;
  block_video?: boolean;
  res?: string;
}
export class WebSocketClient {
  private url = '';
  private socket: WebSocket | null = null;
  // #重连记数
  private reconnectCount = 0;
  // #最大重连数
  private maxReconnectCount = 5;
  // #重连间隔
  private reconnectInterval = 10000; // 10 seconds
  // #心跳间隔
  private heartbeatInterval = 1000 * 20;
  // #心跳计时器id
  private heartbeatTimer?: number | undefined;
  // #是否建立连接
  private isConnected = false;
  // // #收到消息回调
  private onMsg = (_data: IMsg): void => {};
  // #重新连接回调
  private onCallback: Function | null = null;

  constructor(url: string, onMsg: (data: IMsg) => void) {
    this.url = url;
    this.onMsg = onMsg;
  }

  // 消息发送
  public send(message: ISendMsg): void {
    if (this.socket && this.socket.readyState === WebSocket.OPEN) {
      this.socket.send(JSON.stringify(message));
    } else {
      console.log('WebSocket未连接，发送消息失败[send]... ' + this.url);
    }
  }

  // !初始化连接
  public connect(): void {
    if (this.socket && this.socket.readyState === WebSocket.OPEN) {
      return;
    }

    this.socket = new WebSocket(this.url);

    console.log(`正在连接[connect]...    ${this.url}`);

    // websocket连接成功
    this.socket.onopen = () => {
      this.isConnected = true;

      this.reconnectCount = 0;

      this.startHeartbeat();

      if (this.onCallback) {
        this.onCallback();
        this.onCallback = null;
      }
      console.log(`连接成功[onopen]...     ${this.url}`);
    };

    this.socket.onmessage = (data: MessageEvent<string>) => {
      const message = JSON.parse(data.data || '{}');
      if (message.type !== 'pong') {
        this.onMsg(message);
      }
    };

    this.socket.onclose = (e) => {
      this.isConnected = false;
      console.log(`连接断开[onclose]...    ${this.url}`, e);

      if (e.code !== 1000) {
        this.handleReconnect();
      }
    };

    this.socket.onerror = (event) => {
      console.log(`连接异常[onerror]...    ${this.url}`, event);
    };
  }

  // # 主动断开重连
  public resetConnection(callback?: Function): void {
    this.close();
    if (callback) {
      this.onCallback = callback;
    }
    this.connect();
  }

  // > 断网重连逻辑
  private handleReconnect(): void {
    if (this.reconnectCount < this.maxReconnectCount) {
      this.reconnectCount += 1;
      console.log(`尝试重连[handleReconnect]... (${this.reconnectCount}/${this.maxReconnectCount})--${this.url}`);
      if (this.reconnectCount === 1) {
        // 首次自立即重连
        this.connect();
      } else {
        setTimeout(() => {
          this.connect();
        }, this.reconnectInterval);
      }
    } else {
      this.close();
      console.log(`最大重连失败，终止重连[handleReconnect]...  ${this.url}`);
    }
  }

  // >关闭连接
  public close(): void {
    if (this.socket) {
      this.socket.close();
      this.socket = null;
    }
    this.closeHeartbeat();
  }

  // >开始心跳检测 -> 定时发送心跳消息
  private startHeartbeat(): void {
    if (!this.isConnected) return;

    this.heartbeatTimer = setInterval(() => {
      if (this.socket) {
        this.socket.send(JSON.stringify({ type: 'ping' }));
      } else {
        console.log(`WebSocket未连接[startHeartbeat]...    ${this.url}`);
      }
    }, this.heartbeatInterval);
  }

  // >关闭心跳
  private closeHeartbeat(): void {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = undefined;
    }
  }
}
