import { ref } from 'vue';

type ThrottleFn<TArgs extends unknown[]> = (...args: TArgs) => void;

export function useThrottle<TArgs extends unknown[]>(fn: (...args: TArgs) => void, delay: number): ThrottleFn<TArgs> {
  const timer = ref<ReturnType<typeof setTimeout> | null>(null);

  return (...args: TArgs) => {
    if (!timer.value) {
      timer.value = setTimeout(() => {
        timer.value = null;
      }, delay);

      fn(...args);
    }
  };
}
