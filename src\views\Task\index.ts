export const TrainStatus = [
  { label: '创建中', value: 'creating', color: 'orange' },
  { label: '运行中', value: 'running', color: 'orange' },
  { label: '创建失败', value: 'create_failed', color: 'red' },
  { label: '停止中', value: 'stopping', color: 'orange' },
  { label: '已停止', value: 'stopped', color: 'green' },
  { label: '已完成', value: 'completed', color: 'green' },
  { label: '训练失败', value: 'training_failed', color: 'red' },
];

export const STATE_MAP = {
  CREATING: 'creating', // 创建中
  RUNNING: 'running', // 运行中
  CREATE_FAILED: 'create_failed', // 创建失败
  STOPPING: 'stopping', // 停止中
  STOPPED: 'stopped', // 已停止
  COMPLETED: 'completed', // 已完成
  TRAINING_FAILED: 'training_failed', // 训练失败
}
export const DATASET_TYPE_MAP: Record<string, string> = {
  'system': '公开数据集',
  'private': '自研数据集'
}

export const DATASET_MAP: Record<string, string> = {
  'identity': '身份意识微调数据集',
  'travel_plan': '环球数科文旅垂类数据集'
}