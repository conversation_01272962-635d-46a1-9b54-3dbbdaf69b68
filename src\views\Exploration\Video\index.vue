<script setup lang="ts">
  import { nextTick, reactive, ref } from 'vue';
  import { LoadingOutlined, Loading3QuartersOutlined, QuestionCircleFilled } from '@ant-design/icons-vue';
  import { message } from 'ant-design-vue';

  const listRef = ref<any>(null);
  const state = reactive<any>({
    formState: {},

    open: false,
    switchover: false,
    activeKey: 1,
    selsecModel: 'llm-blender/gen_fuser_3b',
  });

  const switchover = () => {
    state.switchover = false;
  };

  const send = () => {};
</script>

<template>
  <div class="exploration">
    <div class="left overflow-scroll"></div>
    <div class="right">
      <div>
        <div>{{ 'Qwen2.5-vl-72b-instruct' }}</div>
        <div>
          <a-button type="link" @click="state.open = true">设置</a-button>
          <a-button type="link" @click="state.switchover = true">切换</a-button>
        </div>
      </div>
      <div class="overflow-scroll" ref="listRef">
        <div>video</div>
      </div>
      <div>
        <a-textarea
          v-model:value="state.question"
          :auto-size="{ minRows: 2, maxRows: 5 }"
          placeholder="给模型发送消息。"
        />
        <a-button type="primary" @click="send">发送</a-button>
      </div>
    </div>

    <a-drawer :width="400" title="显示设置" placement="right" :open="state.open" @close="state.open = false">
      <template #extra> </template>
      <a-form layout="vertical" autocomplete="off">
        <a-form-item label="视频比例">
          <a-radio-group v-model:value="state.formState.prompt">
            <a-radio-button :value="i" v-for="(n, i) in ['1_1','16_9','9_16']" :key="i">
              <div :class="`l${n.replace('_', '-')}`"></div>
              <div>{{ n.replace('_', ':') }}</div>
            </a-radio-button>
          </a-radio-group>
        </a-form-item>
        <a-form-item>
          <template #label>
            <span style="margin-right: 10px">提示词</span>
            <a-tooltip>
              <template #title>?????</template>
              <question-circle-filled class="span-mg-left" />
            </a-tooltip>
          </template>
          <a-textarea
            v-model:value="state.formState.prompt"
            :auto-size="{ minRows: 2, maxRows: 5 }"
            placeholder="请输入Prompt"
          />
        </a-form-item>
        <a-form-item>
          <template #label>
            <span style="margin-right: 10px">随机种子</span>
            <a-tooltip>
              <template #title>?????</template>
              <question-circle-filled class="span-mg-left" />
            </a-tooltip>
          </template>
          <a-input v-model:value="state.formState.prompt">
            <template #addonAfter>
              <Loading3QuartersOutlined />
            </template>
          </a-input>
        </a-form-item>


        <a-form-item v-if="false">
          <template #label>
            <span style="margin-right: 10px">图片数量</span>
            <a-tooltip>
              <template #title>?????</template>
              <question-circle-filled class="span-mg-left" />
            </a-tooltip>
          </template>
          <a-slider v-model:value="state.formState.prompt" :min="1" :max="20" />
          <a-input-number v-model:value="state.formState.prompt" :min="1" :max="20" class="right-input" />
        </a-form-item>
        <a-form-item v-if="false">
          <template #label>
            <span style="margin-right: 10px">采样步数</span>
            <a-tooltip>
              <template #title>?????</template>
              <question-circle-filled class="span-mg-left" />
            </a-tooltip>
          </template>
          <a-slider v-model:value="state.formState.prompt" :min="1" :max="20" />
          <a-input-number v-model:value="state.formState.prompt" :min="1" :max="20" class="right-input" />
        </a-form-item>
        <a-form-item v-if="false">
          <template #label>
            <span style="margin-right: 10px">引导比例</span>
            <a-tooltip>
              <template #title>?????</template>
              <question-circle-filled class="span-mg-left" />
            </a-tooltip>
          </template>
          <a-slider v-model:value="state.formState.prompt" :min="1" :max="20" />
          <a-input-number v-model:value="state.formState.prompt" :min="1" :max="20" class="right-input" />
        </a-form-item>
      </a-form>
    </a-drawer>

    <a-modal centered :visible="state.switchover" :width="'40%'" @cancel="state.switchover = false">
      <template #title>
        <div class="title">
          <exclamation-circle-outlined class="title-icon" />
          <span style="margin-right: 10px">切换模型</span>
        </div>
      </template>
      <template #footer>
        <slot name="footer">
          <a-button @click="state.switchover = false">
            <span>取消</span>
          </a-button>
          <a-button type="primary" @click="switchover()">
            <span>{{ '立即体验' }}</span>
          </a-button>
        </slot>
      </template>
      <a-tabs v-model:active-key="state.activeKey">
        <a-tab-pane :key="1" tab="预置模型">
          <a-radio-group v-model:value="state.selsecModel">
            <a-radio value="llm-blender/gen_fuser_3b">llm-blender/gen_fuser_3b</a-radio>
            <a-radio value="llm-blender/gen_fuser_5b">llm-blender/gen_fuser_5b</a-radio>
          </a-radio-group>
        </a-tab-pane>
        <a-tab-pane :key="2" tab="我的模型">
          <a-radio-group v-model:value="state.selsecModel">
            <a-radio value="llm-blender/gen_fuser_3b">llm-blender/gen_fuser_3b</a-radio>
            <a-radio value="llm-blender/gen_fuser_5b">llm-blender/gen_fuser_5b</a-radio>
          </a-radio-group>
        </a-tab-pane>
      </a-tabs>
    </a-modal>
  </div>
</template>

<style lang="less" scoped>
  .exploration {
    display: flex;
    flex-direction: row;
    height: 100%;
    overflow: hidden;

    .left {
      width: 0%;
      // border: 1px solid;
    }

    .right {
      display: flex;
      flex: 1;
      flex-direction: column;

      > div:nth-child(1) {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 10px;
        cursor: pointer;
        background-color: rgb(204 204 204 / 50%);
        border: 1px solid rgb(204 204 204 / 20%);
        border-radius: 10px;

        > div:nth-child(2) {
          display: flex;
        }
      }

      > div:nth-child(2) {
        display: flex;
        flex: 1;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        margin: 20px 0;
        font-size: 30px;
      }

      > div:nth-child(3) {
        position: relative;

        > button {
          position: absolute;
          right: 10px;
          bottom: calc(50% - 32px / 2);
        }

        > div:last-child(1) {
          position: absolute;
          border: 1px solid #dad6d6;
          border-radius: 50%;
        }
      }
    }
  }

  :deep(.right-input) {
    position: absolute !important;
    top: -35px !important;
    right: 0 !important;
  }

  :deep(.ant-radio-group) {
    display: flex;
    flex-direction: column;

    > label {
      margin-bottom: 15px;
    }
  }

  :deep(.ant-radio-group-outline) {
    display: flex;
    flex-direction: row;

    > label {
      flex: 1;
      height: 100%;

      > span:nth-child(2) {
        display: flex;
        flex-direction: column;
        align-items: center;

        > div:nth-child(1) {
          width: 20px;
          height: 20px;
          margin-top: 10px;
          border: 1px dashed rgb(214 184 184 / 59.6%);
          border-radius: 15%;
        }

        .l1-2 {
          width: 10px !important;
        }

        .l3-2 {
          width: 30px !important;
          height: 20px !important;
        }

        .l3-4 {
          width: calc(20px / 4 * 3) !important;
          height: 20px !important;
        }

        .l16-9 {
          width: calc(20px / 9 * 16) !important;
          height: 20px !important;
        }

        .l9-16 {
          width: calc(20px / 16 * 9) !important;
          height: 20px !important;
        }
      }
    }
  }
</style>
