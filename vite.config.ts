import { fileURLToPath, URL } from 'node:url';

import { defineConfig } from 'vite';
import UnoCSS from 'unocss/vite';
import vue from '@vitejs/plugin-vue';
import vueJsx from '@vitejs/plugin-vue-jsx';
import { viteCommonjs } from '@originjs/vite-plugin-commonjs';
import { codeInspectorPlugin } from 'code-inspector-plugin';
import istanbul from 'vite-plugin-istanbul';

const isCoverageEnabled = process.env.COVERAGE === 'true';

// https://vite.dev/config/
export default defineConfig({
  base: './',
  envDir: './env',
  publicDir: 'public',
  plugins: [
    vue(),
    vueJsx(),
    UnoCSS(),
    viteCommonjs(),
    process.env.NODE_ENV === 'development' &&
      codeInspectorPlugin({
        bundler: 'vite',
        showSwitch: false,
      }),

    isCoverageEnabled &&
      istanbul({
        include: 'src/*',
        exclude: ['node_modules', 'test/', 'cypress/', 'dist/'],
        extension: ['.js', '.ts', '.tsx', '.vue'],
        cypress: true,
        requireEnv: false,
      }),
  ].filter(Boolean),
  build: {
    chunkSizeWarningLimit: 10240,
    sourcemap: true,
    minify: 'terser',
    terserOptions: {
      compress: true,
      keep_classnames: true,
      keep_fnames: true,
    },
  },
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
    },
  },
  css: {
    preprocessorOptions: {
      less: {
        // 添加 LESS 全局变量
        additionalData: '@import "./src/assets/css/values.less";',
      },
    },
  },
  server: {
    host: '0.0.0.0',
    port: 5173,
    watch: {
      ignored: ['**/coverage/**', '**/.nyc_output/**', '**/lcov-report/**'],
    },
    proxy: {
      '^/data/ai-platform-backend/api/v1': {
        target: `https://dev.shukeyun.com`,
        // target: `http://**************:8080`,
        changeOrigin: true,
        // rewrite: (path) => path.replace(/^\/data\/ai-platform-backend\/api\/v1/, '/api/v1')
        rewrite: (path) => path.replace(/^\/data\/ai-platform-backend\/api\/v1/, '/data/ai-platform-backend/api/v1'),
      },
      '^/data/ai-platform-backend/api/v2': {
        target: `https://dev.shukeyun.com`,
        // target: `http://**************:8080`,
        changeOrigin: true,
        // rewrite: (path) => path.replace(/^\/data\/ai-platform-backend\/api\/v1/, '/api/v1')
        rewrite: (path) => path.replace(/^\/data\/ai-platform-backend\/api\/v1/, '/data/ai-platform-backend/api/v2'),
      },
      '^/blender': {
        target: `http://************:8010/`,
        // target: `https://test-gcluster.shukeyun.com/algorithm/llm-blender`,
        changeOrigin: true,
        rewrite: (path) => path.replace('/blender/', ''),
      },
      '^/p2l': {
        // target: `http://************:6789/`,
        target: `https://gcluster.shukeyun.com/algorithm/ai-model-integration`,
        changeOrigin: true,
        rewrite: (path) => path.replace('/p2l/', ''),
      },
      '^/opsmonitor': {
        target: `https://opsmonitor.shukeyun.com`,
        changeOrigin: true,
        rewrite: (path) => path.replace('/opsmonitor/', ''),
      },
      '^/avatar': {
        // target: `http://************:8003/`,
        target: `https://dev-gcluster.shukeyun.com/algorithm`,
        changeOrigin: true,
        rewrite: (path) => {
          return path.replace('/avatar', '');
        },
      },
      '^/data/api': {
        target: `https://dev.shukeyun.com`,
        // target: `http://192.168.80.125:8000`,
        changeOrigin: true,
        // rewrite: (path) => path.replace(/^\/data\/api/, '')
        rewrite: (path) => path.replace(/^\/data\/api/, '/data/api'),
      },
    },
  },
});
