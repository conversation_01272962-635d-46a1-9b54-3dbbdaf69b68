import { test, expect } from '@playwright/test';

test('用户登录成功跳转到首页', async ({ page }) => {
  await page.goto('https://dev.shukeyun.com/cas/login/#/login?appId=ieCx4PxQExAFrJafek9g');

  // 填写账号密码（用测试账号）
  await page.fill('input[id="credential"]', '13530403623');
  await page.fill('input[id="secret"]', '123456');
  await page.click('div.btn:has-text("登录")');

  // 登录成功后，初始 URL 为 /#/?tk=xxx，然后再 JS 重定向或路由跳转到 #/model,需要等待一段时间
  await page.waitForFunction(() => location.hash.includes('model'), { timeout: 8000 });
  await expect(page).toHaveURL(/\/data\/ai-platform-frontend\/#\/model/);

  // 验证用户名是否正确显示
  await expect(page.locator('.layout-header')).toContainText('13530403623');
});
