<script setup lang="ts">
  import { fetchServiceList, fetchServiceListByUser } from '@/api/exploration';
  import { ref, reactive, watch, onMounted } from 'vue';
  import { CloseOutlined, InfoCircleOutlined } from '@ant-design/icons-vue';
  import { debounce } from '@/utils';
  import type { IIntegrationConfig, ICreateIntegration } from '@/interface/integration';
  import { checkIntegrationName, createIntegration } from '@/api/integration';
  import { message } from 'ant-design-vue';
  import type { Rule } from 'ant-design-vue/es/form';
  interface IProps {
    open: boolean;
    close: () => void;
  }
  interface IService {
    id: string;
    model_name: string;
    service_name: string;
  }
  const props = defineProps<IProps>();
  const emits = defineEmits<{
    (event: 'refresh'): void;
  }>();
  const modelRef = ref();
  const configRef = ref();
  const formState = reactive<{ name: string; type: string; svc_list: IService[] }>({
    name: '',
    type: 'p2l',
    svc_list: [],
  });

  const configState = reactive<IIntegrationConfig>({
    top_k: 2,
    sort_model: undefined,
    fusion_model: undefined,
  });
  const dataSource = reactive<{ manage: IService[]; user: IService[] }>({
    manage: [],
    user: [],
  });
  const models = ref<IService[]>([]);
  const services = ref<IService[]>([]);
  const selectModel = ref('');
  const service_mode = ref('manage');
  const serviceName = ref('');
  const radioStyle = reactive({
    display: 'flex',
    height: '30px',
    lineHeight: '30px',
  });

  const validatorName = async (_rule: Rule, value: string) => {
    if (value == '' || value.trim().length == 0) {
      return Promise.reject('请输入任务名称');
    }
    try {
      await checkIntegrationName(value.trim());
    } catch (e) {
      if (e === 'AlreadyExists') {
        return Promise.reject('该名称已存在，请重新命名');
      }
      return Promise.reject(e);
    }
    return Promise.resolve();
  };

  const onSearch = () => {
    const data: IService[] = selectModel.value
      ? models.value.filter((item) => item.model_name === selectModel.value)
      : models.value;
    services.value = serviceName.value ? data.filter((item) => item.service_name.includes(serviceName.value)) : data;
  };
  const debouncedSearch = debounce(onSearch);
  const confirmAdd = async () => {
    await modelRef.value.validateFields();
    if (configRef.value) {
      await configRef.value.validateFields();
    }
    // TODO 调接口]
    const params: ICreateIntegration = {
      ...formState,
      svc_id_list: formState.svc_list.map((item) => item.id),
    };
    if (formState.type === 'blender') {
      params.config = configState;
    }
    // @ts-expect-error
    delete params.type;
    // @ts-expect-error
    delete params.svc_list;
    await createIntegration(formState.type, params);
    message.success('已添加');
    handleClose();
    emits('refresh');
  };

  const hangleChange = (e: Event) => {
    // @ts-expect-error
    const checked = e.target.checked;
    // @ts-expect-error
    const item: IService = e.target.value;
    const index = formState.svc_list.findIndex((i) => i.id === item.id);
    if (checked) {
      index === -1 && formState.svc_list.push(item);
    } else {
      index > -1 && formState.svc_list.splice(index, 1);
    }
  };

  const handleClose = async () => {
    await modelRef.value.clearValidate();
    await modelRef.value.resetFields();
    props.close();
  };
  const handleDeleteSelectModel = (index: number) => {
    formState.svc_list.splice(index, 1);
  };
  onMounted(async () => {
    const manage: IService[] = await fetchServiceList({ model_category: ['llm'] });
    const user: IService[] = await fetchServiceListByUser({ model_category: ['llm'] });
    Object.assign(dataSource, { manage, user });
    // 默认为p2l
    models.value = manage.filter((item) => /^qwen/.test(item.model_name.toLowerCase()));
    services.value = models.value;
  });

  watch(
    () => formState.type,
    (type) => {
      if (type) {
        models.value =
          type === 'p2l'
            ? dataSource.manage.filter((item) => /^qwen/.test(item.model_name.toLowerCase()))
            : dataSource.manage;
        services.value = models.value;
        formState.svc_list = [];
      }
    },
    { deep: true, immediate: true },
  );

  watch(
    () => service_mode.value,
    (mode) => {
      if (mode) {
        const serviceMode = mode === 'manage' ? dataSource.manage : dataSource.user;
        models.value =
          formState.type === 'p2l'
            ? serviceMode.filter((item) => /^qwen/.test(item.model_name.toLowerCase()))
            : serviceMode;
        services.value = models.value;
        // 更换预制服务或我的服务时，重置当前选择的模型
        selectModel.value = '';
        formState.svc_list = [];
      }
    },
    { deep: true },
  );

  watch(
    () => selectModel.value,
    (select) => {
      if (select !== undefined || select !== null) {
        services.value = select ? models.value.filter((item) => item.model_name === select) : models.value;
      }
    },
    { deep: true },
  );

  defineExpose({ handleClose });
</script>

<template>
  <a-drawer
    :open="open"
    title="新建模型集成任务"
    placement="right"
    :width="650"
    :body-style="{ padding: '0 15px' }"
    @close="handleClose"
  >
    <template #footer>
      <div class="flex justify-end">
        <a-button @click="handleClose" class="mr-10px">取消</a-button>
        <a-button type="primary" @click="confirmAdd">确定</a-button>
      </div>
    </template>

    <a-form
      ref="modelRef"
      :model="formState"
      label-align="right"
      layout="vertical"
      :label-col="{ style: { width: '200px' } }"
    >
      <div class="textbefo">基本信息</div>
      <a-form-item label="任务名称" name="name" :rules="[{ required: true, validator: validatorName }]">
        <a-input v-model:value="formState.name" placeholder="请输入任务名称" show-count :maxlength="64"> </a-input>
      </a-form-item>
      <a-form-item label="集成方式" name="type" :rules="[{ required: true, message: '请选择' }]">
        <a-radio-group v-model:value="formState.type">
          <a-radio :style="radioStyle" value="p2l">
            <div>
              P2L<span class="text-12px text-#797979 ml-10px"
                >精准剖析用户提问，快速为不同部署服务评分排序，直接调用排名第一的优质模型给出精准答案。</span
              >
            </div>
          </a-radio>
          <a-radio :style="radioStyle" value="blender">
            <div>
              LLM-Blender<span class="text-12px text-#797979 ml-10px"
                >博采众长，巧妙地将多个部署服务所给出的答案加以融合优化，从而精心产出更优质、更全面且更令人满意的结果。</span
              >
            </div>
          </a-radio>
        </a-radio-group>
      </a-form-item>
      <div class="textbefo">集成服务</div>
      <a-form-item label="已选服务" name="svc_list" :rules="[{ required: true, message: '请选择' }]">
        <div v-if="formState.svc_list.length" class="select-box">
          <div v-for="(item, index) in formState.svc_list" :key="index">
            {{ item.service_name }}
            <CloseOutlined style="font-size: 12px; margin-left: 5px" @click="handleDeleteSelectModel(index)" />
          </div>
        </div>
        <div v-else>暂未选择服务</div>
      </a-form-item>
      <div class="flex justify-between mt-10px">
        <a-radio-group v-model:value="service_mode">
          <a-radio-button value="manage">预制服务</a-radio-button>
          <a-radio-button value="user">我的服务</a-radio-button>
        </a-radio-group>
        <a-input-search
          v-model:value="serviceName"
          allow-clear
          placeholder="搜索服务名称"
          style="width: 300px"
          @change="debouncedSearch"
        />
      </div>
      <div class="flex justify-between mt-10px">
        <div class="model-box">
          <div class="mb-10px">模型名称</div>
          <div v-if="[...new Set(models.map((model) => model.model_name))].length" class="h-150px overflow-scroll">
            <div class="model-item" :class="{ actived: selectModel === '' }" @click="selectModel = ''">全部模型</div>
            <div
              v-for="(item, index) in [...new Set(models.map((model) => model.model_name))]"
              :key="index"
              class="model-item"
              :class="{ actived: selectModel === item }"
              @click="selectModel = item"
            >
              {{ item }}
            </div>
          </div>
          <div v-else class="h-150px">
            <a-empty />
          </div>
        </div>
        <div class="service-box">
          <div class="mb-10px">服务名称</div>
          <div v-if="services.length" class="h-150px overflow-scroll">
            <a-checkbox-group style="width: 100%" v-model:value="formState.svc_list">
              <a-checkbox v-for="(item, index) in services" :key="index" :value="item" :style="radioStyle">{{
                item.service_name
              }}</a-checkbox>
            </a-checkbox-group>
          </div>
          <div v-else class="h-150px">
            <a-empty />
          </div>
        </div>
      </div>
    </a-form>
    <template v-if="formState.type === 'blender'">
      <a-form
        ref="configRef"
        :model="configState"
        label-align="right"
        layout="vertical"
        :label-col="{ style: { width: '200px' } }"
      >
        <div class="textbefo">排序策略</div>
        <a-form-item name="sort_model" :rules="[{ required: true, message: '请选择' }]">
          <template #label>
            <span style="margin-right: 10px">排序模型</span>
            <a-tooltip>
              <template #title>给不同答案打分排队的裁判，能比较各个答案的好坏，把它们按质量高低排好顺序 。</template>
              <InfoCircleOutlined class="span-mg-left" />
            </a-tooltip>
          </template>
          <a-select v-model:value="configState.sort_model" style="width: 100%" placeholder="请选择排序模型">
            <a-select-option value="Rank Model">Rank Model</a-select-option>
          </a-select>
        </a-form-item>

        <div class="textbefo">融合策略</div>
        <a-form-item name="top_k" :rules="[{ required: true, message: '请选择' }]">
          <template #label>
            <span style="margin-right: 10px">top_K</span>
            <a-tooltip>
              <template #title
                >K 是个数字，比如 3。top_K 就是从排好队的答案里，挑出前 K 个最好的答案，再把这几个答案 “揉”
                到一起，生成最终更好的回答。</template
              >
              <InfoCircleOutlined class="span-mg-left" />
            </a-tooltip>
          </template>
          <a-input-number
            :min="1"
            allowClear
            v-model:value="configState.top_k"
            style="width: 100%"
            placeholder="请选择融合个数"
          ></a-input-number>
        </a-form-item>

        <a-form-item name="fusion_model" :rules="[{ required: true, message: '请选择' }]">
          <template #label>
            <span style="margin-right: 10px">融合模型</span>
            <a-tooltip>
              <template #title>把选出来的几个好答案和问题混在一起，重新 “加工” 出一个更完美的答案。</template>
              <InfoCircleOutlined class="span-mg-left" />
            </a-tooltip>
          </template>
          <a-select v-model:value="configState.fusion_model" style="width: 100%" placeholder="请选择融合模型">
            <!-- <a-select-option value="llm-blender/gen_fuser_3b">llm-blender/gen_fuser_3b</a-select-option> -->
            <a-select-option value="lmsys/vicuna-7b-v1.5">lmsys/vicuna-7b-v1.5</a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
    </template>
  </a-drawer>
</template>

<style scoped lang="less">
  .select-box {
    display: flex;
    flex-wrap: wrap;
    > div {
      background-color: #eef3fe;
      padding: 5px;
      display: flex;
      justify-content: space-between;
      margin: 0 10px 10px 0;
      cursor: pointer;
    }
  }
  .model-box,
  .service-box {
    width: calc(50% - 5px);
    // height: 100px;
    border: 1px solid #ddd;
    padding: 10px;
  }
  .model-item {
    border-radius: 5px;
    border: 1px solid #ddd;
    padding: 5px;
    margin-bottom: 5px;
    cursor: pointer;
  }
  .actived {
    border-color: #1890ff;
    color: #1890ff;
    background-color: #eef3fe;
  }
</style>
