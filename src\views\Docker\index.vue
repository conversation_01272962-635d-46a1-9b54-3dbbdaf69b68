<!--
 * @Author: dengfusheng 
 * @Date: 2025-03-28 10:55:10
 * @LastEditTime: 2025-04-14 16:24:24
 * @LastEditors: <EMAIL>
 * @FilePath: \ai-platform-frontend\src\views\Docker\index.vue
 * @Description: 官方镜像
-->
<script setup lang="ts">
  import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue';
  import { CustomForm } from '@/components';
  import type { IFormItem, IPage } from '@/interface';
  import { TABLE_PAGINATION } from '@/json/common';
  import type { ColumnType, TablePaginationConfig } from 'ant-design-vue/es/table/interface';
  import { deleteDocker, dockerStatus, fetchDockerList } from '@/api/docker';
  import { message } from 'ant-design-vue';
  import type { Rule } from 'ant-design-vue/es/form';
  import { convertIsoTimeToLocalTime } from '@/utils/common';
  const copyFormRef = ref();
  const DEFAULT_SEARCHSTATE = {
    image_name: undefined,
  };
  const searchState = reactive({ ...DEFAULT_SEARCHSTATE });
  const formConfig: IFormItem[] = [
    {
      field: 'image_name',
      type: 'input',
      label: '镜像名称',
      placeholder: '请输入',
    },
  ];
  const columns: ColumnType[] = [
    { title: '镜像名', dataIndex: 'name', fixed: 'left', width: 120 },
    { title: '标签', dataIndex: 'tag' },
    { title: '框架', dataIndex: 'frame' },
    { title: 'Python版本', dataIndex: 'py_version' },
    { title: 'CUDA版本', dataIndex: 'cuda_version' },
    { title: '镜像大小（GIB）', dataIndex: 'image_size' },
    { title: '状态', dataIndex: 'status' },
    { title: '创建时间', dataIndex: 'created_at' },
    { title: '镜像描述', dataIndex: 'description' },
    { title: '操作', dataIndex: 'operation', width: 240, fixed: 'right' },
  ];

  interface IStateMap {
    label: string;
    value: string;
    color: string;
  }
  const status: IStateMap[] = [
    { label: '上线', value: 'online', color: 'green' },
    { label: '下线', value: 'offline', color: 'orange' },
  ];
  const pagination = reactive({ ...TABLE_PAGINATION });
  const page: IPage = reactive({ page: 1, limit: 10 });
  const loading = ref(false);
  const dataSource: Record<string, string>[] = reactive([]);
  const tableHeight = ref(0);
  const copyState = reactive({
    visible: false,
    loading: false,
    name: '',
  });

  const getTaskListReq = async () => {
    loading.value = true;
    const data = await fetchDockerList({
      ...page,
      ...searchState,
    });
    const { total, items: list } = data;
    dataSource.length = 0;
    dataSource.push(...list);
    Object.assign(pagination, { current: page.page, total: total });

    loading.value = false;
  };

  const toggleTable = (_pagination: TablePaginationConfig) => {
    const { current, pageSize } = _pagination;
    console.log(current, pageSize);
    Object.assign(pagination, { current, pageSize });
    Object.assign(page, { page: current, limit: pageSize });
    getTaskListReq();
  };
  const deleteItem = async (id: string) => {
    await deleteDocker(id as string);
    message.success('删除成功');
    getTaskListReq();
  };
  const onlineDocker = async (n: { id: string; status: string }) => {
    const { id, status } = n;
    // await dockerStatus({ operation: 'online', oid: id });
    // message.success(`发布成功`);
    await dockerStatus({ operation: status == 'online' ? 'offline' : 'online', oid: id });
    message.success(`${{ online: '下线', offline: '上线' }[status]}成功`);
    getTaskListReq();
  };

  // const handleCopy = () => {
  //   copyState.visible = true;
  // };
  const onFinish = (values: Record<string, string>) => {
    const searchConfig: { [key: string]: string } = {};
    for (const key in values) {
      searchConfig[key] = values[key];
    }
    Object.assign(searchState, { ...searchConfig });
    page.page = 1;
    getTaskListReq();
  };
  const onRest = () => {
    Object.assign(pagination, { ...TABLE_PAGINATION });
    Object.assign(page, { page: 1, limit: 10 });
    Object.assign(searchState, { ...DEFAULT_SEARCHSTATE });
    getTaskListReq();
  };

  const confirmCopy = async () => {
    await copyFormRef.value.validateFields();
  };
  const validatorName = (_rule: Rule, value: string) => {
    if (value == '' || value.trim().length == 0) {
      return Promise.reject('请输入模型名称');
    }
    if (value.length > 64) {
      return Promise.reject('模型名称最多输入 64 个字');
    }
    return Promise.resolve();
  };
  onMounted(async () => {
    getTaskListReq();
    await nextTick();
    getTableHeight();
  });
  const getTableHeight = () => {
    const tableItem = document.querySelector('.container');
    tableHeight.value = tableItem?.clientHeight as number;
  };
  onUnmounted(() => {});
</script>

<template>
  <CustomForm :form-items="formConfig" @on-finish="onFinish" @on-rest="onRest" />
  <a-table
    :data-source="dataSource"
    :columns="columns"
    :pagination="pagination"
    :loading="loading"
    :scroll="{ y: tableHeight - 200 }"
    @change="toggleTable"
  >
    <template #bodyCell="{ column, record, text }">
      <div v-if="column.dataIndex === 'operation'" class="operation-box">
        <a-popconfirm title="确定删除这条镜像吗?" @confirm="deleteItem(record.id)">
          <a class="del-btn">删除</a>
        </a-popconfirm>
        <a-popconfirm
          :title="`确定${{ online: '下线', offline: '上线' }[record.status]}这条镜像吗 ?`"
          @confirm="onlineDocker(record)"
        >
          <a>{{ { online: '下线', offline: '上线' }[record.status] }}</a>
        </a-popconfirm>
        <!-- <a @click="handleCopy">复制</a> -->
      </div>
      <div v-else-if="column.dataIndex === 'status'">
        <a-tag :color="status.find((item) => item.value === text)?.color">{{
          status.find((item) => item.value === text)?.label
        }}</a-tag>
      </div>
      <div v-else-if="['created_at', 'updated_at'].includes(column.dataIndex)">
        {{ convertIsoTimeToLocalTime(text) }}
      </div>
      <div v-else>{{ text }}</div>
    </template>
  </a-table>
  <a-modal v-model:open="copyState.visible" title="复制" width="40%" centered @ok="confirmCopy">
    <a-form ref="copyFormRef" layout="vertical" autocomplete="off" :model="copyState">
      <a-form-item label="模型名称" name="name" :rules="[{ required: true, validator: validatorName }]">
        <a-input v-model:value="copyState.name" style="width: 100%" show-count :maxlength="64" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>
