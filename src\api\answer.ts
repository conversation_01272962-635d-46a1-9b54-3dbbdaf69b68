import request from '@/utils/request';
import { getEnv } from '@/utils';

const { VITE_APP_AVATAR_URL } = getEnv();

const AVATAR = VITE_APP_AVATAR_URL;

// 获取答疑题目
export function getAnswer(params: any) {
  return request.$Axios.get(`/virtual-classroom-service/qa/query/[${AVATAR}]`, params);
}

// 获取测验题目
export function getProject(params: any) {
  return request.$Axios.get(`/virtual-classroom-service/video/generate/project/[${AVATAR}]`, params);
}

// 获取固定反馈的视频
export function generateStaticVideo(params: any) {
  return request.$Axios.post(`/virtual-classroom-service/qa/video/[${AVATAR}]`, params);
}
