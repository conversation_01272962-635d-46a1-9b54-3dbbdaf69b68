<script setup lang="ts">
  import { ref } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { ArrowLeftOutlined } from '@ant-design/icons-vue';
  import Container from './Components/container.vue';
  import Monitor from '@/views/Cluster/Components/monitor.vue';
  import Event from '@/views/Cluster/Components/event.vue';
  import Workload from '@/views/Cluster/Workload/index.vue';
  import Service from '@/views/Cluster/Service/index.vue';
  const router = useRouter();
  const route = useRoute();
  const activeKey = ref<string>('1');
</script>

<template>
  <div class="h-100%">
    <div class="text-xl m-b-20px">
      <ArrowLeftOutlined @click="router.back()" />
      <span class="m-l-10px">{{ route.params.name }}</span>
    </div>
    <a-tabs v-model:active-key="activeKey" type="card">
      <a-tab-pane key="1" tab="容器">
        <Container />
      </a-tab-pane>
      <a-tab-pane key="2" tab="事件">
        <Event />
      </a-tab-pane>
      <a-tab-pane key="3" tab="监控详情">
        <Monitor :podName="String(route.params.name)" />
      </a-tab-pane>
      <a-tab-pane key="4" tab="工作负载">
        <Workload />
      </a-tab-pane>
      <a-tab-pane key="5" tab="服务与路由">
        <Service />
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<style scoped lang="less">
  :deep(.ant-tabs) {
    height: calc(100% - 50px);
  }
  :deep(.ant-tabs-content-holder) {
    overflow: auto;
  }
</style>
