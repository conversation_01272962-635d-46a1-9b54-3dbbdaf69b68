<script setup lang="ts">
  import { reactive, ref, onMounted, watch } from 'vue';
  import type { IFormItem, IOptions } from '@/components/CustomForm/index.vue';
  import { CustomForm } from '@/components';
  import { init, type EChartsType } from 'echarts';
  import {
    fetchContainerFilterList,
    fetchPodFilterList,
    fetchPodSystemMetrics,
    fetchPodSystemMetricsWithoutContainer,
  } from '@/api/k8s';
  import { useRoute } from 'vue-router';
  import dayjs from 'dayjs';
  import type { Dayjs } from 'dayjs';
  import type { IFetchSystemMetricsResponse } from '@/interface/kubernetes';
  import { message } from 'ant-design-vue';
  import { convertIsoTimeToLocalTime } from '@/utils/common';
  interface IProps {
    podName?: string;
  }
  const props = withDefaults(defineProps<IProps>(), {
    podName: '',
  });
  interface IEventSearchState {
    timeFilter?: 'minute' | 'hour' | 'day';
    search: Dayjs[];
    pod_name?: string;
    Container_name?: string;
  }
  const echartRefs = ref();
  const route = useRoute();
  const spinning = ref(false);
  const echartsInstance: EChartsType[] = [];
  const monitorResult = ref<IFetchSystemMetricsResponse[]>([]);
  const DEFAULT_SEARCH_STATE: IEventSearchState = {
    timeFilter: 'minute',
    search: [dayjs(new Date()), dayjs(new Date())],
  };
  const searchState: IEventSearchState = reactive({ ...DEFAULT_SEARCH_STATE });
  const podList = ref<IOptions[]>([]);
  const ContainerList = ref<IOptions[]>([]);
  const DEFAULT_FORM_CONFIG: IFormItem[] = [
    {
      field: 'timeFilter',
      type: 'select',
      label: '时间精度',
      placeholder: '请输入关键字',
      options: [
        { label: '按日', value: 'day' },
        { label: '按时', value: 'hour' },
        { label: '按分钟', value: 'minute' },
      ],
    },
    {
      field: 'search',
      type: 'dateRange',
      label: '时间段',
      placeholder: ['请选择', '请选择'],
    },
  ];
  const customFromConfig: IFormItem[] = reactive([
    {
      field: 'pod_name',
      type: 'select',
      label: '容器组名称',
      placeholder: '请选择',
      options: podList,
    },
    {
      field: 'Container_name',
      type: 'select',
      label: '容器名称',
      placeholder: '请选择',
      options: ContainerList,
    },
  ]);
  const formConfig: IFormItem[] = reactive([...DEFAULT_FORM_CONFIG]);
  interface IEcharts {
    titleText: string;
    yAxisUnit: string;
    seriesKey: string;
    isEmpty: boolean;
  }
  const echarts = ref<IEcharts[]>([
    {
      titleText: 'GPU 使用率',
      yAxisUnit: '%',
      seriesKey: 'gpu_util_percent',
      isEmpty: true,
    },
    {
      titleText: 'GPU Memory 使用率',
      yAxisUnit: '%',
      seriesKey: 'gpu_mem_percent',
      isEmpty: true,
    },
    {
      titleText: 'CPU 使用率',
      yAxisUnit: '%',
      seriesKey: 'cpu_percent',
      isEmpty: true,
    },
    {
      titleText: 'CPU Memory 使用率',
      yAxisUnit: '%',
      seriesKey: 'memory_percent',
      isEmpty: true,
    },
  ]);
  const onFinish = (values: Record<string, string>) => {
    const searchConfig: { [key: string]: string } = {};
    for (const key in values) {
      searchConfig[key] = values[key];
    }
    Object.assign(searchState, { ...searchConfig });
    getInfo();
  };

  const onRest = () => {
    Object.assign(searchState, { ...DEFAULT_SEARCH_STATE });
    getInfo();
  };

  const getInfo = async () => {
    const pod_name: string = props.podName ? props.podName : (searchState.pod_name as string);
    const container_name: string = searchState.Container_name as string;
    if (!props.podName && (!pod_name || !container_name)) {
      return message.warn('请选择容器！');
    }
    spinning.value = true;
    // 根据是否传入 pod_name 调不同的接口
    const data: IFetchSystemMetricsResponse[] = props.podName
      ? await fetchPodSystemMetricsWithoutContainer({
          pod: pod_name,
          time_unit: searchState.timeFilter,
          start_at: dayjs(dayjs(searchState.search[0]).format('YYYY-MM-DD') + ' 00:00:00').format(
            'YYYY-MM-DDTHH:mm:ss',
          ),
          end_at: dayjs(dayjs(searchState.search[1]).format('YYYY-MM-DD') + ' 23:59:59').format('YYYY-MM-DDTHH:mm:ss'),
        })
      : await fetchPodSystemMetrics({
          deployment: route.params.name as string,
          pod: pod_name,
          container: container_name,
          time_unit: searchState.timeFilter,
          start_at: dayjs(dayjs(searchState.search[0]).format('YYYY-MM-DD') + ' 00:00:00').format(
            'YYYY-MM-DDTHH:mm:ss',
          ),
          end_at: dayjs(dayjs(searchState.search[1]).format('YYYY-MM-DD') + ' 23:59:59').format('YYYY-MM-DDTHH:mm:ss'),
        });
    monitorResult.value = data;
    echarts.value.map((item) => {
      if (data.length) {
        item.isEmpty = false;
        initechart();
      }
    });
    spinning.value = false;
  };

  const initechart = () => {
    if (echartRefs.value) {
      echartRefs.value.map((e: HTMLDivElement, i: number) => {
        const { yAxisUnit, seriesKey } = echarts.value[i];
        const echartInstance = init(e);
        echartInstance.setOption({
          xAxis: {
            type: 'category',
            data: monitorResult.value.map((item) => convertIsoTimeToLocalTime(item.time)),
          },
          yAxis: {
            type: 'value',
            axisLabel: {
              formatter: (value: string) => `${value} ${yAxisUnit}`,
            },
          },
          tooltip: {
            show: true,
            trigger: 'axis', // 或 'item'
            // @ts-ignore
            formatter: (params) => {
              let content = '';
              params.forEach((item: { name: string; data: string }) => {
                content += `<div>时间: ${item.name}<br/>值: ${item.data} ${yAxisUnit}</div>`;
              });
              return content;
            },
          },
          series: [
            {
              data: monitorResult.value.map((d) => {
                if (seriesKey) return yAxisUnit == '%' && (d[seriesKey] as number) > 100 ? 100 : d[seriesKey];
                else return 0;
              }),
              type: 'line',
            },
          ],
        });
        echartsInstance.push(echartInstance);
      });
    }
  };

  const getPodFilterList = async () => {
    const data: string[] = await fetchPodFilterList({ deployment_name: route.params.name as string });
    podList.value = data.map((item) => {
      return {
        label: item,
        value: item,
      };
    });
    searchState.pod_name = data && data.length ? data[0] : '';
  };

  const getContainerFilterList = async (name: string) => {
    const data: string[] = await fetchContainerFilterList({ pod_name: name });
    ContainerList.value = data.map((item) => {
      return {
        label: item,
        value: item,
      };
    });
    searchState.Container_name = data && data.length ? data[0] : '';
  };
  onMounted(async () => {
    if (!props.podName) {
      formConfig.unshift(...customFromConfig);
      await getPodFilterList();
    } else {
      getInfo();
    }
    window.addEventListener('resize', () => {
      if (echartsInstance) echartsInstance.map((e) => e?.resize());
    });
  });

  watch(
    () => searchState.pod_name,
    (name) => {
      if (name) {
        getContainerFilterList(name);
      }
    },
    { deep: true, immediate: true },
  );

  watch(
    [() => searchState.pod_name, () => searchState.Container_name],
    async ([pod_name, Container_name]) => {
      if (pod_name && Container_name) {
        await initechart();
        await getInfo();
      }
    },
    { deep: true, immediate: true },
  );
  watch(
    () => searchState.timeFilter,
    async (timeFilter) => {
      if (timeFilter) {
        searchState.search = ['hour', 'day'].includes(timeFilter)
          ? [dayjs(new Date()).subtract(7, 'day'), dayjs(new Date())]
          : [dayjs(new Date()), dayjs(new Date())];
      }
    },
    { deep: true, immediate: true },
  );
</script>

<template>
  <a-spin :spinning="spinning">
    <CustomForm :form-items="formConfig" :value="searchState" @on-finish="onFinish" @on-rest="onRest" />
    <div class="textbefo">GPU</div>
    <div class="flex justify-between">
      <div v-for="echart in echarts.slice(0, 2)" :key="echart.seriesKey" class="echart-box">
        <div class="text-20px font-bold">{{ echart.titleText }}</div>

        <div ref="echartRefs" class="w-100% h-100%">
          <a-empty style="height: 100%; display: flex; flex-direction: column; justify-content: center" />
        </div>
      </div>
    </div>

    <div class="textbefo">CPU</div>
    <div class="flex justify-between">
      <div v-for="echart in echarts.slice(2)" :key="echart.seriesKey" class="echart-box">
        <div class="text-20px font-bold">{{ echart.titleText }}</div>

        <div ref="echartRefs" class="w-100% h-100%">
          <a-empty style="height: 100%; display: flex; flex-direction: column; justify-content: center" />
        </div>
      </div>
    </div>
  </a-spin>
</template>

<style scoped lang="less">
  .echart-box {
    width: calc(50% - 10px);
    height: 400px;
    border: 1px solid rgb(0 0 0 / 20%);
    padding: 10px;
  }
</style>
