<script setup lang="ts">
  import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue';
  import { CustomForm } from '@/components';
  import type { IFormItem, IPage } from '@/interface';
  import { TABLE_PAGINATION } from '@/json/common';
  import { deleteOutputModel, getOutputModelList } from '@/api/model';
  import type { ColumnType, TablePaginationConfig } from 'ant-design-vue/es/table';
  import type { IOutputModelItem } from '@/interface/model';
  import { convertIsoTimeToLocalTime } from '@/utils/common';
  import Deploy from '@/views/Model/Deploy/index.vue';
  import { message } from 'ant-design-vue';
  import { modelCategory } from '@/views/Model/Manage/index';

  const DEFAULT_SEARCHSTATE = {
    name: undefined,
    biz_type: undefined,
    model_category: undefined,
  };
  const searchState = reactive({ ...DEFAULT_SEARCHSTATE });
  const tableHeight = ref(0);
  const deleteVisible = ref(false);
  const deployVisible = ref(false);
  const currentRecord = reactive({
    name: '',
    id: '',
  });
  const currentModel = ref();
  const formConfig: IFormItem[] = [
    {
      field: 'name',
      type: 'input',
      label: '模型名称',
      placeholder: '请输入',
    },
    {
      field: 'model_category',
      type: 'select',
      label: '模型类型',
      options: modelCategory,
      placeholder: '请输入',
    },
  ];
  const columns: ColumnType[] = [
    { title: '模型名称', dataIndex: 'name', fixed: 'left' },
    { title: '版本描述', dataIndex: 'description', width: 300, ellipsis: true },
    { title: '模型类型', dataIndex: 'category' },
    { title: '创建时间', dataIndex: 'created_at' },
    { title: '操作', dataIndex: 'operation', width: 240, fixed: 'right' },
  ];
  const pagination = reactive({ ...TABLE_PAGINATION });
  const page: IPage = reactive({ page: 1, limit: 10 });
  const loading = ref(false);
  const dataSource: IOutputModelItem[] = reactive([]);

  const getList = async () => {
    loading.value = true;
    const data: { items: IOutputModelItem[]; total: number } = await getOutputModelList({
      ...page,
      ...searchState,
    });
    const { items, total } = data;
    dataSource.length = 0;
    dataSource.push(...items);
    Object.assign(pagination, { total });
    loading.value = false;
  };

  const toggleTable = (_pagination: TablePaginationConfig) => {
    let { current, pageSize } = _pagination;
    console.log(current, pageSize);
    Object.assign(pagination, { current, pageSize });
    Object.assign(page, { page: current, limit: pageSize });
    getList();
  };
  const onFinish = (values: Record<string, string>) => {
    const searchConfig: { [key: string]: string } = {};
    for (let key in values) {
      searchConfig[key] = values[key];
    }
    Object.assign(searchState, { ...searchConfig });
    page.page = 1;
    getList();
  };
  const onRest = () => {
    Object.assign(pagination, { ...TABLE_PAGINATION });
    Object.assign(page, { page: 1, limit: 10 });
    Object.assign(searchState, { ...DEFAULT_SEARCHSTATE });
    getList();
  };

  const getTableHeight = () => {
    const tableItem = document.querySelector('.container');
    tableHeight.value = tableItem?.clientHeight as number;
  };
  const handleDelete = (record: IOutputModelItem) => {
    const { name, id } = record;
    Object.assign(currentRecord, { name, id });
    deleteVisible.value = true;
  };
  const confirmDelete = async () => {
    await deleteOutputModel(currentRecord.id);
    message.success('删除成功');
    getList();
    deleteVisible.value = false;
  };
  const handleDeploy = (record: IOutputModelItem) => {
    currentModel.value = record;
    deployVisible.value = true;
  };

  onMounted(async () => {
    getList();
    await nextTick();
    getTableHeight();
  });
</script>

<template>
  <CustomForm :form-items="formConfig" @on-finish="onFinish" @on-rest="onRest" />
  <a-table
    :data-source="dataSource"
    :columns="columns"
    :pagination="pagination"
    :loading="loading"
    :scroll="{ y: tableHeight - 200 }"
    @change="toggleTable"
  >
    <template #bodyCell="{ column, record, text }">
      <div v-if="column.dataIndex === 'operation'" class="operation-box">
        <a @click="handleDeploy(record)">部署</a>
        <a class="del-btn" @click="handleDelete(record)">删除</a>
      </div>
      <div v-else-if="['created_at', 'updated_at'].includes(column.dataIndex)">
        {{ convertIsoTimeToLocalTime(text) }}
      </div>
      <div v-else>{{ text || '--' }}</div>
    </template>
  </a-table>
  <a-modal v-model:open="deleteVisible" centered :title="`确定删除模型“${currentRecord.name}”？`" @ok="confirmDelete">
    <p>删除模型会将对应的任务删除，是否确认删除？</p>
  </a-modal>

  <a-drawer v-model:open="deployVisible" width="800" :mask-closable="false">
    <template #title>
      部署
      <a-tooltip>
        <template #title>使用预置开源的模型并将其部署为在线服务，以进行实时的推理调用。</template>
        <QuestionCircleOutlined class="ml-5px" />
      </a-tooltip>
    </template>
    <Deploy :model="currentModel" :close-visible="() => (deployVisible = false)" />
  </a-drawer>
</template>

<style scoped lang="less"></style>
