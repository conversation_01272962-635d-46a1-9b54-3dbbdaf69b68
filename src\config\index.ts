// 首先定义配置接口
import { getEnv } from '@/utils/getEnv';
interface Config {
  avatorWs: string;
  asr: string;
  textToImage: string;
}

const { VITE_APP_AVATAR_WS_URL } = getEnv();

const config: Config = {
  // 数字人
  avatorWs: `${VITE_APP_AVATAR_WS_URL}/algorithm/avatar-back-end/avatar/`,
  // wss://dev-gcluster.shukeyun.com/algorithm/avatar-back-end/avatar/"

  // 语音识别
  // asr: `${VITE_APP_AVATAR_WS_URL}/algorithm/model-compare-back-end/asr/`,
  asr: 'wss://dev-gcluster.shukeyun.com/algorithm/model-compare-back-end/asr/',

  // 文生图
  // textToImage: `${VITE_APP_AVATAR_WS_URL}/algorithm/model-compare-back-end/tutu/`,
  textToImage: 'wss://test-gcluster.shukeyun.com/algorithm/model-compare-back-end/tutu/',
  // wss://gcluster.shukeyun.com/algorithm/model-compare-back-end/tutu/
};

export default config;
