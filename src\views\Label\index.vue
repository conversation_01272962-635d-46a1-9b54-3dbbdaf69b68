<script setup lang="ts">
  import { ref, reactive, nextTick, onMounted } from 'vue';
  import { PlusCircleOutlined } from '@ant-design/icons-vue';
  import {
    addLabel,
    fetchAllLabel,
    updateTag,
    updateCatagory,
    updateTags,
    deleteTag,
    deleteCatagory,
    type IAddLabel,
    type IUpdateTag,
    type IUpdataeCatagory,
  } from '@/api/label.ts';
  import { message } from 'ant-design-vue';
  import type { Rule } from 'ant-design-vue/es/form/interface';
  import type { TreeDataItem } from 'ant-design-vue/es/tree';
  interface ILabelChildren {
    name: string;
    id: string;
    seq: number;
    parent_id: string;
  }
  interface ILabelData {
    name: string;
    id: string;
    seq: number;
    children: ILabelChildren[];
  }
  const labelData = reactive<ILabelData[]>([]);
  const displayLabelData = reactive<ILabelData[]>([]);
  const inputRef = ref();
  const labelRef = ref();
  const loading = ref(false);
  const formState: { name: string } = reactive({
    name: '',
  });
  const changeState: { key: string; value: string } = reactive({
    key: '',
    value: '',
  });
  const modelState: { visible: boolean; id?: string } = reactive({
    visible: false,
  });
  const selectedKeys = ref<string[]>([]);
  const dropProject = async (info: any) => {
    const dropPos = info.node.pos.split('-');
    /**
     * 
      -1：表示拖拽节点插入到目标节点的前面。
      0：表示拖拽节点插入到目标节点的内部（作为子节点）。
      1：表示拖拽节点插入到目标节点的后面。
     */
    const dropPosition = info.dropPosition - Number(dropPos[dropPos.length - 1]);
    // console.log('🚀 ~ dropProject ~ dropPosition:', dropPos, dropPosition, info.dropToGap);
    const isSameLevel = (a: TreeDataItem, b: TreeDataItem) => {
      const aLevel = a.pos.split('-').length;
      const bLevel = b.pos.split('-').length;
      // 拖拽节点插入到目标节点的内部（作为子节点） 特殊处理
      return dropPosition === 0 ? Math.abs(aLevel - bLevel) === 1 : aLevel === bLevel;
    };
    const isSameParent = (a: TreeDataItem, b: TreeDataItem) => {
      const aParent = a.pos.split('-').slice(0, -1).join('-');
      const bParent = b.pos.split('-').slice(0, -1).join('-');
      return aParent === bParent;
    };
    if (!isSameLevel(info.dragNode, info.node)) {
      return message.warn('不能跨层级拖拽');
    }
    const loop = (data: any, key: string, callback: (item: ILabelData, index: number, arr: ILabelData[]) => void) => {
      console.log(key);
      data.forEach((item: any, index: number, arr: any) => {
        if (item.id === key) {
          return callback(item, index, arr);
        }
        if (item.children) {
          return loop(item.children, key, callback);
        }
      });
    };

    const data: ILabelData[] = JSON.parse(JSON.stringify(labelData));
    let dragObj: any;
    // 截取拖拽的节点
    loop(data, info.dragNode.id, (item, index, arr) => {
      arr.forEach((item, idx) => {
        if (idx > index) item.seq--;
      });
      arr.splice(index, 1);
      dragObj = item;
    });

    // 往节点之前插入
    let ar: ILabelData[] = [];
    let i = 0;
    loop(data, info.node.id, (item, index, arr) => {
      ar = arr;
      i = index;
    });
    ar.forEach((a: ILabelData, idx: number) => {
      // 根据节点插入情况，更新其他节点的seq
      if (dropPosition === 0) {
        a.children.forEach((child) => child.seq++);
      } else if (dropPosition === -1) {
        if (idx >= i) a.seq++;
      } else {
        if (idx > i) a.seq++;
      }
    });
    let curSeq: number = -1;

    // 根据节点插入情况，更新拖拽节点的seq
    if (dropPosition === 0) {
      curSeq = Math.min(...ar[i].children.map((child) => child.seq)) - 1;
    } else if (dropPosition === -1) {
      curSeq = Math.max(...ar.filter((_, idx: number) => idx < i).map((a: ILabelData) => a.seq), -1) + 1;
    } else {
      curSeq = Math.max(...ar.filter((_, idx: number) => idx <= i).map((a: ILabelData) => a.seq), -1) + 1;
    }
    dragObj.seq = curSeq;

    // 将更新后的拖拽节点插入到原数组内
    if (dropPosition === 0) {
      ar[i].children.unshift(dragObj);
    } else if (dropPosition === -1) {
      ar.splice(i, 0, dragObj);
    } else {
      ar.splice(i + 1, 0, dragObj);
    }
    const params: IUpdateTag[] = [];
    data.forEach((item) => {
      item.children?.forEach((i) => {
        params.push({
          category_name: item.name,
          category_seq: item.seq,
          tag_name: i.name,
          tag_seq: i.seq,
          id: i.id,
        });
      });
    });
    await updateTags(params);
    message.success('更新成功');
    getTagList();
  };

  const selectProject = (record: any) => {
    const { key } = record;
    selectedKeys.value = [key];
  };
  const handleAdd = (id?: string) => {
    modelState.visible = true;
    modelState.id = id;
  };
  const jumpToEidtLabel = async (record: any) => {
    changeState.key = record.id;
    changeState.value = record.name;
    await nextTick();
    if (inputRef.value) {
      inputRef.value.focus();
    }
  };
  const clickDeletLabel = async (record: any) => {
    extractData(record);
    modelState.id ? await deleteTag(record.id) : await deleteCatagory({ category_name: record.name });
    message.success('删除成功');
    getTagList();
  };
  const handleAddClass = async () => {
    await labelRef.value.validateFields();
    const params: IAddLabel = {
      category_name: '',
      category_seq: 0,
      tag_name: '',
      tag_seq: 0,
    };
    if (modelState.id) {
      const currentCatagory = labelData.find((i) => i.id === modelState.id);
      const maxTagSeq =
        currentCatagory?.children && currentCatagory?.children.length
          ? Math.max(...currentCatagory?.children.map((item) => item.seq))
          : -1;
      Object.assign(params, {
        category_name: currentCatagory?.name,
        category_seq: currentCatagory?.seq,
        tag_name: formState.name,
        tag_seq: maxTagSeq + 1,
      });
    } else {
      const maxCateSeq = Math.max(...labelData.map((item) => item.seq), -1);
      Object.assign(params, {
        category_name: formState.name,
        category_seq: maxCateSeq + 1,
        tag_name: '',
        tag_seq: 0,
      });
    }
    await addLabel(params);
    message.success('添加成功');
    modelState.visible = false;
    getTagList();
    await labelRef.value.resetFields();
  };
  const handleCancel = async () => {
    await labelRef.value.resetFields();
  };
  const getTagList = async () => {
    loading.value = true;
    const data = await fetchAllLabel();
    data.forEach((item: ILabelData) => {
      item.id = item.name;
      if (item.children) {
        // 给子元素添加父元素id，方便以后找
        item.children.forEach((i) => {
          i.parent_id = item.id;
        });
      }
    });
    labelData.length = 0;
    labelData.push(...data);
    const displayData: ILabelData[] = JSON.parse(JSON.stringify(data));
    displayData.forEach((item) => {
      if (item.children) {
        item.children.forEach((i, index) => {
          if (!i.name) {
            item.children?.splice(index, 1);
          }
        });
      }
    });
    displayLabelData.length = 0;
    displayLabelData.push(...displayData);
    loading.value = false;
  };
  const extractData = (data: ILabelData | ILabelChildren) => {
    let children: ILabelChildren[] | undefined;
    let parentId: string | undefined;

    if ('children' in data) {
      // 如果有 children 属性，说明是 ILabelData
      children = data.children;
      // modelState.id 为空表示此时操作的是分类，有值表示此时操作的是标签
      modelState.id = '';
    } else if ('parent_id' in data) {
      // 如果有 parent_id 属性，说明是 ILabelChildren
      parentId = data.parent_id;
      modelState.id = data.id;
    } else {
      throw new Error('Unknown type');
    }
    return { children, parentId };
  };
  const handleEdit = async (e: any, record: ILabelData | ILabelChildren) => {
    const { name, id, seq } = record;
    const target = e.target.value;
    // 如果值没有改变直接 return
    if (target === name) {
      changeState.key = '';
      return;
    }
    // 区分此时编辑的是分类还是标签
    const { parentId } = extractData(record);
    const msg = handleValidator(target);
    if (msg) return message.warn(msg);
    if (modelState.id) {
      const currentCatagory = labelData.find((i) => i.id === parentId) as ILabelData;
      const params: IUpdateTag = {
        category_name: currentCatagory?.name,
        category_seq: currentCatagory?.seq,
        tag_name: target,
        tag_seq: seq,
        id: currentCatagory.id,
      };
      await updateTag(id, params);
    } else {
      const params: IUpdataeCatagory = {
        fr: name,
        to: target,
      };
      await updateCatagory(params);
    }
    message.success('编辑成功');
    getTagList();
    changeState.key = '';
  };

  const handleValidator = (value: string) => {
    let msg = '';
    if (value == undefined || value.trim().length == 0) {
      msg = `请输入${modelState.id ? '标签' : '分类'}名称`;
      return msg;
    }
    if (value.length > 16) {
      msg = `${modelState.id ? '标签' : '分类'}名称最多输入16个字`;
      return msg;
    }
    const labels = labelData.map((item) => item.name);
    const tags = labelData
      .map((item) => item.children)
      .flat()
      .map((i) => i?.name);
    if (modelState.id) {
      if (tags.includes(value)) {
        msg = '标签名称不能重复';
      }
    } else {
      if (labels.includes(value)) {
        msg = '分类名称不能重复';
      }
    }
    return msg;
  };

  const validatorName = (_rule: Rule, value: string) => {
    const msg = handleValidator(value);
    if (msg) return Promise.reject(new Error(msg));
    return Promise.resolve();
  };

  onMounted(() => {
    getTagList();
  });
</script>

<template>
  <a-row style="width: 100%; height: 100%">
    <a-col :span="6" style="background-color: #fff">
      <div class="section-container">
        <a-spin
          style="display: flex; align-items: center; justify-content: center; height: 100%"
          v-if="loading"
          tip="数据加载中,请稍等..."
        ></a-spin>
        <template v-else>
          <a-tree
            v-if="displayLabelData.length"
            style="height: 100px; padding: 8px"
            class="main-content-scroll"
            block-node
            :selectedKeys="selectedKeys"
            :autoExpandParent="true"
            defaultExpandAll
            :tree-data="displayLabelData"
            :draggable="!changeState.key"
            @drop="dropProject"
            :show-icon="false"
            expand-action="false"
          >
            <template #title="record">
              <div class="group-title" style="max-width: 100%" @click="selectProject(record)">
                <a-input
                  v-if="changeState.key === record.id"
                  style="flex: 1; width: 100px"
                  v-model:value="changeState.value"
                  @blur="(e: any) => handleEdit(e, record)"
                  ref="inputRef"
                />
                <span v-else v-ellipse-tooltip.top style="flex: 1; width: 100px" @dblclick="jumpToEidtLabel(record)">{{
                  record.name
                }}</span>
                <template v-if="record.children">
                  <div class="icon-box">
                    <a-tooltip>
                      <template #title>添加标签</template>
                      <PlusCircleOutlined class="icon" style="color: #1890ff" @click="handleAdd(record.id)" />
                    </a-tooltip>
                    <div class="edit icon" @click="jumpToEidtLabel(record)"></div>
                    <a-popconfirm title="确定删除该分类及其子标签吗?" @confirm="clickDeletLabel(record)">
                      <div class="delete icon"></div>
                    </a-popconfirm>
                  </div>
                </template>
                <template v-else>
                  <div class="icon-box">
                    <div class="edit icon" @click="jumpToEidtLabel(record)"></div>
                    <a-popconfirm title="确定删除该标签吗?" @confirm="clickDeletLabel(record)">
                      <div class="delete icon"></div>
                    </a-popconfirm>
                  </div>
                </template>
              </div>
            </template>
          </a-tree>
          <div v-else style="display: flex; align-items: center; justify-content: center; height: 100%">
            <a-empty></a-empty>
          </div>
        </template>

        <div class="tree-bottom">
          <a-button type="primary" :style="{ width: 'calc(100% - 30px)' }" @click="handleAdd()"> 添加分类 </a-button>
        </div>
      </div>
    </a-col>
  </a-row>
  <a-modal
    v-model:open="modelState.visible"
    :title="`添加${modelState.id ? '标签' : '分类'}`"
    @ok="handleAddClass"
    @cancel="handleCancel"
    centered
  >
    <a-form ref="labelRef" :model="formState" name="basic" autocomplete="off" layout="vertical">
      <a-form-item
        :label="`${modelState.id ? '标签' : '分类'}名称`"
        name="name"
        :rules="[{ required: true, validator: validatorName }]"
      >
        <a-input v-model:value="formState.name" :placeholder="`请输入`" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<style scoped lang="less">
  .group-title {
    display: flex;
    justify-content: space-between;

    .icon {
      display: none;
    }

    &:hover .icon {
      display: block;
    }
  }

  .icon-box {
    display: flex;
    align-items: center;
    justify-content: center;

    .icon {
      width: 16px;
      height: 16px;
      margin: 0 4px;
      background-size: 100%;
    }

    .edit {
      background-image: url('https://minio-prod.shukeyun.com/deepfox/716737441f75e3ac976c289fcfec8558.png');
    }

    .delete {
      background-image: url('https://minio-prod.shukeyun.com/deepfox/3da3e261122ef5f18e58c8014b65f88b.png');
    }
  }

  .tree-bottom {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    background-color: #fff;
    border-top: 1px solid #ebebeb;

    button {
      margin: 15px 0;
    }
  }

  :deep(.ant-tree-treenode-selected) {
    color: #1890ff !important;
    background-color: #e7f4ff !important;
  }

  :deep(.ant-tree-node-selected) {
    color: #1890ff !important;
    background-color: #e7f4ff !important;
  }
</style>
