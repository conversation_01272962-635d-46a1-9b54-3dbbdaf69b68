import request from '@/utils/request';

const BLENDER = 'opsmonitor';

export function getOpsmonitor(params : { url: string, body: object, method: string }) {
  return request.$Axios.post(`/k8s_cluster/cluster/metrics`, params);
}

export function getDeploymentsList(params : { page: number, limit: number }) {
  return request.$Axios.get(`/k8s_cluster/deployments_list`, params);
}

export function getPodsList(params : { page: number, limit: number }) {
  return request.$Axios.get(`/k8s_cluster/pods_list`, params);
}

export function getServicesList(params : { page: number, limit: number }) {
  return request.$Axios.get(`/k8s_cluster/services_list`, params);
}
