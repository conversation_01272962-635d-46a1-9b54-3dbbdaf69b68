<script setup lang="ts">
  import { ref, reactive, watch } from 'vue';
  import { message } from 'ant-design-vue';
  import { QuestionCircleFilled } from '@ant-design/icons-vue';
  import { getServiceSetting, postServiceSetting } from '@/api/exploration';
  import type { ICallParams } from '@/interface/exploration';
  interface IProps {
    visible: boolean;
    serviceName: string;
    id: string;
  }
  const props = withDefaults(defineProps<IProps>(), {});
  const emits = defineEmits<{
    (event: 'cancel'): void;
    // (event: 'ok', value: string): any;
  }>();
  const prompt = ref<string>('');
  const spinning = ref(false);
  const formState = reactive<ICallParams>({
    max_tokens: 512,
    temperature: 0.7,
    top_p: 1,
    frequency_penalty: 1.2,
  });
  const handleCancel = async () => {
    await postServiceSetting({
      deploy_id: props.id,
      system_prompt: prompt.value,
      call_params: formState,
    });
    message.success('保存成功');
    emits('cancel');
  };

  const getServiceSettingReq = async () => {
    spinning.value = true;
    const { system_prompt, call_params } = await getServiceSetting(props.id);
    prompt.value = system_prompt || '';
    call_params && Object.assign(formState, { ...call_params });
    spinning.value = false;
  };

  watch(
    () => props.id,
    (id) => {
      if (id) {
        getServiceSettingReq();
      }
    },
    { deep: true, immediate: true },
  );

  defineExpose({
    formState,
    prompt,
  });
</script>

<template>
  <a-drawer :width="400" title="参数配置" placement="right" :open="visible" @close="handleCancel">
    <a-spin :spinning="spinning">
      <!-- <template #footer>
      <a-button type="primary">保存</a-button>
    </template> -->
      <a-form layout="vertical" autocomplete="off">
        <a-alert style="margin-bottom: 20px" :message="props.serviceName" type="info" />
        <a-form-item>
          <template #label>
            <span style="margin-right: 10px">系统提示词</span>
            <a-tooltip>
              <template #title>模型的初始设定或背景信息</template>
              <question-circle-filled class="span-mg-left" />
            </a-tooltip>
          </template>
          <a-textarea v-model:value="prompt" :rows="3" placeholder="请输入系统提示词" />
        </a-form-item>

        <a-form-item>
          <template #label>
            <span style="margin-right: 10px">最大 Token 数</span>
            <a-tooltip>
              <template #title>大模型返回答案的最大 token 数（范围：1-16384）。</template>
              <question-circle-filled class="span-mg-left" />
            </a-tooltip>
          </template>
          <a-slider v-model:value="formState.max_tokens" :min="1" :max="16384" />
          <a-input-number v-model:value="formState.max_tokens" :min="1" :max="16384" class="right-input" />
        </a-form-item>
        <a-form-item>
          <template #label>
            <span style="margin-right: 10px">随机度</span>
            <a-tooltip>
              <template #title
                >控制模型输出的随机性（范围 0-1，精度为 0.1）；值越高，输出就越随机，而值越低，输出就越确定。</template
              >
              <question-circle-filled class="span-mg-left" />
            </a-tooltip>
          </template>
          <a-slider v-model:value="formState.temperature" :min="0" :max="1" :step="0.1" />
          <a-input-number
            v-model:value="formState.temperature"
            :min="0"
            :max="1"
            :precision="1"
            :step="0.1"
            class="right-input"
          />
        </a-form-item>
        <a-form-item>
          <template #label>
            <span style="margin-right: 10px">采样率</span>
            <a-tooltip>
              <template #title>指定模型输出的多样性（范围 0-1，精度为 0.1）。值越高，输出的就越发散。</template>
              <question-circle-filled class="span-mg-left" />
            </a-tooltip>
          </template>
          <a-slider v-model:value="formState.top_p" :min="0" :max="1" :step="0.1" />
          <a-input-number
            v-model:value="formState.top_p"
            :min="0"
            :max="1"
            :precision="1"
            :step="0.1"
            class="right-input"
          />
        </a-form-item>
        <a-form-item>
          <template #label>
            <span style="margin-right: 10px">去重强度</span>
            <a-tooltip>
              <template #title
                >通过惩罚已经频繁使用的单词来降低模型行中出现重复单词的可能性（-2 到 2，精度 0.1）。</template
              >
              <question-circle-filled class="span-mg-left" />
            </a-tooltip>
          </template>
          <a-slider v-model:value="formState.frequency_penalty" :min="-2" :max="2" :step="0.1" />
          <a-input-number
            v-model:value="formState.frequency_penalty"
            :min="-2"
            :max="2"
            :precision="1"
            :step="0.1"
            class="right-input"
          />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-drawer>
</template>

<!-- <style scoped lang="less"></style> -->
