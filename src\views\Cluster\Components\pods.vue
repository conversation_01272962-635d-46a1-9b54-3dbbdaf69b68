<script setup lang="ts">
  import { ref, reactive, onMounted } from 'vue';
  import type { ColumnType } from 'ant-design-vue/es/table';
  import { useRoute, useRouter } from 'vue-router';
  import { fetchClusterPods, fetchPodsYamlConfig, fetchServicePodList } from '@/api/k8s';
  import type { IClusterPosItem } from '@/interface/kubernetes';
  import { copyText, formatTime, convertIsoTimeToLocalTime } from '@/utils/common';
  import { VAceEditor } from 'vue3-ace-editor';
  import 'ace-builds/src-noconflict/theme-chrome';
  import 'ace-builds/src-noconflict/theme-monokai.js';
  import 'ace-builds/src-noconflict/mode-python.js';
  import 'ace-builds/src-noconflict/mode-sh.js';
  import type { IAceOptions } from '@/interface';
  import yaml from 'js-yaml';

  const router = useRouter();
  const route = useRoute();
  const loading = ref(false);
  const dataSource = reactive<IClusterPosItem[]>([]);
  const YAMLState: { visible: boolean; value: string; title: string; spinning: boolean } = reactive({
    visible: false,
    title: 'Deployment',
    spinning: false,
    value: '',
  });
  const columns: ColumnType[] = [
    { title: '容器组名称', dataIndex: 'name' },
    { title: '就绪', dataIndex: 'ready' },
    { title: '所在节点', dataIndex: 'node' },
    { title: '容器状态', dataIndex: 'phase' },
    { title: 'IP 地址', dataIndex: 'pod_ip' },
    { title: '创建时间', dataIndex: 'creation_time' },
    { title: '更新时间', dataIndex: 'start_time' },
    { title: '已重启', dataIndex: 'restart_count' },
    { title: '已创建', dataIndex: 'runtime' },
    { title: '操作', dataIndex: 'operation' },
  ];

  const aceOptions: IAceOptions = {
    enableBasicAutocompletion: true,
    enableSnippets: true,
    enableLiveAutocompletion: true,
    tabSize: 4,
    enableEmmet: true,
    fontSize: 15,
    showPrintMargin: false, // 去除编辑器里的竖线
    highlightActiveLine: true,
    useWorker: true,
    readOnly: false,
  };
  const fetchList = async () => {
    loading.value = true;
    const data: IClusterPosItem[] =
      route.name === 'workload-detail'
        ? await fetchClusterPods(route.params.name as string)
        : await fetchServicePodList(route.params.name as string);
    dataSource.length = 0;
    dataSource.push(...data);
    loading.value = false;
  };
  const fetchYamlConfigReq = async (record: IClusterPosItem) => {
    YAMLState.visible = true;
    YAMLState.spinning = true;
    const data = await fetchPodsYamlConfig(record.name);
    YAMLState.value = yaml.dump(data);
    YAMLState.spinning = false;
  };
  const jumpDetail = (record: IClusterPosItem) => {
    router.push(`/cluster-manage/pod/${record.name}`);
  };

  onMounted(() => {
    fetchList();
  });
</script>

<template>
  <a-table :data-source="dataSource" :columns="columns" :pagination="false" :loading="loading">
    <template #bodyCell="{ column, text, record }">
      <div v-if="column.dataIndex === 'operation'">
        <a @click="fetchYamlConfigReq(record)">YAML</a>
      </div>
      <div v-else-if="column.dataIndex === 'name'">
        <a class="table-a-btn" @click="jumpDetail(record)">{{ text }}</a>
      </div>
      <div v-else-if="['creation_time', 'start_time'].includes(column.dataIndex)">
        {{ convertIsoTimeToLocalTime(text) }}
      </div>
      <div v-else-if="column.dataIndex === 'runtime'">
        {{ formatTime(Math.floor(text)) }}
      </div>
      <div v-else-if="column.dataIndex === 'ready'">1 / 1</div>
      <div v-else>{{ text }}</div>
    </template>
  </a-table>
  <a-modal
    v-model:open="YAMLState.visible"
    :title="YAMLState.title"
    width="60%"
    centered
    :footer="false"
    @cancel="
      YAMLState.visible = false;
      YAMLState.value = '';
    "
  >
    <div class="h-800px overflow-scroll">
      <a-spin :spinning="YAMLState.spinning">
        <div class="w-100% h-32px flex flex-justify-end">
          <a @click="copyText(YAMLState.value)"> 复制到剪切板 </a>
        </div>
        <v-ace-editor
          v-model:value="YAMLState.value"
          lang="sh"
          theme="monokai"
          :options="aceOptions"
          style="height: calc(100% - 32px); border: 1px solid #dbd3d3"
        />
      </a-spin>
    </div>
  </a-modal>
</template>

<style scoped lang="less"></style>
