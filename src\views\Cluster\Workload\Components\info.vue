<script setup lang="ts">
  import { ref, onMounted } from 'vue';
  import { fetchClusterDetail } from '@/api/k8s';
  import { useRoute } from 'vue-router';
  import type { IClusterDetailResponse } from '@/interface/kubernetes';
  import { QuestionCircleFilled } from '@ant-design/icons-vue';
  import { UPDATE_STRATEGY_MAP } from '@/views/Cluster/index';
  const route = useRoute();
  const info = ref<IClusterDetailResponse>();
  const spinning = ref(false);
  const getDetail = async () => {
    spinning.value = true;
    const data: IClusterDetailResponse = await fetchClusterDetail(route.params.name as string);
    info.value = data;
    spinning.value = false;
  };
  onMounted(() => {
    getDetail();
  });
</script>

<template>
  <div class="h-100%">
    <a-spin :spinning="spinning">
      <a-form :label-col="{ style: { width: '120px' } }" label-align="left">
        <div class="textbefo">基本信息</div>
        <a-row>
          <a-col :span="8">
            <a-form-item label="工作负载名称">{{ `${info?.namespace} / ${info?.name}` }}</a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="工作负载分层">{{ info?.tier || '默认' }}</a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="工作负载类型">{{ info?.workload_type }}</a-form-item>
          </a-col>
        </a-row>
        <a-form-item label="工作负载描述" :span="3">
          {{ info?.description || '-' }}
        </a-form-item>
        <a-form-item label="注解">
          <div v-for="key in Object.keys(info?.annotations || {})" :key="key" class="m-b-10px">
            <span class="bg-#36435c text-#fff p-x-5px">{{ key }}</span>
            <span class="bg-#eff4f9 p-l-10px">{{
              info?.annotations[key] ? JSON.stringify(JSON.parse(String(info?.annotations[key]))) : ''
            }}</span>
          </div>
        </a-form-item>

        <div class="textbefo">容器镜像</div>
        <a-form-item label="容器镜像版本">
          <div v-for="item in info?.container_images" :key="item">
            {{ item }}
          </div>
        </a-form-item>

        <div class="textbefo">更新策略</div>
        <a-row>
          <a-col :span="8">
            <a-form-item label="类型">{{
              info?.update_strategy ? UPDATE_STRATEGY_MAP[info.update_strategy] : ''
            }}</a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="最大超出副本数">
              <template #label>
                <span style="margin-right: 10px">最大超出副本数</span>
                <a-tooltip>
                  <template #title>模型的初始设定或背景信息</template>
                  <question-circle-filled class="span-mg-left" />
                </a-tooltip>
              </template>
              {{ info?.max_surge }}</a-form-item
            >
          </a-col>
          <a-col :span="8">
            <a-form-item label="最大不可用副本数">{{ info?.max_unavailable }}</a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-spin>
  </div>
</template>

<style scoped lang="less">
  :deep(.ant-form-item) {
    margin-bottom: 0;
  }
</style>
