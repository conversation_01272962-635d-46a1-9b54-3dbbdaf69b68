<template>
  <div class="flv-player-container">
    <video
      id="live_id"
      ref="videoRef"
      autoplay
      :muted="props.muted"
      playsinline
      style="width: 100%; max-height: 100%"
    ></video>
  </div>
</template>

<script lang="ts" setup>
  import { ref, onMounted, onBeforeUnmount, nextTick } from 'vue';

  declare global {
    interface Window {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      flvjs: any;
    }
  }

  const props = defineProps({
    src: {
      type: String,
      required: true,
    },
    muted: {
      type: Boolean,
      default: true,
    },
    debug: {
      type: Boolean,
      default: false,
    },
  });

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  let player: any = null;
  const isSupported = ref(true);
  const videoRef = ref<HTMLVideoElement | null>(null);

  // 重新播放函数
  const replay = () => {
    if (player && videoRef.value) {
      player
        .play()
        .then(() => {
          console.log('flv.js 重新播放成功');
        })
        .catch((err: string) => {
          console.error('flv.js 重新播放失败:', err);
          // 短暂延迟后尝试再次播放
          setTimeout(replay, 3000);
        });
    }
  };

  onMounted(async () => {
    if (!window.flvjs.isSupported()) {
      console.log('当前浏览器不支持 FLV.js');
      return;
    }

    const videoElement = document.getElementById('live_id') as HTMLVideoElement;
    if (!videoElement) {
      console.log('未找到视频元素');
      return;
    }
    await nextTick();
    console.log('FLV.js loaded: 检查 FLV.js 是否加载成功', window.flvjs); // 检查 FLV.js 是否加载成功

    const flvjs = window.flvjs;

    if (props.debug) {
      flvjs.LoggingControl.enableDebug = true;
    }

    player = flvjs.createPlayer({
      type: 'flv',
      url: props.src,
      isLive: true,
    });

    player.attachMediaElement(videoElement);
    player.load();
    player
      .play()
      .then(() => {
        console.log('flv.js 播放成功');
      })
      .catch((err: string) => {
        console.error('flv.js 播放失败:', err);
        isSupported.value = false;
        replay(); // 调用重新播放函数
      });
  });

  onBeforeUnmount(() => {
    if (player) {
      player.destroy();
      player = null;
    }
    if (videoRef.value) {
      videoRef.value.removeEventListener('ended', replay);
    }
  });
</script>

<style scoped></style>
