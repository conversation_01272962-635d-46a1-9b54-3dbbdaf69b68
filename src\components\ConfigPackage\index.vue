<template>
  <div id="ConfigPackage" ref="main_ref" :style="{ '--itemSpace': `${state.item_space}px` }">
    <div class="title">
      <div
        :class="{ disabled: on === false || on === null }"
        @click="on === false || on === null ? () => {} : expandClick()"
      >
        <DownOutlined v-if="is_on && isSolt" class="icon" />
        <RightOutlined v-else class="icon" :style="isSolt ? {} : { display: 'none', marginLeft: 0 }" />
        <span :class="{ 'span-mg-left': isSolt }">{{ label }}</span>
        <Tooltip v-if="tip">
          <template #title>
            <div v-html="tip"></div>
          </template>
          <question-circle-filled class="span-mg-left" />
        </Tooltip>
        <slot name="right"></slot>
      </div>
      <div
        :class="{ district: true, [`${effect}`]: typeof effect == 'string' }"
        :style="typeof effect == 'object' ? effect : {}"
      >
        <slot name="effect"></slot>
        <div v-if="typeof on == 'boolean' && !noton" class="right_icon">
          <div v-if="on" class="eys_show" @click="changOn(false)"></div>
          <div v-else class="no_eys_show" @click="changOn(true)"></div>
          <!-- <EyeOutlined v-if="on" @click="emits('update:on', false)" />
          <EyeInvisibleOutlined v-else @click="emits('update:on', true)" /> -->
        </div>
      </div>
    </div>
    <template v-if="true">
      <div
        v-if="isSolt"
        :class="{ upstep: state.child }"
        :style="{
          // ...(state.child ? {} : { marginRight: `-${upstep_val}px`, marginLeft: `-${upstep_val}px`, padding: `0 ${upstep_val}px` }),
          ...(state.child ? {} : { padding: `0 ${upstep_val}px` }),
          ...{ marginTop: `${is_on ? state.item_space : 0}px` },
        }"
      >
        <div
          :class="{ box: true, clip_box: !spacious }"
          :style="{
            height: is_on ? `${state.box_height + (state.child || spacious ? 0 : 40)}px` : '0px',
            '--before': `${state.box_height}px`,
            marginTop: is_on ? '0px' : '0px',
          }"
        >
          <div ref="box_ref" :class="content_class">
            <slot></slot>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
  import { Tooltip, Tabs, Table } from 'ant-design-vue';
  import {
    EyeInvisibleOutlined,
    QuestionCircleFilled,
    EyeOutlined,
    RightOutlined,
    DownOutlined,
  } from '@ant-design/icons-vue';
  import { onMounted, reactive, ref, watch, nextTick, useSlots, computed } from 'vue';
  interface IProps {
    label: string; //标题
    on?: any; //眼睛
    noton?: boolean; //在绑定眼睛的情况下的特殊排除条件
    expand: boolean; //展开
    expandClick: any; //展开事件
    tip?: string; //标题提示
    spacious?: boolean; //没有线装饰
    upstep_val?: number; //背景扩展距离
    child?: boolean; //是否嵌套子组件
    effect?: any; // effect 样式
    content_class?: any; // effect 样式
    // slot => right effect
  }
  const props = withDefaults(defineProps<IProps>(), {
    label: '',
    expand: false,
    child: false,
    tip: '',
    on: undefined,
    noton: false,
    spacious: false,
    upstep_val: 20,
    effect: {},
    content_class: '', // 内容样式
    expandClick: () => {},
  });

  const sols: any = useSlots();
  const main_ref: any = ref(null);
  const box_ref: any = ref(null);
  const emits = defineEmits<{
    (event: 'update:on', value: boolean): void;
    (event: 'change', data: any): any;
  }>();

  const state = reactive<any>({
    box_height: 0,
    child: props.child,
    item_space: 12, //内容项间距
  });

  const changOn = (bool: boolean) => {
    emits('update:on', bool);
    emits('change', bool);
  };

  const is_on = computed(() => {
    const { on, expand } = props;
    if (typeof on == 'boolean') {
      return on ? expand : on;
    } else return expand;
  });

  watch(
    () => props.on,
    (val, old_val) => {
      const { expand, expandClick } = props;
      if (val !== old_val) {
        if (val && !expand) expandClick();
      }
    },
  );

  // slot 下没有html / 注释 / v-if  时仅作为switch开关
  const isSolt = computed(() => {
    // console.log(props.label,sols?.default());

    if (sols?.default) return !sols?.default().every((e: any) => e.children == 'v-if');
    else return false;
  });

  const resizeObserver = new ResizeObserver((entries: any) => {
    const [
      {
        contentRect: { height },
      },
    ] = entries;
    // state.box_height= height
    if (box_ref.value) state.box_height = box_ref.value?.getBoundingClientRect()['height'];
  });

  watch(
    () => box_ref.value,
    (val: any) => {
      if (val) resizeObserver.observe(box_ref.value);
    },
    { immediate: true, deep: true },
  );

  onMounted(() => {
    //是否直接嵌套子组件
    // console.log(main_ref.value,main_ref.value.parentNode,main_ref.value.parentNode.parentNode,main_ref.value.parentNode.parentNode.className);
    if (main_ref.value && main_ref.value.parentNode && main_ref.value.parentNode.className == '' && !state.child)
      state.child = true;
  });
</script>

<style scoped lang="less">
  #ConfigPackage {
    margin-bottom: var(--itemSpace);

    > .title {
      display: flex;
      align-items: center;
      // padding: 0 0 10px 0;

      > div:nth-child(1) {
        flex: 1;
        cursor: pointer;
        display: flex;
        align-items: center;
      }

      .disabled {
        span {
          opacity: 0.4;
        }
      }

      .icon {
        font-size: 13px;
        padding: 0 5px 0 0px;
      }

      .district {
        display: flex;
        align-items: center;

        .right_icon {
          span {
            cursor: pointer;
          }
        }

        :deep(.ant-row) {
          margin-bottom: 0;
        }
      }
    }

    > div:nth-child(2) {
      background: #f4f4f4;

      > .box {
        position: relative;
        height: 0;
        overflow: hidden;
        transition: all 0.15s linear;
      }

      .clip_box {
        > div:nth-child(1) {
          padding: 0 0 1px 20px;
          padding-top: var(--itemSpace);
          margin: 20px 0;
        }
      }

      @clip_px: 15px;

      > .clip_box::before {
        position: absolute;
        content: '';
        // width: 100%;
        top: 20px;
        width: calc(100% - 6px);
        left: 6px;
        height: var(--before);
        border: 1px solid #ccc;
        clip-path: polygon(0% 0%, @clip_px 0%, @clip_px 100%, 0% 100%);
      }
    }

    .upstep {
      margin-right: 0 !important;
      margin-left: 0 !important;
      padding: 0 !important;

      > .clip_box {
        > div:nth-child(1) {
          margin: 0 !important;
        }
      }

      > .clip_box::before {
        top: 0px !important;
      }
    }

    .eys_show,
    .no_eys_show {
      width: 16px;
      height: 16px;
    }

    .eys_show {
      background: url('@/assets/image/base/on-eys.png') 0 0 / 100% 100%;
    }

    .no_eys_show {
      background: url('@/assets/image/base/off-eys.png') 0 0 / 100% 100%;
    }

    .span-mg-left {
      margin-left: 10px;
    }
  }
</style>
