import request from '@/utils/request';
import { getEnv } from '@/utils';

// 定义宽松的对象类型
type TLooseObject = Record<string, unknown>;

const { VITE_APP_AVATAR_URL } = getEnv();

const AVATAR = VITE_APP_AVATAR_URL;

export function upload(params: FormData, config: TLooseObject) {
  return request.$Axios
    .post(`/virtual-classroom-service/face/upload/[${AVATAR}]`, params, config)
    .then((data: string[]) => data as string[]);
}


export function getModelList() {
  return request.$Axios
    .get(`/avatar-back-end/model_info/[${AVATAR}]`)
    .then((data: string[]) => data as string[]);
}
