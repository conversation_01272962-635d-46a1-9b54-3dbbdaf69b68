<!--
 * @Author: dengfusheng 
 * @Date: 2025-03-25 17:31:58
 * @LastEditTime: 2025-04-18 14:48:20
 * @LastEditors: <EMAIL>
 * @FilePath: \ai-platform-frontend\src\views\Integration\index.vue
 * @Description: 答策中枢
-->
<script setup lang="ts">
  import { onBeforeMount, onMounted, reactive, ref, watch } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { closeUpStatus, getLocalItem, setLocalItem } from '@/utils/common';
  import { ConfigPackage } from '@/components';
  import { drillStatus, executeBlend, executeP2L, routerDrill } from '@/api/integration';
  import { Loading3QuartersOutlined, LoadingOutlined } from '@ant-design/icons-vue';
  import { h } from 'vue';
  import dayjs from 'dayjs';
  import { v4 as uuid } from 'uuid';
  import { message } from 'ant-design-vue';

  const route = useRoute();
  const router = useRouter();
  const deployRef = ref<any>(null);
  const drillRef = ref<any>(null);
  const modelRef = ref<any>(null);

  const upStatus = reactive<any>({
    childform: reactive<any>({}),
    compare: true,
    collocate: true,
    execute: true,
    course: true,
    parameter: true,
  });

  const init_general_parameters = {
    proj_name: `router模型训练_${dayjs().format('YYYY-MM-DD hh:mm:ss')}`,
    learning_rate: 0.00001,
    adam_epsilon: 0.00000001,
    batch_size: 4,
    max_length: 4096,
    num_train_epochs: 1,
    train_data_path: '', // data/train
    val_data_path: '', // data/val
    output_dir: '', // outputs
    pretrain_model_name: '', // my_pretrained_model
    gradient_accumulation_steps: 16,
    chat_template: 'my_chat_template',
    model_type: 'qwen2',
    head_type: 'bt',
    loss_type: 'bt-tie',
    weighted_loss: true,
    deepspeed_config_path: '', // deepspeed_config.json
    init_type: 'reset_params',
    load_train_data_from_disk: true,
  };

  const formState = reactive<any>({
    question: '',
    modelList: [],

    // 融合
    top_K: 2,
    fuserModelName: 'llm-blender/gen_fuser_3b', //vicuna-v1.3   lmsys/vicuna-7b-v1.5 llm-blender/gen_fuser_3b
    // 筛选
    general_parameters: init_general_parameters,
  });

  const state = reactive<any>({
    modelList: [
      {
        name: 'deepseek-r1:32b',
        description: '',
        checked: false,
      },
      {
        name: 'qwen2.5:72b',
        description: '',
        checked: false,
      },
    ],
    open: false,
    loading: false,
    fun: true, //  true 对比  false 融合
    // 通用参数微调
    general_parameters: [
      {
        label: '项目名称',
        value: 'proj_name',
        desc: '项目名称',
        type: 'input',
        required: true,
      },
      {
        label: '学习率',
        value: 'learning_rate',
        desc: 'AdamW 优化器的初始学习率。',
        type: 'input',
        required: true,
      },
      {
        label: 'Adam epsilon',
        value: 'adam_epsilon',
        desc: 'Adam epsilon',
        type: 'input',
        required: true,
      },
      {
        label: '批处理大小',
        value: 'batch_size',
        desc: '批处理大小',
        type: 'inputNumber',
        required: true,
        min: 1,
      },
      {
        label: '最大长度',
        value: 'max_length',
        desc: '最大长度',
        type: 'inputNumber',
        required: true,
        min: 1,
      },
      {
        label: '训练轮数',
        value: 'num_train_epochs',
        desc: '需要执行的训练总轮数。',
        type: 'inputNumber',
        required: true,
        min: 1,
      },

      {
        label: '梯度累积步数',
        value: 'gradient_accumulation_steps',
        desc: '梯度累积步数',
        type: 'inputNumber',
        required: true,
        min: 1,
      },
      {
        label: '聊天模板',
        value: 'chat_template',
        desc: '聊天模板',
        type: 'input',
        required: true,
      },
      {
        label: '模型类型',
        value: 'model_type',
        desc: '模型类型',
        type: 'input',
        required: true,
      },
      {
        label: '头类型',
        value: 'head_type',
        desc: '头类型',
        type: 'input',
        required: true,
      },
      {
        label: '损失类型',
        value: 'loss_type',
        desc: '损失类型',
        type: 'input',
        required: true,
      },
      {
        label: '是否使用加权损失',
        value: 'weighted_loss',
        desc: '是否使用加权损失',
        type: 'select',
        required: true,
        option: [
          { label: 'True', value: true },
          { label: 'False', value: false },
        ],
      },

      {
        label: '初始化类型',
        value: 'init_type',
        desc: '初始化类型',
        type: 'input',
        required: true,
      },
      {
        label: '从本地加载训练数据',
        value: 'load_train_data_from_disk',
        desc: '是否从本地加载训练数据',
        type: 'select',
        required: true,
        option: [
          { label: 'True', value: true },
          { label: 'False', value: false },
        ],
      },
      {
        label: '预训练模型名称或路径',
        value: 'pretrain_model_name',
        desc: '预训练模型名称或路径 不填使用默认值',
        type: 'input',
        required: !true,
      },
      {
        label: '训练数据路径',
        value: 'train_data_path',
        desc: '训练数据路径 不填使用默认值',
        type: 'input',
        required: !true,
      },
      {
        label: '验证数据路径',
        value: 'val_data_path',
        desc: '验证数据路径 不填使用默认值',
        type: 'input',
        required: !true,
      },

      {
        label: 'DeepSpeed配置路径',
        value: 'deepspeed_config_path',
        desc: 'DeepSpeed配置路径 不填使用默认值',
        type: 'input',
        required: !true,
      },
      {
        label: '输出目录',
        value: 'output_dir',
        desc: '输出目录 不填使用默认值',
        type: 'input',
        required: !true,
      },
    ],
    questionRow: 4,
  });

  const integration = async () => {
    await deployRef.value?.validateFields().then(async () => {
      state.loading = true;
      let needSkip = undefined;
      try {
        const { modelList, question, top_K, fuserModelName } = formState;
        state.loading = true;
        let param: any = {},
          restul: any = { id: uuid() };
        if (state.fun) {
          param = { prompt: question, model_list: modelList };
          const data = (await executeP2L(param)) as any;
          const { need_training, chosen_model, answer } = data;
          // 需要训练
          if (need_training && need_training === true) {
            message.info('模型未找到，请先进行模型训练，在进行尝试！');
            state.open = true;
          } else if (chosen_model && answer) {
            restul = { ...restul, ...data };
            needSkip = restul.id;
            message.success('已成功比较模型！');
          }
        } else {
          param = { contents: [question], top_K, models: modelList, fusion_model: fuserModelName };
          const data = (await executeBlend(param)) as any;
          const { fused_result } = data;
          if (fused_result) {
            restul = { ...restul, ...data };
            needSkip = restul.id;
            message.success('执行成功！');
          }
        }

        let integration: any = JSON.parse(getLocalItem('integration')) || undefined;
        let created_at = dayjs(new Date()).format('YYYY-MM-DD HH:mm:ss');
        if (Object.keys(restul).length > 2) {
          let obj: any = { req: param, res: restul, created_at };
          if (integration) {
            integration[Number(!state.fun)].push(obj);
            setLocalItem('integration', integration);
          } else setLocalItem('integration', state.fun ? [[obj], []] : [[], [obj]]);
        }
      } catch (error) {}
      state.loading = false;
      if (needSkip) history(needSkip);
    });
  };

  const history = (id: string) => {
    router.push({
      path: '/history',
      query: { ind: Number(!state.fun), ...route.query, ...(typeof id == 'string' ? { id } : {}) },
    });
  };

  const reset = () => {
    formState.question = '';
    (formState.modelList = []),
      (formState.top_K = 2),
      (formState.fuserModelName = 'llm-blender/gen_fuser_3b'),
      (formState.general_parameters = init_general_parameters);
  };

  // p2l 模型训练
  const drill = async () => {
    await drillRef.value.validateFields();
    console.log(formState.general_parameters);
    const { modelList, general_parameters } = formState;
    state.drillLoading = true;
    try {
      const { task_id } = (await routerDrill({ model_list: modelList, ...general_parameters })) as any;
      if (task_id) {
        const { status } = await drillStatus(task_id);
        if (status == 'pending') state.p2l_task_id = task_id;
      } else {
        message.info('本地匹配数据集未找到，请联系开发上传数据集！');
        state.drillLoading = false;
      }
    } catch (error) {
      state.drillLoading = false;
    }
  };

  const radioClick = () => {
    const { id, ind } = route.query as any;
    if (id && ind) {
      router.replace({
        path: '/integration',
        query: {},
      });
    }
  };

  // 监听 p2l 模型训练状态
  watch(
    () => state.p2l_task_id,
    async (val, old) => {
      if (val) {
        state.drillInterval = setInterval(async () => {
          const { status } = await drillStatus(val);
          if (status == 'completed' && state.drillInterval) {
            clearInterval(state.drillInterval);
            state.p2l_task_id = undefined;
            state.drillLoading = false;
            message.success('已训练完成！');
            state.open = false;
            await integration();
          }
        }, 10000);
      }
    },
    { immediate: true, deep: true },
  );

  watch(
    () => state.loading,
    (val, oldval) => {
      if (val === true) {
      }
    },
  );

  watch(
    () => formState.question,
    (val, oldval) => {
      if (val.split(/\r?\n/).length > 5) {
        state.questionRow = 8;
      }
    },
    { deep: true },
  );

  onMounted(async () => {
    const { id, ind } = route.query as any;
    if (id && ind) {
      state.fun = !Number(ind);
      if (getLocalItem('integration')) {
        const list = JSON.parse(getLocalItem('integration'));
        const obj: any = list[ind].find((e: any) => e.res.id == id);

        let rest: any = {};
        if (Number(ind)) {
          (rest.question = obj.req.contents.join(';')), (rest.modelList = obj.req.models);
          rest.top_K = obj.req.top_K;
          rest.fuserModelName = obj.req.fusion_model;
        } else {
          rest.question = obj.req.prompt;
          rest.modelList = obj.req.model_list;
        }
        Object.assign(formState, {
          ...rest,
        });
      }
    }
    if (modelRef.value) {
      modelRef.value.addEventListener('wheel', (e: any) => {
        e.preventDefault(); // 阻止默认垂直滚动
        modelRef.value.scrollLeft += e.deltaY; // 横向滚动
        // 可选：增加滚动惯性
        modelRef.value.scrollLeft += e.deltaY * 0.5;
      });
    }
  });
</script>

<template>
  <div class="integration overflow-scroll">
    <div style="display: flex; align-items: center; justify-content: space-between">
      <a-radio-group
        v-model:value="state.fun"
        :disabled="state.loading"
        size="large"
        style="margin-bottom: 20px"
        @click="radioClick"
      >
        <a-radio-button :value="true">P2L</a-radio-button>
        <a-radio-button :value="false">LLM-Blender</a-radio-button>
      </a-radio-group>
      <div>
        <a-button :disabled="state.loading" type="link" @click="reset">重置</a-button>
        <a-button :disabled="state.loading" type="link" @click="history">历史任务</a-button>
      </div>
    </div>

    <a-form ref="deployRef" :model="formState" name="basic" autocomplete="off">
      <div>
        <ConfigPackage
          :expand="upStatus.compare"
          :expandClick="() => closeUpStatus('compare', upStatus)"
          label="模型选择"
          spacious
        >
          <!-- <a-alert message="请选择你需要比较的模型！" type="info" /> -->
          <a-form-item name="modelList" :rules="[{ required: true, message: '请选择模型！' }]">
            <a-checkbox-group v-model:value="formState.modelList">
              <div class="model-list overflow-scroll" ref="modelRef">
                <div>
                  <a-card hoverable style="position: relative; width: 200px" v-for="(n, i) in state.modelList" :key="i">
                    <a-checkbox :value="n.name">
                      <a-card-meta :title="n.name" :description="n.description"> </a-card-meta>
                    </a-checkbox>
                  </a-card>
                </div>
              </div>
            </a-checkbox-group>
          </a-form-item>
        </ConfigPackage>
      </div>

      <ConfigPackage
        v-if="!state.fun"
        spacious
        :expand="upStatus.course"
        :expandClick="() => closeUpStatus('course', upStatus)"
        label="模型排序和过滤"
      >
        <div :class="{ course: true, dotted: state.loading }">
          <a-steps
            :current="-1"
            disabled
            :items="[
              {
                title: '排序模型配置',
                description: '',
                // icon: false && state.loading ? h(LoadingOutlined) : h(Loading3QuartersOutlined),
              },
              {
                title: '过滤模型配置',
                description: '',
                // icon: false && state.loading ? h(LoadingOutlined) : h(Loading3QuartersOutlined),
              },
            ]"
          ></a-steps>
          <a-form class="defcourse" labelAlign="right" :label-col="{ style: { width: '80px' } }">
            <div style="margin: 15px 0; font-size: 15px; font-weight: 600">排序模型</div>
            <a-form-item label="排序模型">
              <a-input value="Rank Model" disabled style="width: 100%"> </a-input>
            </a-form-item>
            <div style="margin: 15px 0; font-size: 15px; font-weight: 600">过滤模型</div>
            <a-form-item label="融合模型">
              <a-select v-model:value="formState.fuserModelName" style="width: 100%">
                <a-select-option value="llm-blender/gen_fuser_3b">llm-blender/gen_fuser_3b</a-select-option>
                <a-select-option value="lmsys/vicuna-7b-v1.5">lmsys/vicuna-7b-v1.5</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="top_K">
              <a-input-number
                :min="1"
                :max="formState.modelList.length || 2"
                allowClear
                v-model:value="formState.top_K"
                style="width: 100%"
                placeholder="请选择融合个数"
              ></a-input-number>
            </a-form-item>
          </a-form>
        </div>
      </ConfigPackage>

      <div class="fotter">
        <a-form-item name="question" :rules="[{ required: true, message: '请填写问题！' }]">
          <a-textarea
            v-model:value="formState.question"
            placeholder="请输入你要想提出的问题！"
            :rows="state.questionRow"
          />
        </a-form-item>
        <a-button type="primary" @click="integration" :loading="state.loading">发送</a-button>
      </div>
    </a-form>

    <a-drawer
      v-model:open="state.open"
      class="custom-class"
      root-class-name="root-class-name"
      title="ROUTER 配置"
      placement="right"
      size="large"
      @after-open-change="() => {}"
    >
      <ConfigPackage
        :expand="upStatus.parameter"
        :expandClick="() => closeUpStatus('parameter', upStatus)"
        label="参数配置"
      >
        <div class="parameter">
          <a-form
            ref="drillRef"
            labelAlign="right"
            :model="formState.general_parameters"
            :label-col="{ style: { width: '170px' } }"
          >
            <div class="parameter-items" v-for="item in state.general_parameters" :key="item.value">
              <a-form-item
                :name="[item.value]"
                :rules="[
                  {
                    required: item.required,
                    message: ['input', 'inputNumber'].includes(item.type) ? '请输入' : '请选择',
                  },
                ]"
              >
                <template #label>
                  <span>{{ item.label }}</span>
                  <Tooltip v-if="item.desc">
                    <template #title>{{ item.desc }}</template>
                    <QuestionCircleFilled class="ml-5px" />
                  </Tooltip>
                </template>
                <a-input
                  v-if="item.type === 'input'"
                  v-model:value="formState.general_parameters[`${item.value}`]"
                  :placeholder="item.desc || '请输入'"
                />
                <a-input-number
                  v-if="item.type === 'inputNumber'"
                  :min="item.min"
                  :max="item.max"
                  :step="item.step || 1"
                  :precision="item.precision || 0"
                  v-model:value="formState.general_parameters[`${item.value}`]"
                  :style="{ width: '100%' }"
                  placeholder="请输入"
                ></a-input-number>
                <a-select
                  v-if="item.type === 'select'"
                  v-model:value="formState.general_parameters[`${item.value}`]"
                  style="width: 100%"
                >
                  <a-select-option v-for="opt in item.option" :value="opt.value" :key="item.value">{{
                    opt.label
                  }}</a-select-option>
                </a-select>
                <a-checkbox
                  v-if="item.type === 'checkbox'"
                  v-model:checked="formState.general_parameters[`${item.value}`]"
                ></a-checkbox>
              </a-form-item>
            </div>
          </a-form>
          <div class="h-36px mt-10px flex flex-justify-end">
            <a-button type="primary" :loading="state.drillLoading" @click="drill"
              >{{ state.drillLoading ? '训练中' : '开始训练' }}
            </a-button>
          </div>
        </div>
      </ConfigPackage>
    </a-drawer>
  </div>
</template>

<style lang="less" scoped>
  .integration {
    height: 100%;
    overflow: scroll;

    > div {
      margin-bottom: 20px;
    }

    .model-list {
      width: 100%;
      margin: 30px 30px 0;

      > div {
        display: flex;
        flex-wrap: nowrap;
        gap: 20px;
        width: 100%;
        margin-bottom: 25px;

        > div {
          flex: 0 0 auto;
          width: 300px;

          :deep(.ant-checkbox:not(.ant-checkbox-checked)) {
            position: absolute;
            top: 0;
            right: 0;
            display: none;
          }

          &:hover {
            :deep(.ant-checkbox) {
              display: block;
            }
          }
        }
      }

      :deep(.ant-checkbox-checked) {
        position: absolute;
        top: 0;
        right: 0;
      }
    }

    .router-but {
      width: 300px;
      height: 50px;
      margin: 20px;
      font-size: 20px;
      font-weight: 600;
    }

    .fotter {
      display: flex;
      flex-direction: row;
      align-items: center;
      margin-top: 20px;
      margin-bottom: 20px;

      > div:nth-child(1) {
        flex: 1;
        margin-bottom: 22px;
      }

      > button {
        height: 54px;
        padding: 0 30px;
        margin: 0 0 22px 20px;
      }
    }

    .course {
      width: 100%;
      height: 340px;

      > div:nth-child(1) {
        margin-top: 20px;
        margin-bottom: 20px;
      }

      .defcourse {
        margin: 20px;
      }

      .stepdot {
        position: absolute;
        bottom: -15px;
        left: -10px;
        width: 20px;
        height: 20px;
        border: 1px solid;
        border-radius: 50%;
      }
    }

    :deep(.ant-checkbox-group) {
      width: 100% !important;
    }

    :deep(.ant-radio-button-wrapper) {
      width: 130px;
      text-align: center;
    }

    :deep(.dotted .ant-steps-item-title::after) {
      height: 2px;
      background: repeating-linear-gradient(
        to right,
        #1677ff 0,
        #1677ff 5px,
        transparent 0,
        transparent 10px
      ) !important;
    }
  }
</style>
