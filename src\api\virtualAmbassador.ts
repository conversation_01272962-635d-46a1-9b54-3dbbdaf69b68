import request from '@/utils/request';
import { getEnv } from '@/utils';

// 定义宽松的对象类型
type TLooseObject = Record<string, unknown>;

const { VITE_APP_AVATAR_URL } = getEnv();

const AVATAR = VITE_APP_AVATAR_URL;

export function upload(params: FormData, config: TLooseObject) {
  return request.$Axios
    .post(`/virtual-brand-ambassador/material/upload[${AVATAR}]`, params, config)
    .then((data: string[]) => data as string[]);
}

export function generalVirtualAmbassador(params?: TLooseObject) {
  return request.$Axios.post(`/virtual-brand-ambassador/image/generate[${AVATAR}]`, params);
}

export function nameValidity(params?: TLooseObject) {
  return request.$Axios.get(`/virtual-brand-ambassador/name_validity[${AVATAR}]`, params);
}

export function getVirtualAmbassadorList(params?: TLooseObject) {
  return request.$Axios.post(`/virtual-brand-ambassador/image/list[${AVATAR}]`, params);
}

export function saveVirtualAmbassador(params?: TLooseObject) {
  // 使用request.$Axios.post方法，发送POST请求，保存虚拟大使的图片
  return request.$Axios.post(`/virtual-brand-ambassador/image/save[${AVATAR}]`, params);
}

export function deleteVirtualAmbassador(params?: TLooseObject) {
  // 使用request.$Axios.post方法，发送POST请求，保存虚拟大使的图片
  return request.$Axios.post(`/virtual-brand-ambassador/image/delete[${AVATAR}]`, params);
}
