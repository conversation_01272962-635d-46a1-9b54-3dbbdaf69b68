<script setup lang="ts">
  import { reactive, ref, onMounted } from 'vue';
  import type { IFormItem } from '@/components/CustomForm/index.vue';
  import { CustomForm } from '@/components';
  import type { ColumnType } from 'ant-design-vue/es/table';
  import { useRoute } from 'vue-router';
  import { fetchClusterEvent, fetchPodEvent, fetchServiceEvent } from '@/api/k8s';
  import type { IClusterEvent } from '@/interface/kubernetes';
  import { convertIsoTimeToLocalTime } from '@/utils/common';
  const route = useRoute();
  const tableHeight = ref(0);
  const spinning = ref(false);
  interface IEventSearchState {
    event_type: string;
  }
  const DEFAULT_SEARCHSTATE: IEventSearchState = {
    event_type: '',
  };
  const searchState: IEventSearchState = reactive({ ...DEFAULT_SEARCHSTATE });
  const dataSource = reactive<IClusterEvent[]>([]);
  const formConfig: IFormItem[] = [
    {
      field: 'event_type',
      type: 'select',
      label: '类型',
      placeholder: '请选择',
      options: [
        { label: 'Warning', value: 'Warning' },
        { label: 'Normal', value: 'Normal' },
      ],
    },
  ];
  const columns: ColumnType[] = [
    // TODO 联调字段
    { title: '原因', dataIndex: 'reason' },
    { title: '重复次数', dataIndex: 'count' },
    { title: '时间', dataIndex: 'last_time' },
    { title: '类型', dataIndex: 'type' },
    { title: '消息', dataIndex: 'message', width: 360 },
  ];
  const onFinish = (values: Record<string, string>) => {
    const searchConfig: { [key: string]: string } = {};
    for (const key in values) {
      searchConfig[key] = values[key];
    }
    Object.assign(searchState, { ...searchConfig });
    getList();
  };

  const onRest = () => {
    Object.assign(searchState, { ...DEFAULT_SEARCHSTATE });
    getList();
  };

  const getList = async () => {
    // 获取数据
    const params: { name: string; event_type?: string } = {
      name: route.params.name as string,
    };
    if (searchState.event_type) {
      params.event_type = searchState.event_type;
    }
    let data: IClusterEvent[] = [];
    if (route.name === 'pod-detail') {
      data = await fetchPodEvent(params);
    } else if (route.name === 'workload-detail') {
      data = await fetchClusterEvent(params);
    } else if (route.name === 'service-detail') {
      data = await fetchServiceEvent(params);
    }
    dataSource.length = 0;
    dataSource.push(...data);
  };
  const getTableHeight = () => {
    const tableItem = document.querySelector('.ant-tabs-content-holder');
    tableHeight.value = tableItem?.clientHeight as number;
  };
  onMounted(async () => {
    await getList();
    getTableHeight();
  });
</script>

<template>
  <a-spin :spinning="spinning">
    <CustomForm :form-items="formConfig" @on-finish="onFinish" @on-rest="onRest" />
    <a-table :data-source="dataSource" :columns="columns" :pagination="false" :scroll="{ y: tableHeight - 185 }">
      <template #bodyCell="{ column, text }">
        <div v-if="['last_time'].includes(column.dataIndex)">
          {{ convertIsoTimeToLocalTime(text) }}
        </div>
        <div v-else-if="column.dataIndex === 'message'" v-ellipse-tooltip.top>
          {{ text }}
        </div>
        <div v-else>{{ text === undefined || text === null ? '--' : text }}</div>
      </template>
    </a-table>
  </a-spin>
</template>

<style scoped lang="less"></style>
