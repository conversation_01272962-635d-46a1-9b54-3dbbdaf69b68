<template>
  <div class="page">
    <div class="top">
      <span style="cursor: pointer" @click="router.back()">
        <LeftOutlined />
        创建课程
      </span>
      <span class="center-title">
        <span v-if="!isEdit">{{ projectData.name }}</span>
        <span v-else>
          <a-input
            v-model:value="projectData.name"
            show-count
            :style="{ width: '200px' }"
            :maxlength="20"
            allow-clear
            @blur="handleNameBlur"
          />
        </span>
        <EditOutlined
          style="margin-left: 5px"
          @click="
            if (isEdit) {
              updateProjectName(projectData.name);
              isEdit = !isEdit;
            } else {
              dialogData.tempName = projectData.name; // 保存当前名称
              isEdit = !isEdit;
            }
          "
        />
      </span>
      <span>
        <span v-if="projectData.updated_at" style="color: #646566">
          上一次保存 {{ dayjs(projectData.updated_at).format('YYYY-MM-DD HH:mm:ss') }}
        </span>
        <a-button style="margin-left: 16px" @click="updateLensData(selectLens, true)">保存</a-button>
        <a-button style="margin-left: 16px" @click="goPreview">预览</a-button>
        <a-button type="primary" style="margin-left: 16px" @click="openDig('合成')">合成课程</a-button>
      </span>
    </div>
    <div class="content">
      <div class="left">
        <a-tabs v-model:active-key="activeKey" centered :tab-bar-gutter="30" :animated="false" style="flex-wrap: wrap">
          <a-tab-pane key="1" tab="我的课件">
            <div style="height: 120px">
              <UploadSingle
                :accept="[
                  'application/pdf',
                  'application/vnd.ms-powerpoint',
                  'application/vnd.openxmlformats-officedocument.presentationml.presentation',
                  'application/msword',
                  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                ]"
                :file-size="10"
                @get-upload-data="getUploadData"
              ></UploadSingle>
            </div>
            <div class="coursewareBox">
              <template v-for="(item, index) in coursewareList" :key="index">
                <span
                  v-if="index === 0 || item.original_name !== coursewareList[index - 1].original_name"
                  class="original-name"
                >
                  <span class="file-icon"></span>
                  {{ removeFileExt(item.original_name) }}
                </span>
                <div
                  v-if="item.image_url"
                  :style="{
                    backgroundImage: `url(${item.image_url})`,
                  }"
                  class="courseware"
                  @click="updateLensData(item)"
                ></div>
              </template>
            </div>
          </a-tab-pane>
          <a-tab-pane key="2" tab="我的素材">
            <div style="height: 120px">
              <UploadSingle
                :file-size="10"
                :accept="['image/jpeg', 'image/jpg', 'image/png']"
                text1="点击或拖拽上传图片"
                text2="支持jpg、jpeg、png"
                @get-upload-data="getUploadData1"
              ></UploadSingle>
            </div>
            <div class="coursewareBox">
              <div
                v-for="(item, index) in materialList"
                :key="index"
                :style="{
                  backgroundImage: `url(${item.image_url})`,
                }"
                class="courseware"
                @click="updateLensData(item)"
              >
                <div class="delBox" @click.stop="delMater(item.id)">
                  <img class="del" :src="del" />
                </div>
              </div>
            </div>
          </a-tab-pane>
        </a-tabs>
      </div>

      <div class="right">
        <div v-if="coursewareList.length == 0" class="offData">
          <img :src="upload" alt="icon" />
          <div>请先在左侧上传课件，创建课程分镜</div>
        </div>
        <div v-else class="noData">
          <div class="top">
            <span>{{ lensName }}</span>
            <div class="sound">
              <a-button class="digitalBtn" @click="opneDialog()">
                数字人
                <img v-if="selectHuman.image_url" :src="selectHuman.image_url" alt="icon" class="digitalHuman" />
              </a-button>
              <a-select
                v-model:value="selectLens.speaker"
                style="width: 100px"
                :options="toneOptions"
                @change="updateLensData(selectLens)"
              >
              </a-select>
            </div>
          </div>
          <div class="centerBox">
            <div class="center">
              <div class="imageBox" :style="{ backgroundImage: `url(${selectLens?.image_url})` }"></div>
              <div class="videoBox">
                <img v-if="selectHuman.image_url" class="human" :src="selectHuman.image_url" alt="human" />
              </div>
            </div>
            <div class="centerRight relative">
              <div v-if="copywritingLoading" class="copywriting-mask">
                <a-spin size="large" />
              </div>
              <a-tabs
                v-model:active-key="activeKey1"
                centered
                :tab-bar-gutter="30"
                :animated="false"
                style="flex-wrap: wrap"
              >
                <a-tab-pane key="1">
                  <template #tab>
                    <span>配音文案</span>
                  </template>
                  <div class="rightTop">
                    <span style="">文案内容</span>
                    <a-tooltip title="基于分镜材料生成">
                      <a-button
                        class="AIBtn"
                        :disabled="!selectLens.image_url"
                        @click="selectLens.image_url && openDig('文案')"
                      >
                        <img :src="aiIcon" alt="icon" />
                        AI生成
                      </a-button>
                    </a-tooltip>
                  </div>
                  <div class="rightContent">
                    <div class="mytextarea">
                      <a-textarea
                        v-model:value="selectLens.text"
                        placeholder="请输入文案内容"
                        :rows="14"
                        :maxlength="9999"
                        @blur="updateLensData(selectLens)"
                      />
                      <div class="num">{{ selectLens?.text?.length || 0 }}/9999</div>
                    </div>
                    <div
                      class="rightBottom"
                      :class="{ disabled: !selectLens?.text, playing: isOnPlay }"
                      @click="listening"
                    >
                      <img v-if="isOnPlay" :src="onplay" alt="icon" />
                      <img v-else :src="playIcon" alt="icon" />
                      试听
                    </div>
                  </div>
                </a-tab-pane>
                <a-tab-pane key="2">
                  <template #tab>
                    <span>答疑设置</span>
                  </template>
                  <div class="rightTop">
                    <span>答疑对</span>
                    <a-tooltip title="基于配音文案生成">
                      <a-button class="AIBtn" :disabled="!selectLens.image_url" @click="openDig('答疑')">
                        <img :src="aiIcon" alt="icon" />
                        AI生成
                      </a-button>
                    </a-tooltip>
                  </div>
                  <div class="rightContent">
                    <a-form ref="formRef" :model="fromList" class="answersForm">
                      <div v-for="(item, index) in fromList" :key="index" class="answersBox">
                        <div class="answersTop">
                          答疑对{{ index + 1 }}
                          <DeleteOutlined class="del" @click="delAnswerData(item.id)" />
                        </div>
                        <div class="title">问题</div>
                        <a-form-item
                          label=""
                          :name="[index, 'question']"
                          :rules="{ required: true, trigger: 'blur', message: '不能为空' }"
                        >
                          <a-textarea
                            v-model:value="item.question"
                            placeholder="请输入文案内容"
                            :rows="8"
                            style="height: 70px"
                            @blur="updateAnswerData(item)"
                          />
                        </a-form-item>
                        <div class="title">选项</div>
                        <a-form-item
                          label="A"
                          :name="[index, 'answer', 'A']"
                          :rules="{ required: true, trigger: 'blur', message: '不能为空' }"
                        >
                          <a-tooltip :title="item.answer?.A || ''" placement="top" :mouse-enter-delay="0.5">
                            <a-input
                              v-model:value="item.answer.A"
                              placeholder="请输入"
                              allow-clear
                              :maxlength="20"
                              @blur="updateAnswerData(item)"
                            />
                          </a-tooltip>
                        </a-form-item>
                        <a-form-item
                          label="B"
                          :name="[index, 'answer', 'B']"
                          :rules="{ required: true, trigger: 'blur', message: '不能为空' }"
                        >
                          <a-tooltip :title="item.answer?.B || ''" placement="top" :mouse-enter-delay="0.5">
                            <a-input
                              v-model:value="item.answer.B"
                              placeholder="请输入"
                              allow-clear
                              :maxlength="20"
                              @blur="updateAnswerData(item)"
                            />
                          </a-tooltip>
                        </a-form-item>
                        <a-form-item
                          label="C"
                          :name="[index, 'answer', 'C']"
                          :rules="{ required: true, trigger: 'blur', message: '不能为空' }"
                        >
                          <a-tooltip :title="item.answer?.C || ''" placement="top" :mouse-enter-delay="0.5">
                            <a-input
                              v-model:value="item.answer.C"
                              placeholder="请输入"
                              allow-clear
                              :maxlength="20"
                              @blur="updateAnswerData(item)"
                            />
                          </a-tooltip>
                        </a-form-item>
                        <a-form-item
                          label="D"
                          :name="[index, 'answer', 'D']"
                          :rules="{ required: true, trigger: 'blur', message: '不能为空' }"
                        >
                          <a-tooltip :title="item.answer?.D || ''" placement="top" :mouse-enter-delay="0.5">
                            <a-input
                              v-model:value="item.answer.D"
                              placeholder="请输入"
                              allow-clear
                              :maxlength="20"
                              @blur="updateAnswerData(item)"
                            />
                          </a-tooltip>
                        </a-form-item>
                        <div class="title">答案</div>
                        <a-form-item
                          label=""
                          :name="[index, 'true_answer']"
                          :rules="{ required: true, trigger: 'change', message: '不能为空' }"
                        >
                          <a-radio-group v-model:value="item.true_answer" @change="updateAnswerData(item)">
                            <a-radio value="A">A</a-radio>
                            <a-radio value="B">B</a-radio>
                            <a-radio value="C">C</a-radio>
                            <a-radio value="D">D</a-radio>
                          </a-radio-group>
                        </a-form-item>
                      </div>
                    </a-form>
                    <a-button type="dashed" style="width: 100%" @click="addProblem">
                      <template #icon>
                        <PlusOutlined />
                      </template>
                      增加问答
                    </a-button>
                  </div>
                </a-tab-pane>
              </a-tabs>
            </div>
          </div>
          <div class="bottom">
            <div style="color: #333333; margin-bottom: 16px; font-size: 12px">
              当前{{ lensName }}，共{{ projectData?.images?.length }}个分镜
            </div>
            <div class="lensList">
              <div
                v-for="(item, index) in projectData?.images"
                :key="index"
                :style="{
                  backgroundImage: `url(${item.image_url ? item.image_url : emptyBgc})`,
                  border: item.active ? ' 2px solid #1777FF' : '1px solid #edeff2',
                }"
                class="lensItem"
                @click="chooseLens(item, index)"
              >
                <div
                  class="serialNum"
                  :style="{
                    backgroundColor: item.active ? '#1777FF' : '#D9E9FF',
                    color: item.active ? '#fff' : '#000',
                  }"
                >
                  {{ index + 1 }}
                </div>
                <div class="delBox" @click.stop="delLensData(item.id, index)">
                  <img class="del" :src="del" />
                </div>
              </div>
              <div class="lensItem1" @click="addLensData">
                <PlusOutlined style="font-size: 40px; color: #d1cccc" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 选择数字人弹窗 -->
    <a-modal
      v-if="humanVisble"
      v-model:open="humanVisble"
      :width="1015"
      title="选择数字人"
      class="dialog"
      @cancel="cancelHuman"
    >
      <div class="humanPage">
        <div
          v-for="(item, index) in humanList"
          :key="index"
          class="humanItem"
          :class="{ disabled: item.disabled }"
          :style="{
            backgroundImage: `url(${item.image_url})`,
            border: item.active ? '2px solid #1777FF' : 'none',
            opacity: item.disabled ? '0.5' : '1',
          }"
          @click="item.disabled ? null : choosehuman(item, index)"
        ></div>
      </div>

      <template #footer>
        <a-button type="primary" @click="updateLensData">全局应用</a-button>
      </template>
    </a-modal>
    <!-- AI生成二次确认弹窗 -->
    <a-modal
      v-if="dialogData.dialogVisble"
      v-model:open="dialogData.dialogVisble"
      :title="dialogData.dialogTit"
      @ok="handleOk"
      @cancel="handleCancel"
    >
      <a-form v-if="dialogData.dialogTit == '确定合成课程？'" ref="formRef1" :model="projectData">
        <div style="margin-bottom: 8px">课程名称</div>
        <a-form-item label="" name="name" :rules="{ required: true, trigger: 'blur', message: '不能为空' }">
          <a-input v-model:value="projectData.name" placeholder="请输入" allow-clear :maxlength="20" />
        </a-form-item>
      </a-form>
      <p v-else>{{ dialogData.dialogText }}</p>
    </a-modal>
    <a-modal
      v-model:open="previewVisible"
      title="预览视频"
      width="1015px"
      :footer="null"
      centered
      class="preview-modal"
      wrap-class-name="preview-modal-wrap"
      @cancel="handlePreviewClose"
    >
      <div class="previewBox">
        <div class="preview">
          <div
            class="previewLeft"
            :style="{
              backgroundImage: `url(${projectData?.images?.[currentPreviewIndex]?.image_url})`,
              backgroundSize: 'cover',
              backgroundPosition: 'center',
            }"
          ></div>
          <div class="previewRight">
            <img class="human" :src="selectHuman.image_url" alt="human" />
          </div>
        </div>
        <div class="palyBtn">
          <img
            :src="previewPlaying && currentAudioIndex === currentPreviewIndex ? onplayIcon : playIcon"
            alt="icon"
            style="cursor: pointer"
            @click="playPreviewAudio(currentPreviewIndex)"
          />
        </div>
        <div class="previewBottom">
          <div
            v-for="(item, index) in projectData?.images"
            :key="index"
            class="previewItem"
            :class="{ active: currentPreviewIndex === index }"
            :style="{
              backgroundImage: `url(${item.image_url})`,
              backgroundSize: 'cover',
              backgroundPosition: 'center',
              border: currentPreviewIndex === index ? '2px solid #1777FF' : '1px solid #edeff2',
            }"
            @click="handlePreviewChange(index)"
          >
            <div class="hover-play-icon">
              <img
                :src="previewPlaying && currentAudioIndex === index ? play : pause"
                alt="icon"
                @click.stop="playPreviewAudio(index)"
              />
            </div>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
  <!-- 加载中 -->
  <div v-if="loading" class="mask">
    <a-spin size="large" />
  </div>
</template>
<script lang="ts" setup>
  import { ref, onMounted, reactive, unref } from 'vue';
  import { LeftOutlined, EditOutlined, PlusOutlined, DeleteOutlined } from '@ant-design/icons-vue';
  import dayjs from 'dayjs';
  import { message, Modal } from 'ant-design-vue';
  import {
    addProject,
    updateProject,
    getCourse,
    analysisPPT,
    getMaterial,
    delMaterial,
    addLens,
    updateLens,
    delLens,
    addAnswer,
    getAnswer,
    updateAnswer,
    delAnswer,
    tts,
    aiCopywriting,
    aiAnswer,
    synthesisVideo,
  } from '@/api/classRoom';
  import { useRoute, useRouter } from 'vue-router';
  import { toneOptions, HumanType } from '@/utils/enum';
  import playIcon from '@/assets/image/base/pictures/play1.png';
  import upload from '@/assets/image/base/pictures/upload.png';
  import emptyBgc from '@/assets/image/base/pictures/emptyBgc.png';
  import del from '@/assets/image/base/pictures/del.png';
  import aiIcon from '@/assets/image/base/pictures/aiIcon.png';
  import onplay from '@/assets/image/base/pictures/onplay.gif';
  import onplayIcon from '@/assets/image/base/pictures/播放2.png';
  import play from '@/assets/image/base/pictures/播放1.png';
  import pause from '@/assets/image/base/pictures/暂停1.png';

  import UploadSingle from '@/components/uploadSingle/index.vue';
  import { getLocalItem, playAudio } from '@/utils/common';

  const route = useRoute();
  const router = useRouter();
  const projectId = ref<any>('');
  const loading = ref<any>(false);
  const copywritingLoading = ref<boolean>(false);
  const isEdit = ref<boolean>(false);
  const activeKey = ref<string>('1');
  const activeKey1 = ref<string>('1');
  const lensName = ref<string>('分镜1');
  const formRef = ref();
  const formRef1 = ref();
  const selectLens = ref<any>({}); //选中的分镜
  const lensIndex = ref<number>(0); //选中的分镜
  const selectHuman = ref<any>({}); //选中的数字人
  const selectHumanOld = ref<any>({}); //选中的数字人(旧数据)
  const humanVisble = ref<boolean>(false); //弹窗开关
  const isOnPlay = ref<boolean>(false); //是否试听中
  const fromList = ref<any>([]);
  const projectData = ref<any>({
    name: '',
    images: [],
    active: false,
  });
  const coursewareList = ref<any>([]); //课件列表
  const materialList = ref<any>([]); //素材列表
  const userInfo = JSON.parse(getLocalItem('HQSK_AI_PLATFORM_FRONTEND_USERINFO') || '{}');
  const humanList = ref<any>([
    {
      id: '../../../assets/image/avatar/2d.png',
      image_url: new URL('../../../assets/image/avatar/2d.png', import.meta.url).href,
      type: '2d',
      unEditor: true,
      active: true,
      disabled: false,
    },
    // {
    //   id: '../../../assets/image/avatar/3d.png',
    //   image_url: new URL('../../../assets/image/avatar/3d.png', import.meta.url).href,
    //   type: '3d',
    //   unEditor: true,
    //   active: false,
    //   disabled: true,
    // },
  ]);
  const dialogData = reactive<any>({
    dialogSign: '', //对话框标识
    dialogVisble: false, //对话框开关
    dialogTit: '', //对话框标题
    dialogText: '', //对话框内容同
    tempName: '', // 临时存储课堂名称
  });
  const previewVisible = ref(false);
  const currentPreviewIndex = ref(0);
  const previewPlaying = ref(false);
  const currentAudioIndex = ref(-1);
  const currentAudio = ref<HTMLAudioElement | null>(null); // 添加当前音频的引用

  const fileUrl = ref<string>('');
  const file_info = ref<any>([]);

  const removeFileExt = (name: string) => {
    if (!name) return '';
    return name.replace(/\.[^/.]+$/, '');
  };

  onMounted(() => {
    // 重置所有状态
    lensIndex.value = 0;
    selectLens.value = {};
    fromList.value = [];
    coursewareList.value = [];
    file_info.value = []; // 重置 file_info 数组
    projectData.value = {
      name: '',
      images: [],
      active: false,
    };

    // 获取新的 projectId
    if (route.query?.classId) {
      //编辑或复制编辑
      const newProjectId = Number(route.query?.classId);
      sessionStorage.setItem('projectId', newProjectId.toString());
      projectId.value = newProjectId;
      getClassDetail(newProjectId);
    } else {
      // 新增
      projectData.value.name = '课程' + dayjs().format('YYYYMMDD');
      if (projectId.value) {
        getClassDetail(projectId.value);
      } else {
        addProjectData();
      }
    }
    getMaterialList(); //获取素材列表
  });

  //获取课程详情
  const getClassDetail = async (classId: number) => {
    loading.value = true;
    try {
      let res: any = await getCourse({ id: classId });
      if (!res) {
        message.error('获取课程信息失败');
        return;
      }

      // 保存当前选中的数字人类型
      const currentHumanType = selectHuman.value.type;

      // 重置并更新数据
      projectData.value = res || {};
      projectData.value.name = res.name || '课程' + dayjs().format('YYYYMMDD');
      coursewareList.value = JSON.parse(JSON.stringify(res.images)) || [];

      // 初始化临时名称
      dialogData.tempName = projectData.value.name;

      if (projectData.value?.images?.length > 0) {
        lensIndex.value = 0;
        selectLens.value = projectData.value.images[0] || {};
        selectLens.value.speaker = selectLens.value.speaker || 'xingxing';

        // 如果有当前选中的数字人类型，优先使用它
        if (currentHumanType) {
          selectLens.value.type = currentHumanType;
        }
        chooseLens(selectLens.value, 0);
      } else {
        // 如果没有分镜，设置默认音色
        selectLens.value.speaker = 'xingxing';
      }
    } catch (error) {
      console.error('获取课程详情失败:', error);
      message.error('获取课程详情失败');
    } finally {
      loading.value = false;
    }
  };

  //获取课程详情并保持指定分镜选中
  const getClassDetailWithCurrentLens = async (classId: number, targetIndex: number) => {
    loading.value = true;
    try {
      let res: any = await getCourse({ id: classId });
      if (!res) {
        message.error('获取课程信息失败');
        return;
      }

      // 保存当前选中的数字人类型
      const currentHumanType = selectHuman.value.type;

      // 重置并更新数据
      projectData.value = res || {};
      projectData.value.name = res.name || '课程' + dayjs().format('YYYYMMDD');
      coursewareList.value = JSON.parse(JSON.stringify(res.images)) || [];

      // 初始化临时名称
      dialogData.tempName = projectData.value.name;

      if (projectData.value?.images?.length > 0) {
        // 确保目标索引在有效范围内
        const validIndex = Math.min(targetIndex, projectData.value.images.length - 1);
        lensIndex.value = validIndex;
        selectLens.value = projectData.value.images[validIndex] || {};
        selectLens.value.speaker = selectLens.value.speaker || 'xingxing';

        // 如果有当前选中的数字人类型，优先使用它
        if (currentHumanType) {
          selectLens.value.type = currentHumanType;
        }
        chooseLens(selectLens.value, validIndex);
      } else {
        // 如果没有分镜，设置默认音色
        lensIndex.value = 0;
        selectLens.value.speaker = 'xingxing';
      }
    } catch (error) {
      console.error('获取课程详情失败:', error);
      message.error('获取课程详情失败');
    } finally {
      loading.value = false;
    }
  };

  //选择分镜
  const chooseLens = (item: any, index: number) => {
    // 如果当前正在播放，先停止播放
    if (isOnPlay.value && currentAudio.value) {
      currentAudio.value.pause();
      currentAudio.value = null;
      isOnPlay.value = false;
    }

    lensIndex.value = index;
    lensName.value = '分镜' + (index + 1);
    selectLens.value = item;
    if (projectData.value.images.length > 0) {
      projectData.value.images.forEach((n: any, i: number) => {
        if (i === index) {
          n.active = true;
        } else {
          n.active = false;
        }
      });
    }

    // 优先使用分镜的type字段，如果没有则使用当前选中的数字人类型
    const lensType = item.type || selectHuman.value.type || '2d';
    selectHuman.value = lensType == '2d' ? humanList.value[0] : humanList.value[1];

    getAnswerData(); //获取问答
  };
  //添加分镜
  const addLensData = async () => {
    if (isOnPlay.value) {
      message.warning('请先结束语音试听');
      return;
    }

    // 保存当前选中的分镜索引
    const currentIndex = lensIndex.value;

    let res: any = await addLens({
      proj_info_id: projectId.value,
      type: '2d',
      speaker: selectLens.value.speaker || 'xingxing', // 使用当前选中的音色
    });
    if (res) {
      // 重新获取课程详情，但保持当前选中的分镜
      await getClassDetailWithCurrentLens(projectId.value, currentIndex);
    }
  };
  //修改分镜数据||应用全局
  const updateLensData = async (item?: any, pop?: boolean) => {
    // 记住当前分镜id
    const currentId = selectLens.value.id;
    if (item && item.id) {
      // 如果是素材图片，只更新当前分镜的图片，不影响课件列表
      if (materialList.value.some((m: any) => m.id === item.id)) {
        selectLens.value.image_url = item.image_url;
        selectLens.value.text = item.text || selectLens.value.text;
        selectLens.value.speaker = item.speaker || selectLens.value.speaker;
      } else {
        // 如果是课件图片，更新所有相关数据
        selectLens.value.image_url = item.image_url;
        selectLens.value.text = item.text;
        selectLens.value.speaker = item.speaker;
      }
    }

    if (projectData.value.images.length == 0) {
      message.warning('分镜不能为空');
      return;
    }

    // 检查是否是音色变更
    const isSpeakerChange = item && item.speaker && item.speaker !== selectLens.value.speaker;
    const targetSpeaker = isSpeakerChange ? item.speaker : selectLens.value.speaker || 'xingxing';

    // 构建更新数据时，保持课件列表的原始数据不变
    let newArr = projectData.value.images.map((n: any) => {
      // 如果是素材图片，只更新当前分镜的数据
      if (materialList.value.some((m: any) => m.id === n.id)) {
        return {
          duration: n.duration || '5000',
          image_id: n.id,
          image_url: n.image_url,
          speaker: targetSpeaker, // 使用目标音色（全局统一）
          text: n.text,
          type: selectHuman.value.type || '2d',
        };
      }
      // 如果是课件图片，也要更新数字人类型（全局应用时）
      return {
        duration: n.duration || '5000',
        image_id: n.id,
        image_url: n.image_url,
        speaker: targetSpeaker, // 使用目标音色（全局统一）
        text: n.text,
        type: selectHuman.value.type || n.type || '2d',
      };
    });

    let res: any = await updateLens({
      id: projectId.value,
      images: newArr,
      name: projectData.value.name,
    });
    if (res) {
      if (pop) {
        message.success('已保存草稿');
      }
      // 更新 images
      projectData.value.images = res.images;
      // 查找当前分镜
      const newIndex = res.images.findIndex((img: any) => img.id === currentId);
      if (newIndex !== -1) {
        selectLens.value = res.images[newIndex];
        lensIndex.value = newIndex;
        chooseLens(selectLens.value, newIndex);
      } else {
        selectLens.value = res.images[0] || {};
        lensIndex.value = 0;
        chooseLens(selectLens.value, 0);
      }
    } else {
      message.error('修改失败');
    }
  };
  //删除分镜数据
  const delLensData = (id: number, idx: number) => {
    Modal.confirm({
      title: `确认删除分镜${idx + 1}？`,
      content: '删除后，将同步删除对应的配音文案和答疑对',
      onOk: async () => {
        // 判断删除的是否为当前选中的分镜
        const isCurrentLens = idx === lensIndex.value;
        // 保存当前分镜索引，用于后续计算
        const currentIndex = lensIndex.value;

        let res: any = await delLens({ ids: [id] });
        if (res.length > 0) {
          if (isCurrentLens) {
            // 如果删除的是当前分镜，跳转到第一个分镜
            getClassDetail(projectId.value);
          } else {
            // 如果删除的不是当前分镜，需要重新计算索引
            // 如果删除的分镜在当前分镜之前，当前分镜的索引会减1
            let newIndex = currentIndex;
            if (idx < currentIndex) {
              newIndex = currentIndex - 1;
            }
            // 保持当前选中的分镜（索引已调整），但不要切换到被删除的分镜
            await getClassDetailWithCurrentLens(projectId.value, newIndex);
          }
        } else {
          message.error('删除失败');
        }
      },
    });
  };
  //获取问答
  const getAnswerData = async () => {
    if (selectLens.value.id) {
      let res: any = await getAnswer({ image_ids: selectLens.value.id });
      if (res.list) {
        fromList.value = res.list;
      }
    }
  };
  //添加问答
  const addProblem = async () => {
    // 表单校验
    const form = unref(formRef);
    if (!form) return;
    const valid = await form.validate();
    if (!valid) return;
    if (!selectLens.value.id) {
      message.warning('分镜不能为空');
      return;
    }
    let res: any = await addAnswer({
      answer: {},
      image_info_id: selectLens.value.id,
      model_name: HumanType[selectLens.value.type],
      question: '',
      speaker: selectLens.value.speaker || 'xingxing', // 使用当前选中的音色
      true_answer: '',
    });
    if (res == 'OK') {
      getAnswerData();
    } else {
      message.error('添加失败');
    }
  };
  //修改问答
  const updateAnswerData = async (item: any) => {
    const { answer, id, model_name, question, speaker, true_answer } = item;
    if (Object.keys(answer).length === 4) {
      let param = {
        qas: [
          {
            answer: answer,
            id: id,
            model_name: model_name,
            question: question,
            speaker: speaker,
            true_answer: true_answer,
          },
        ],
      };
      let res: any = await updateAnswer(param);
      if (res == 'SUCCESS') {
        // message.success('保存成功');
      } else {
        message.error('保存失败');
      }
    }
  };
  //删除问答
  const delAnswerData = async (id: number) => {
    let res: any = await delAnswer({ ids: [id] });
    if (res.length > 0) {
      message.success('删除成功');
      getAnswerData();
    } else {
      message.error('删除失败');
    }
  };

  //打开弹窗
  const openDig = async (sign: string) => {
    dialogData.dialogSign = sign;
    if (isOnPlay.value) {
      message.warning('请先结束语音试听');
      return;
    }
    switch (dialogData.dialogSign) {
      case '答疑':
        if (!selectLens.value.id) {
          message.warning('分镜不能为空');
          return;
        }
        // 检查当前分镜是否有答疑内容
        const qaRes: any = await getAnswer({ image_ids: selectLens.value.id });
        if (!qaRes?.list || qaRes.list.length === 0) {
          // 如果没有答疑内容，直接生成
          aiAnswerData();
        } else {
          // 如果有答疑内容，显示确认弹窗
          dialogData.dialogTit = '确定使用AI生成答疑对？';
          dialogData.dialogText = '使用此功能会生成新的答疑对，现有的答疑对将被替换';
          dialogData.dialogVisble = true;
        }
        break;
      case '文案':
        if (!selectLens.value.id) {
          message.warning('分镜不能为空');
          return;
        }
        // 检查当前分镜是否有文案内容
        if (!selectLens.value.text || selectLens.value.text.trim() === '') {
          // 如果没有文案内容，直接生成
          aiCopywritingData();
        } else {
          // 如果有文案内容，只显示确认弹窗，不调用生成函数
          dialogData.dialogTit = '确定使用AI生成文案？';
          dialogData.dialogText = '使用此功能会生成新的文案内容，现有的内容将被替换';
          dialogData.dialogVisble = true;
        }
        break;
      case '合成':
        // 1. 检查是否有分镜
        if (!projectData.value?.images || projectData.value.images.length === 0) {
          message.warning('请先上传课件');
          return;
        }

        // 2. 检查是否有内容为空的分镜
        const emptyImageIndex = projectData.value.images.findIndex((item: any) => !item.image_url);
        if (emptyImageIndex !== -1) {
          message.warning(`请为分镜${emptyImageIndex + 1}选择课件或素材`);
          return;
        }

        // 3. 检查是否有文案为空的分镜
        const emptyTextIndex = projectData.value.images.findIndex((item: any) => !item.text || item.text.trim() === '');
        if (emptyTextIndex !== -1) {
          message.warning(`请先输入分镜${emptyTextIndex + 1}的文案`);
          return;
        }

        // 4. 检查答疑是否完整
        for (let i = 0; i < projectData.value.images.length; i++) {
          const image = projectData.value.images[i];
          // 获取当前分镜的答疑数据
          const qaRes: any = await getAnswer({ image_ids: image.id });

          // 检查答疑列表是否为空数组
          if (!qaRes?.list || qaRes.list.length === 0) {
            message.warning(`请完善分镜${i + 1}的答疑`);
            return;
          }

          // 如果有答疑数据，检查每个答疑对是否完整
          const incompleteQA = qaRes.list.some((qa: any) => {
            // 检查问题是否为空
            if (!qa.question?.trim()) return true;

            // 检查是否选择了正确答案
            if (!qa.true_answer) return true;

            // 检查是否有答案对象
            if (!qa.answer) return true;

            // 检查是否有完整的ABCD四个选项
            if (Object.keys(qa.answer).length < 4) return true;

            // 检查每个选项是否都有内容
            if (!qa.answer.A?.trim() || !qa.answer.B?.trim() || !qa.answer.C?.trim() || !qa.answer.D?.trim())
              return true;

            return false;
          });

          if (incompleteQA) {
            message.warning(`请完善分镜${i + 1}的答疑`);
            return;
          }
        }

        // 5. 所有检查通过，显示合成确认弹窗
        dialogData.dialogTit = '确定合成课程？';
        dialogData.dialogText = '';
        dialogData.tempName = projectData.value.name;
        dialogData.dialogVisble = true;
        break;
      default:
        break;
    }
  };
  //合成视频
  const synthesis = async () => {
    // 表单校验
    const form = unref(formRef1);
    if (!form) return;
    const valid = await form.validate();
    if (!valid) return;
    if (projectData.value.images.length == 0) {
      message.warning('分镜不能为空');
      return;
    }

    // 如果名称被修改了，先更新名称
    if (projectData.value.name !== dialogData.tempName) {
      try {
        // 等待名称更新完成
        await updateProjectName(projectData.value.name);
      } catch (error) {
        message.error('课堂名称更新失败');
        return;
      }
    }

    // 名称更新成功后，执行合成操作
    let param = {
      data: projectData.value.images.map((item: any) => ({
        image_id: item.id,
        speaker: item.speaker,
        text: item.text,
        type: item.type === '2d' ? 0 : 1,
      })),
      proj_id: projectData.value.id,
    };
    let res: any = await synthesisVideo(param);
    if (res.length > 0) {
      message.success('课程正在合成中');
      dialogData.dialogVisble = false;
      router.back();
    } else {
      message.error('合成失败');
    }
  };
  //对话框确定
  const handleOk = () => {
    switch (dialogData.dialogSign) {
      case '答疑':
        dialogData.dialogVisble = false; // 立即关闭弹窗
        aiAnswerData();
        break;
      case '文案':
        dialogData.dialogVisble = false;
        aiCopywritingData(); // 在用户确认后才调用生成函数
        break;
      case '合成':
        synthesis();
        break;
      default:
        break;
    }
  };
  //AI生成文案
  const aiCopywritingData = async () => {
    // 保存当前选中的分镜ID和索引
    const currentLensId = selectLens.value.id;
    const currentIndex = lensIndex.value;

    copywritingLoading.value = true;
    try {
      let res: any = await aiCopywriting({ image_ids: [currentLensId] });

      if (res.length > 0) {
        // 直接更新当前分镜的文案
        selectLens.value.text = res[0]?.ppt2text;

        // 更新项目数据
        let newArr = projectData.value.images.map((n: any) => {
          if (n.id === currentLensId) {
            return {
              ...n,
              image_id: res[0]?.image_id,
              text: res[0]?.ppt2text,
            };
          }
          return n;
        });

        let updateRes: any = await updateLens({
          id: projectId.value,
          images: newArr,
          name: projectData.value.name,
        });

        if (updateRes) {
          // 更新本地数据
          projectData.value.images = updateRes.images;
          // 保持当前选中的分镜
          chooseLens(projectData.value.images[currentIndex], currentIndex);
        }
      }
    } catch (error) {
      copywritingLoading.value = false;
      console.error('无法识别当前分镜内容，AI文案生成失败', error);
      // message.error('无法识别当前分镜内容，AI文案生成失败');
    } finally {
      copywritingLoading.value = false;
    }
  };
  //AI生成问答
  const aiAnswerData = async () => {
    copywritingLoading.value = true;
    try {
      let res: any = await aiAnswer({
        image_ids: [selectLens.value.id],
        model_name: HumanType[selectLens.value.type],
        speaker: selectLens.value.speaker || 'xingxing', // 使用当前选中的音色
      });
      copywritingLoading.value = false;
      if (res == 'OK') {
        getAnswerData();
      }
    } catch (error) {
      copywritingLoading.value = false;
    } finally {
      copywritingLoading.value = false;
    }
  };
  //点击预览按钮
  const goPreview = () => {
    // 如果当前正在播放试听音频，先停止播放
    if (isOnPlay.value && currentAudio.value) {
      currentAudio.value.pause();
      currentAudio.value = null;
      isOnPlay.value = false;
    }

    // 1. 检查是否有分镜
    if (!projectData.value?.images || projectData.value.images.length === 0) {
      message.warning('请先上传课件');
      return;
    }

    // 2. 检查是否有内容为空的分镜
    const emptyImageIndex = projectData.value.images.findIndex((item: any) => !item.image_url);
    if (emptyImageIndex !== -1) {
      message.warning(`请为分镜${emptyImageIndex + 1}选择课件或素材`);
      return;
    }

    // 3. 检查是否有文案为空的分镜
    const emptyTextIndex = projectData.value.images.findIndex((item: any) => !item.text || item.text.trim() === '');
    if (emptyTextIndex !== -1) {
      message.warning(`请先输入分镜${emptyTextIndex + 1}的文案`);
      return;
    }

    // 4. 所有检查通过，打开预览弹窗
    previewVisible.value = true;
    currentPreviewIndex.value = 0; // 默认显示第一个分镜
    currentAudioIndex.value = -1; // 重置音频播放状态
    previewPlaying.value = false;
  };

  // 暂停当前正在播放的音频
  const pauseCurrentAudio = () => {
    if (currentAudio.value) {
      currentAudio.value.pause();
      // currentAudio.value = null;
    }
    previewPlaying.value = false;
    // currentAudioIndex.value = -1;
  };

  // 添加播放预览语音的方法
  const playPreviewAudio = async (index: number) => {
    // 如果点击的是当前正在播放的分镜，则暂停播放
    if (currentAudioIndex.value === index && previewPlaying.value) {
      pauseCurrentAudio();
      return;
    }

    // 如果有 currentAudio 并且 currentAudioIndex.value === index，说明是继续播放
    if (currentAudio.value && currentAudioIndex.value === index && currentAudio.value.paused) {
      previewPlaying.value = true;
      await currentAudio.value.play();
      return;
    }

    // 如果有其他分镜在播放，先暂停它
    if (previewPlaying.value) {
      pauseCurrentAudio();
      // 等待一小段时间确保音频完全停止
      await new Promise((resolve) => setTimeout(resolve, 100));
    }

    // 开始从指定index播放
    previewPlaying.value = true;

    // 从指定index开始，顺序播放所有分镜
    for (let i = index; i < projectData.value.images.length; i++) {
      // 如果播放被手动停止，退出循环
      if (!previewPlaying.value) {
        break;
      }

      const currentSlide = projectData.value.images[i];
      if (!currentSlide?.text) {
        continue; // 跳过没有文案的分镜
      }

      try {
        loading.value = true;
        const res = await tts({
          input_text: currentSlide.text,
          speaker: selectLens.value.speaker || 'xingxing', // 使用当前选中的音色
        });
        loading.value = false;

        if (res) {
          currentAudioIndex.value = i;
          currentPreviewIndex.value = i; // 更新当前显示的分镜
          const audio = `data:audio/mp3;base64,${res}`;

          // 创建新的音频实例
          if (currentAudio.value) {
            currentAudio.value.pause();
            currentAudio.value = null;
          }
          currentAudio.value = new Audio(audio);

          // 播放音频并等待完成
          try {
            await currentAudio.value.play();
            await new Promise((resolve) => {
              if (currentAudio.value) {
                currentAudio.value.onended = resolve;
              }
            });
          } catch (error) {
            console.error('音频播放失败:', error);
            break;
          }
        } else {
          message.error('获取音频失败');
          break;
        }
      } catch (error) {
        loading.value = false;
        message.error('播放失败');
        break;
      }
    }

    // 播放完成或中断后的清理
    pauseCurrentAudio();
  };

  const handlePreviewChange = async (index: number) => {
    // 更新当前显示的分镜
    currentPreviewIndex.value = index;

    // 如果点击的是当前正在播放的分镜，则暂停播放
    if (currentAudioIndex.value === index && previewPlaying.value) {
      pauseCurrentAudio();
      return;
    }

    // 如果当前有音频在播放，先暂停它
    if (previewPlaying.value) {
      pauseCurrentAudio();
      // 短暂延迟确保音频完全停止
      await new Promise((resolve) => setTimeout(resolve, 100));
      // 从新选择的分镜开始播放
      await playPreviewAudio(index);
    } else {
      // 如果当前没有音频在播放，直接开始播放
      await playPreviewAudio(index);
    }
  };

  // 取消对话框
  const handleCancel = () => {
    if (dialogData.dialogSign === '合成') {
      projectData.value.name = dialogData.tempName; // 恢复原来的课堂名称
    }
    dialogData.dialogVisble = false;
  };

  //打开弹窗
  const opneDialog = () => {
    humanVisble.value = true;
    selectHumanOld.value = selectHuman.value;
    humanList.value.forEach((item: any) => {
      if (item.id == selectHuman.value.id) {
        // 只有当数字人没有被禁用时才设置为选中状态
        if (!item.disabled) {
          item.active = true;
        } else {
          item.active = false;
        }
      } else {
        item.active = false;
      }
    });
  };
  //关闭数字人弹窗
  const cancelHuman = () => {
    //关闭弹窗时恢复之前的数据
    selectHuman.value = selectHumanOld.value;
  };
  //选择数字人
  const choosehuman = (rowItem: any, index: number) => {
    // 如果数字人被禁用，不允许选择
    if (rowItem.disabled) {
      return;
    }
    selectHuman.value = rowItem;
    humanList.value.forEach((item: any, i: number) => {
      item.active = index === i;
    });
  };
  //获取上传课件后的返回值
  const getUploadData = async (data: any) => {
    if (!data || !data.id) {
      message.error('上传失败，请重试');
      return;
    }

    fileUrl.value = data.file_url;

    try {
      if (!projectId.value) {
        // 如果还没有projectId，先创建项目
        const newProjectId = await addProjectData();
        if (!newProjectId) {
          message.error('创建项目失败');
          return;
        }
        // 重新获取projectId
        projectId.value = Number(sessionStorage.getItem('projectId'));
      }

      // 开始解析PPT
      await analysisPPTData(data.id);
    } catch (error) {
      console.error('处理上传文件失败:', error);
      message.error('处理上传文件失败，请重试');
    }
  };
  //获取上传素材后的返回值
  const getUploadData1 = (data: any) => {
    if (data.length > 0) {
      getMaterialList();
    }
  };
  //获取素材列表
  const getMaterialList = async () => {
    loading.value = true;
    let res: any = await getMaterial({ user_id: userInfo?.userId });
    loading.value = false;
    if (res.list) {
      materialList.value = res.list;
    }
  };
  //解析ppt
  const analysisPPTData = async (id: number) => {
    if (!projectId.value) {
      message.error('项目ID不存在');
      return;
    }

    loading.value = true;
    try {
      let res: any = await analysisPPT({ id: id });
      // console.log('PPT解析结果:', res); // 添加日志

      if (!res) {
        message.error('文档解析失败');
        loading.value = false;
        return;
      }

      // 检查解析状态
      if (res.parse_status === 0) {
        // 如果还在解析中，继续轮询
        setTimeout(() => {
          analysisPPTData(id);
        }, 2000); // 增加轮询间隔到2秒
        return;
      }

      // 检查解析信息
      if (!res.parse_info || !Array.isArray(res.parse_info)) {
        message.error('文档解析结果格式错误');
        loading.value = false;
        return;
      }

      // 更新项目数据
      await updateProjectData(projectId.value, res.parse_info);
      message.success('文档解析完成');
    } catch (error) {
      console.error('文档解析出错:', error);
      message.error('文档解析失败，请重试');
    } finally {
      loading.value = false;
    }
  };

  //删除素材
  const delMater = async (id: number) => {
    let res: any = await delMaterial({ ids: [id] });
    if (res.length > 0) {
      getMaterialList();
    } else {
      message.error('删除失败');
    }
  };
  //试听
  const listening = async () => {
    if (!selectLens.value.text) {
      return; // 如果文案为空，直接返回，不执行任何操作
    }

    // 如果当前正在播放，则停止播放
    if (isOnPlay.value && currentAudio.value) {
      currentAudio.value.pause();
      currentAudio.value = null;
      isOnPlay.value = false;
      return;
    }

    // 开始新的播放
    try {
      loading.value = true;
      let res: any = await tts({
        input_text: selectLens.value.text,
        speaker: selectLens.value.speaker || 'xingxing', // 使用当前选中的音色
      });
      loading.value = false;

      if (res) {
        const audio = `data:audio/mp3;base64,${res}`;
        isOnPlay.value = true;

        // 创建新的音频实例
        if (currentAudio.value) {
          currentAudio.value.pause();
          currentAudio.value = null;
        }
        currentAudio.value = new Audio(audio);

        // 播放音频
        await currentAudio.value.play();

        // 监听播放结束事件
        currentAudio.value.onended = () => {
          isOnPlay.value = false;
          currentAudio.value = null;
        };
      } else {
        // 这种情况是请求成功但返回数据为空
        loading.value = false;
        isOnPlay.value = false;
        message.error('获取音频失败');
      }
    } catch (error) {
      loading.value = false;
      isOnPlay.value = false;
      message.error('播放失败');
    }
  };

  //添加项目
  const addProjectData = async () => {
    let res: any = await addProject({ name: projectData.value.name, user_id: userInfo?.userId });
    if (res) {
      sessionStorage.setItem('projectId', res.id);
      projectId.value = res.id; // 直接设置projectId
      return res.id;
    }
    return null;
  };
  //修改项目
  const updateProjectData = async (id: number, images_info: any) => {
    if (!id || !images_info || !Array.isArray(images_info)) {
      message.error('更新项目数据失败：参数错误');
      return;
    }
    // 将新的 images_info 追加到 file_info 数组中，而不是覆盖
    file_info.value = [...file_info.value, ...images_info];

    try {
      let res: any = await updateProject({ id, images_info, file_url: fileUrl.value });
      if (!res || !res.images) {
        message.error('更新项目数据失败：返回数据格式错误');
        return;
      }

      // 更新项目数据
      const imagesWithOriginalName = res.images.map((img: any) => {
        // 先查找老数据
        const old = projectData.value.images.find((oldImg: any) => oldImg.image_url === img.image_url);
        if (old && old.original_name) {
          // 老图片，保留原有 original_name
          return { ...img, original_name: old.original_name };
        }
        // 新图片，查找 images_info
        const info = file_info.value.find((info: any) => info.image_url === img.image_url);
        return { ...img, original_name: info ? info.original_name : undefined };
      });
      projectData.value.images = imagesWithOriginalName;
      coursewareList.value = JSON.parse(JSON.stringify(imagesWithOriginalName)) || [];

      // 更新当前选中的分镜
      if (res.images.length > 0) {
        selectLens.value = res.images[0] || {};
        selectLens.value.speaker = selectLens.value.speaker || 'xingxing';
        chooseLens(selectLens.value, 0);
      }

      // 更新分镜数据
      await updateLensData();
    } catch (error) {
      console.error('更新项目数据失败:', error);
      message.error('更新项目数据失败，请重试');
    }
  };

  // 修改项目名称
  const updateProjectName = async (name: string) => {
    if (!projectId.value) {
      message.warning('项目ID不存在');
      return Promise.reject('项目ID不存在');
    }
    // 当输入框失去焦点时更新
    let newArr = projectData.value.images.map((n: any) => {
      return {
        duration: n.duration || '5000',
        image_id: n.id,
        image_url: n.image_url,
        speaker: n.speaker || 'xingxing',
        text: n.text,
        type: n.type || '2d',
      };
    });
    let res: any = await updateLens({
      id: projectId.value,
      images: newArr,
      name: name,
    });
    if (res) {
      isEdit.value = false;
      return Promise.resolve(res);
    } else {
      // 如果失败，回滚到原来的名称
      getClassDetail(projectId.value);
      return Promise.reject('名称修改失败');
    }
  };

  // 处理预览弹窗关闭
  const handlePreviewClose = () => {
    pauseCurrentAudio(); // 停止当前播放的音频
  };

  // 处理名称失焦事件
  const handleNameBlur = () => {
    if (!projectData.value.name || projectData.value.name.trim() === '') {
      // 恢复最近编辑的名称，如果没有保存的临时名称，则使用原始名称
      const originalName = dialogData.tempName || '课程' + dayjs().format('YYYYMMDD');
      projectData.value.name = originalName;
      // message.warning('课程名称不能为空，已恢复原始名称');
    }
    isEdit.value = !isEdit.value;
  };
</script>
<style>
  .preview-modal-wrap .ant-modal .ant-modal-content {
    padding: 20px 0 0 0 !important;
  }

  .preview-modal-wrap .ant-modal .ant-modal-header .ant-modal-title {
    margin-left: 20px !important;
  }

  .preview-modal-wrap .ant-modal {
    top: 30px !important;
  }
</style>
<style lang="less" scoped>
  .page {
    width: 100%;
    padding: 0;
    height: calc(100% - 45px);
    .top {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .center-title {
        position: absolute;
        left: 58%;
        transform: translateX(-50%);
        display: flex;
        align-items: center;
        // 可选：防止内容太宽溢出
        max-width: 60%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
    .content {
      display: flex;
      width: 100%;
      margin-top: 10px;
      height: 100%;
      .left {
        width: 14%;
        border-top: 1px solid #f0f1f2;
        padding: 0 10px;
        .coursewareBox {
          margin-top: 10px;
          height: 500px;
          overflow-y: auto;
          // border: 1px solid;
          .courseware {
            width: 100%;
            height: 113px;
            border: 1px solid #f0f1f2;
            border-radius: 5px;
            margin-bottom: 12px;
            background-size: contain;
            background-position: center;
            background-repeat: no-repeat;
            cursor: pointer;
            position: relative;
            .delBox {
              position: absolute;
              top: 8px;
              right: 8px;
              background: #17181a;
              border-radius: 4px;
              opacity: 0.65;
              display: none;
              height: 20px;
              width: 20px;
              text-align: center;
              line-height: 16px;
              .del {
                width: 14px;
                height: 14px;
              }
            }
          }
          .courseware:hover .delBox {
            display: block;
          }

          .original-name {
            display: flex;
            height: 18px;
            font-family:
              PingFangSC,
              PingFang SC;
            font-weight: 400;
            font-size: 12px;
            color: #18191a;
            line-height: 18px;
            text-align: left;
            font-style: normal;
            margin: 5px 2px;

            .file-icon {
              display: inline-block;
              width: 3px;
              height: 16px;
              margin-right: 6px;
              border-radius: 4px;
              background: #1777ff;
              margin-top: 2px;
            }
          }
        }
      }
      .right {
        width: 86%;
        background: #f7f8fa;
        .offData {
          display: flex;
          height: 100%;
          width: 100%;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          font-weight: 500;
          font-size: 14px;
          color: #969799;
          > img {
            width: 88px;
            height: 88px;
            margin-bottom: 23px;
          }
        }
        .noData {
          width: 100%;
          background-color: #f5f5f5;
          padding-left: 16px;
          .top {
            width: 100%;
            height: 72px;
            display: flex;
            justify-content: center;
            position: relative;
            align-items: center;
            position: relative;

            // border: 1px solid;
            .sound {
              display: flex;
              position: absolute;
              top: 22px;
              right: 20px;
              .digitalBtn {
                margin-right: 10px;
                display: flex;
                align-items: center;
                .digitalHuman {
                  width: 25px;
                  height: 25px;
                  border-radius: 50%;
                  margin-left: 15px;
                }
              }
            }
          }
          .top::after {
            content: '';
            position: absolute;
            top: 0;
            right: -12px;
            width: 12px;
            height: 72px;
            background-color: #f5f5f5;
          }
          .centerBox {
            width: 100%;
            display: flex;
            background-color: #fff;
            border-radius: 8px;
            .center {
              width: 88%;
              // border: 1px solid;
              padding: 40px 16px;
              display: flex;
              height: 500px;
              .imageBox {
                width: 82%;
                height: 100%;
                // border: 1px solid;
                border-radius: 8px 0 0 8px;
                background-size: contain;
                background-position: center;
                background-repeat: no-repeat;
              }
              .videoBox {
                width: 18%;
                height: 100%;
                padding-top: 100px;
                .human {
                  height: 100%;
                  width: 100%;
                }
              }
            }
            .centerRight {
              padding-left: 10px;
              width: 18%;
              border-left: 1px solid #f0f1f2;

              .rightTop {
                display: flex;
                justify-content: space-between;
                font-size: 14px;
                margin-bottom: 10px;
                > span:nth-child(1) {
                  color: #18191a;
                  font-weight: 600;
                }
                .AIBtn {
                  display: flex;
                  align-items: center;
                  cursor: pointer;
                  border: none;
                  background: #fff;
                  > img {
                    width: 20px;
                    height: 20px;
                    margin-right: 4px;
                  }
                }
                .AIBtn:hover {
                  color: #1777ff;
                }
              }
              .rightContent {
                height: 410px;
                overflow-y: auto;
                .mytextarea {
                  position: relative;
                  .num {
                    position: absolute;
                    bottom: 5px;
                    right: 10px;
                    color: #969799;
                  }
                }
                .rightBottom {
                  display: flex;
                  justify-content: end;
                  cursor: pointer;
                  align-items: center;
                  margin-top: 12px;
                  color: #18191a;
                  transition: all 0.3s;

                  > img {
                    width: 20px;
                    height: 20px;
                    margin-right: 4px;
                  }

                  &:hover:not(.disabled) {
                    color: #1777ff;
                  }

                  &.disabled {
                    cursor: not-allowed;
                    opacity: 0.5;
                  }

                  &.playing {
                    color: #1777ff;
                  }
                }
              }
            }
          }
          .bottom {
            width: 100%;
            padding: 24px 0px 0px 16px;
            margin-top: 16px;
            border-radius: 8px 8px 0px 0px;
            background-color: #fff;
            position: relative;
            .lensList {
              width: 100%;
              display: flex;
              flex-direction: row;
              white-space: nowrap;
              overflow-x: auto;
              gap: 10px;
              .lensItem {
                flex: 0 0 155px;
                height: 80px;
                border-radius: 6px;
                border: 1px solid #edeff2;
                background-size: 100% 100%;
                background-repeat: no-repeat;
                cursor: pointer;
                position: relative;
                padding: 2px;
                .serialNum {
                  padding: 0 9px;
                  height: 18px;
                  font-weight: 600;
                  font-size: 12px;
                  color: #ffffff;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  position: absolute;
                  background: #d9e9ff;
                  border-radius: 6px 0px 6px 0px;
                  top: 0;
                  left: 0;
                }
                .delBox {
                  position: absolute;
                  top: 8px;
                  right: 8px;
                  background: #17181a;
                  border-radius: 4px;
                  opacity: 0.65;
                  display: none;
                  height: 20px;
                  width: 20px;
                  text-align: center;
                  line-height: 16px;
                  .del {
                    width: 14px;
                    height: 14px;
                  }
                }
              }
              .lensItem:hover .delBox {
                display: block;
              }
              .lensItem1 {
                flex: 0 0 80px;
                height: 80px;
                border: 1px solid #edeff2;
                border-radius: 6px;
                cursor: pointer;
                display: flex;
                background: #f2f8ff;
                justify-content: center;
                align-items: center;
              }
              .lensItem1:hover {
                background: #e6f0ff;
              }
            }
          }
          .bottom::after {
            content: '';
            position: absolute;
            top: -14px;
            right: -12px;
            width: 12px;
            height: 16px;
            background-color: #f5f5f5;
          }
        }
      }
    }
  }
  .humanPage {
    width: 100%;
    height: 500px;
    overflow-y: auto;
    display: flex;
    flex-wrap: wrap;
    .humanItem {
      width: 142px;
      height: 188px;
      margin-right: 19px;
      background: #f0f1f2;
      border-radius: 4px;
      margin-bottom: 18px;
      background-size: 100% 100%;
      cursor: pointer;
      box-sizing: border-box;

      &.disabled {
        cursor: not-allowed;
        opacity: 0.5;
        position: relative;

        &::after {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: rgba(0, 0, 0, 0.3);
          border-radius: 4px;
        }
      }
    }
    .humanItem:nth-child(6n) {
      margin-right: 0;
    }
    .btnBox {
      display: flex;
      justify-content: flex-end;
    }
  }
  .preview-modal {
    :deep(.preview-modal) {
      .ant-modal {
        top: 30px !important;
        padding-bottom: 24px;
      }
    }

    .previewBox {
      background: #f5f6f7;

      .preview {
        width: 100%;
        height: 505px;
        display: flex;
        border-radius: 6px;
        overflow: hidden;
        padding: 10px 62px 0 62px;

        .previewLeft {
          width: 80%;
          height: 100%;
          background-repeat: no-repeat;
        }

        .previewRight {
          width: 20%;
          height: 100%;
          border: 1px solid #f0f1f2;
          border-left: none;
          display: flex;
          align-items: center;
          justify-content: center;
          background: #fff;

          .human {
            width: 100%;
            height: auto;
          }
        }
      }

      .palyBtn {
        width: 100%;
        display: flex;
        justify-content: center;
        margin: 20px 0;
        padding: 0 62px;

        img {
          width: 48px;
          height: 48px;
          cursor: pointer;
          transition: all 0.3s;

          &:hover {
            transform: scale(1.1);
          }
        }
      }

      .previewBottom {
        width: 100%;
        display: flex;
        overflow-x: auto;
        padding: 10px 24px;
        background: #ffffff;

        .previewItem {
          flex: 0 0 138px;
          height: 78px;
          margin-right: 12px;
          border-radius: 6px;
          background-repeat: no-repeat;
          cursor: pointer;
          transition: all 0.3s;
          position: relative;

          .hover-play-icon {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: center;
            border-radius: 6px;

            img {
              width: 24px;
              height: 24px;
              transition: transform 0.2s;

              &:hover {
                transform: scale(1.1);
              }
            }
          }

          &:hover {
            .hover-play-icon {
              display: flex;
            }
          }

          &.active {
            border: 2px solid #1777ff;
          }
        }
      }
    }
  }
  .mask {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    background: rgba(0, 0, 0, 0.25);
    position: absolute;
    top: 0;
    left: 0;
  }
  .copywriting-mask {
    width: calc(100% + 12px);
    height: 100%;
    z-index: 10;
    display: flex;
    justify-content: center;
    align-items: center;
    background: rgba(0, 0, 0, 0.25);
    position: absolute;
    top: 0;
    left: 0;
  }
  .humanItem.disabled {
    cursor: not-allowed;
    opacity: 0.5;
    position: relative;
  }
</style>
