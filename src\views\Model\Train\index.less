.radio-button-wrappe {
  cursor: pointer;
  border: 1px solid #d9d9d9;

  &:hover {
    color: #1677ff;
  }

  &:first-child {
    border-start-start-radius: 6px;
    border-end-start-radius: 6px;
  }

  &:last-child {
    border-start-end-radius: 6px;
    border-end-end-radius: 6px;
  }
}

.actived {
  color: #1677ff;
  border: 1px solid #1677ff;
}

.types {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  padding: 10px;
  cursor: pointer;
  border-radius: 2px;

  .types-items {
    position: relative;
    display: flex;
    flex: 1 1 32%;
    max-width: 32%;
    padding: 10px;
    background-color: #fff;
    border: 1px solid #ccc;
  }

  .checkbox {
    position: absolute;
    top: 2px;
    right: 4px;
    width: 16px;
    height: 16px;
  }

  .active {
    border: 1px solid #1677ff;
  }
}
