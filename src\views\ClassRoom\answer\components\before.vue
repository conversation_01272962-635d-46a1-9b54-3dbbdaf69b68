<script setup lang="ts">
  import { Modal } from 'ant-design-vue';
  interface IProps {
    loading: boolean;
  }

  const emits = defineEmits<{
    (event: 'begin'): void;
  }>();

  const props = defineProps<IProps>();

  function handleClick() {
    Modal.confirm({
      title: '确认开始作答？',
      content: '开始作答后，中途离开或刷新页面，答题结果将失效',
      okText: '开始作答',
      cancelText: '考虑一下',
      onOk: () => {
        emits('begin');
      },
    });
  }
</script>

<template>
  <div class="w-full h-full before-answer">
    <h2>课堂测试</h2>
    <p>
      欢迎来到答题模式，每个问题有四个选项，请选择你认为正确的答案，然后点击下一题，下方会展示你已完成作答的题目，回答有误的列为红色，回答正确的列为绿色。现在你可以点击开始按钮作答。
    </p>
    <a-button type="primary" :disabled="loading" class="mt-7.5 h-10" @click="handleClick">开始答题</a-button>
  </div>
</template>

<style lang="less" scoped>
  .before-answer {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 0 150px;
    h2 {
      font-size: 24px;
      margin-bottom: 16px;
      font-weight: 600;
      color: #18191a;
    }
    p {
      font-weight: 400;
      font-size: 16px;
      color: #636466;
      line-height: 30px;
      margin: 0;
    }
  }
</style>
