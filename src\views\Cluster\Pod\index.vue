<script setup lang="ts">
  import { ref, reactive, onMounted, onUnmounted, watch } from 'vue';
  import type { ColumnType, TablePaginationConfig } from 'ant-design-vue/es/table';
  import { RedoOutlined } from '@ant-design/icons-vue';
  import { fetchPodsList, fetchPodsYamlConfig } from '@/api/k8s';
  import type { IPodListResponse, IPodsList } from '@/interface/kubernetes';
  import { copyText, formatTime, convertIsoTimeToLocalTime } from '@/utils/common';
  import { VAceEditor } from 'vue3-ace-editor';
  import 'ace-builds/src-noconflict/theme-chrome';
  import 'ace-builds/src-noconflict/theme-monokai.js';
  import 'ace-builds/src-noconflict/mode-python.js';
  import 'ace-builds/src-noconflict/mode-sh.js';
  import type { IAceOptions, IPage } from '@/interface';
  import type { IFormItem } from '@/components/CustomForm/index.vue';
  import { CustomForm } from '@/components';
  import yaml from 'js-yaml';
  import { useRouter, useRoute } from 'vue-router';
  import { TABLE_PAGINATION } from '@/json/common';
  import { POD_OPTIONS, POD_STATUS_MAP } from '..';

  const router = useRouter();
  const route = useRoute();
  const pagination = reactive({ ...TABLE_PAGINATION });
  const pageParame: IPage = reactive({ page: 1, limit: 10 });
  const tableHeight = ref(0);
  interface IPodSearchState {
    name?: string;
    status?: string;
  }
  const DEFAULT_SEARCHSTATE: IPodSearchState = {
    name: '',
    status: '',
  };
  const searchState: IPodSearchState = reactive({ ...DEFAULT_SEARCHSTATE });
  const loading = ref(false);
  const pollingInterval = ref();
  const countInterval = ref();
  const count = ref(10);
  const dataSource = reactive<IPodListResponse[]>([]);
  const YAMLState: { visible: boolean; value: string; title: string; spinning: boolean } = reactive({
    visible: false,
    title: 'Deployment',
    spinning: false,
    value: '',
  });
  const columns: ColumnType[] = [
    { title: '容器组名称', dataIndex: 'name', width: 400 },
    { title: '运行模式', dataIndex: 'mode' },
    { title: '就绪', dataIndex: 'ready', width: 80 },
    { title: '所在节点', dataIndex: 'node' },
    { title: '容器状态', dataIndex: 'phase', width: 120 },
    { title: 'IP 地址', dataIndex: 'pod_ip' },
    { title: '创建时间', dataIndex: 'creation_time' },
    { title: '已重启', dataIndex: 'restart_count', width: 80 },
    { title: '已创建', dataIndex: 'runtime', width: 120 },
    { title: '操作', dataIndex: 'operation', width: 80 },
  ];
  const formConfig: IFormItem[] = [
    {
      field: 'name',
      type: 'input',
      label: '容器组名称',
      placeholder: '请输入',
    },
    {
      field: 'status',
      type: 'select',
      label: '状态',
      placeholder: '请选择',
      options: POD_OPTIONS,
    },
  ];

  const aceOptions: IAceOptions = {
    enableBasicAutocompletion: true,
    enableSnippets: true,
    enableLiveAutocompletion: true,
    tabSize: 4,
    enableEmmet: true,
    fontSize: 15,
    showPrintMargin: false, // 去除编辑器里的竖线
    highlightActiveLine: true,
    useWorker: true,
    readOnly: false,
  };
  const fetchList = async () => {
    loading.value = true;
    const params: IPodsList = { ...pageParame };
    Object.keys(searchState).forEach((key) => {
      if (searchState[key as keyof IPodSearchState]) {
        params[key as keyof IPodSearchState] = searchState[key as keyof IPodSearchState];
      }
    });
    const data: { items: IPodListResponse[]; total: number } = await fetchPodsList(params);
    const { items, total } = data;
    Object.assign(pagination, { current: pageParame.page, total: total });
    dataSource.length = 0;
    dataSource.push(...items);
    loading.value = false;
  };
  const fetchYamlConfigReq = async (record: IPodListResponse) => {
    YAMLState.visible = true;
    YAMLState.spinning = true;
    const data = await fetchPodsYamlConfig(record.name);
    YAMLState.value = yaml.dump(data);
    YAMLState.spinning = false;
  };
  const jumpDetail = (record: IPodListResponse) => {
    router.push(`/cluster-manage/pod/${record.name}`);
  };

  // 开始轮询
  const startPolling = async () => {
    // 先立即获取一次数据
    await fetchList();
    // 设置定时轮询
    pollingInterval.value = setInterval(async () => {
      await fetchList();
    }, 11000);
    countInterval.value = setInterval(() => {
      count.value > 0 ? count.value-- : (count.value = 10);
    }, 1000);
  };

  // 停止轮询
  const stopPolling = () => {
    if (pollingInterval.value) {
      clearInterval(pollingInterval.value);
      pollingInterval.value = null;
    }
    if (countInterval.value) {
      clearInterval(countInterval.value);
      countInterval.value = null;
    }
  };

  // 刷新
  const handleRedo = () => {
    stopPolling();
    startPolling();
    // 重置倒计时
    count.value = 10;
  };

  const onFinish = (values: Record<string, string>) => {
    const searchConfig: { [key: string]: string } = {};
    for (const key in values) {
      searchConfig[key] = values[key];
    }
    Object.assign(searchState, { ...searchConfig });
    pageParame.page = 1;
    handleRedo();
  };

  const onRest = () => {
    Object.assign(searchState, { ...DEFAULT_SEARCHSTATE });
    handleRedo();
  };

  const toggleTable = (_pagination: TablePaginationConfig) => {
    const { current, pageSize } = _pagination;
    Object.assign(pagination, { current, pageSize });
    Object.assign(pageParame, { page: current, limit: pageSize });
    handleRedo();
  };

  const getTableHeight = () => {
    const tableItem = document.querySelector('.container');
    tableHeight.value = (tableItem?.clientHeight as number) - 46;
  };

  onMounted(() => {
    startPolling();
    getTableHeight();
  });

  onUnmounted(() => {
    stopPolling();
  });

  watch(
    () => route.path,
    (path) => {
      if (path !== '/cluster-manage/pod') {
        stopPolling();
      }
    },
    { deep: true },
  );
</script>

<template>
  <router-view v-if="route.params.name" />
  <a-spin v-else :spinning="loading">
    <CustomForm :form-items="formConfig" @on-finish="onFinish" @on-rest="onRest" style="margin-bottom: 0" />
    <div class="refresh">
      <a-button style="padding: 0">
        <div class="refresh-count">{{ count }}s</div>
        <div class="refresh-icon" @click="handleRedo">
          <RedoOutlined />
        </div>
      </a-button>
    </div>
    <a-table
      :data-source="dataSource"
      :columns="columns"
      :pagination="pagination"
      :scroll="{ y: tableHeight - 170 }"
      @change="toggleTable"
    >
      <template #bodyCell="{ column, text, record }">
        <div v-if="column.dataIndex === 'operation'">
          <a @click="fetchYamlConfigReq(record)">YAML</a>
        </div>
        <div v-else-if="['creation_time', 'start_time'].includes(column.dataIndex)">
          {{ convertIsoTimeToLocalTime(text) }}
        </div>
        <div v-else-if="column.dataIndex === 'name'">
          <a class="table-a-btn" @click="jumpDetail(record)">{{ text }}</a>
        </div>
        <div v-else-if="column.dataIndex === 'mode'">
          <a-tag>{{ record.name.startsWith('ai-platform') ? '中台托管' : '其他' }}</a-tag>
        </div>
        <div v-else-if="column.dataIndex === 'phase'">
          <a-tag :color="POD_STATUS_MAP.find((item) => item.value === text)?.color">{{
            POD_STATUS_MAP.find((item) => item.value === text)?.label
          }}</a-tag>
        </div>

        <div v-else-if="column.dataIndex === 'runtime'">
          {{ formatTime(Math.floor(text)) }}
        </div>
        <div v-else>{{ text }}</div>
      </template>
    </a-table>
  </a-spin>

  <a-modal
    v-model:open="YAMLState.visible"
    :title="YAMLState.title"
    width="60%"
    centered
    :footer="false"
    @cancel="
      YAMLState.visible = false;
      YAMLState.value = '';
    "
  >
    <div class="h-800px overflow-scroll">
      <a-spin :spinning="YAMLState.spinning">
        <div class="w-100% h-32px flex flex-justify-end">
          <a @click="copyText(YAMLState.value)"> 复制到剪切板 </a>
        </div>
        <v-ace-editor
          v-model:value="YAMLState.value"
          lang="sh"
          theme="monokai"
          :options="aceOptions"
          style="height: calc(100% - 32px); border: 1px solid #dbd3d3"
        />
      </a-spin>
    </div>
  </a-modal>
</template>

<style scoped lang="less">
  @import url('../index.less');
</style>
