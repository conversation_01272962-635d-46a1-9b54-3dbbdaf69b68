import { create, all } from 'mathjs';

const config = {};
const math = create(all, config);

const CalculatorPlugin = {
  interceptors: {
    before: {
      handlers: [],
      use(fn) {
        this.handlers.push(fn);
      },
    },
    after: {
      handlers: [],
      use(fn) {
        this.handlers.push(fn);
      },
    },
  },

  // 注册自定义函数
  registerFunction(name, fn) {
    math.import({ [name]: fn }, { override: true });
  },

  // 注册变量
  setVariable(name, value) {
    math.scope[name] = value;
  },

  // 计算表达式，支持拦截器
  calculate(expression) {
    let expr = expression;
    // 前置拦截器
    for (const fn of this.interceptors.before.handlers) {
      const res = fn(expr);
      if (res === false) return '被拦截';
      if (typeof res === 'string') expr = res;
    }
    let result;
    try {
      result = math.evaluate(expr);
    } catch (e) {
      result = '表达式错误: ' + e.message;
    }
    // 后置拦截器
    for (const fn of this.interceptors.after.handlers) {
      result = fn(result, expr);
    }
    return result;
  },
};

export default CalculatorPlugin;
