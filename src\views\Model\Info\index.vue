<script setup lang="ts">
  import { computed, onBeforeMount, reactive, ref, watch } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { QuestionCircleOutlined } from '@ant-design/icons-vue';
  import Train from '@/views/Model/Train/index.vue';
  import Deploy from '@/views/Model/Deploy/index.vue';
  import 'ace-builds/src-noconflict/theme-chrome';
  import 'ace-builds/src-noconflict/mode-python.js';
  import { editModel, getModelInfoV2, getModelManageInfoV2, publishModelV2 } from '@/api/model.js';
  import { message } from 'ant-design-vue';
  import { modelFunStatus } from '@/utils/enum';
  import { MdPreview, MdEditor } from 'md-editor-v3';
  import 'md-editor-v3/lib/style.css';
  import { QuestionCircleFilled } from '@ant-design/icons-vue';
  import DeployList from '@/views/Deploy/index.vue';
  import type { IModelFormState } from '@/interface/model';
  import { machineLearningList } from '../Manage/index';

  const route = useRoute();
  const router = useRouter();
  const spinning = ref(false);

  const DEFAULT_FORM_STATE: Partial<IModelFormState> = {
    tags: [],
    name: '',
    size: '',
    ops: [],
    seq: 1,
    category: undefined,
    source_path: undefined,
    source_name: undefined,
    template: undefined,
    exp_temp: 'text_chat',
    published_at: '',
    inference_engine: undefined,
    engine: [''],
    tf: undefined,
    description: '',
    detail: '',
  };
  interface IState {
    activeKey: number;
    model: Partial<IModelFormState>;
    detail: string;
    description: string;
    allDetail: string;
    editDetailVisible: boolean;
    modalLoading: boolean;
  }
  const state = reactive<IState>({
    activeKey: 1,
    model: JSON.parse(JSON.stringify(DEFAULT_FORM_STATE)),
    detail: '',
    description: '',
    allDetail: '',
    editDetailVisible: false,
    modalLoading: false,
  });

  const visible = ref(false);
  const deployVisible = ref(false);
  const handleClick = async (type: string) => {
    if (type === 'edit') {
      state.editDetailVisible = true;
      state.description = state.model.description || '';
      state.detail = state.model.detail || '';
    } else {
      await publishModelV2({ version_id: state.model.id! });
      message.success('发布成功');
      router.push('/model/manage');
    }
  };

  const handleOperation = (type: string) => {
    const path = route.path;
    if (type === 'experience') {
      const { id, category } = state.model;
      let path: string = '/text/detail';
      switch (category) {
        case 'gotocr':
        case 'ocr':
        case 'paddleocr':
          path = '/ocr/detail';
          break;
        default:
          break;
      }
      router.push({
        path,
        query: { modelId: id },
      });
    } else {
      router.push({
        path,
        query: { ...route.query, type },
      });
      visible.value = type === 'train';
      deployVisible.value = type === 'deploy';
    }
  };

  const getInfo = async () => {
    spinning.value = true;
    const { modelid } = route.query;
    if (modelid) {
      state.model =
        route.path === '/model/manage/detail'
          ? await getModelManageInfoV2(modelid as string)
          : await getModelInfoV2(modelid as string);
      const { detail } = state.model;
      state.allDetail = detail || '';
      spinning.value = false;
    }
  };

  onBeforeMount(async () => {
    await getInfo();
  });

  watch(
    () => route.path,
    async () => {
      if (route.query.type) {
        visible.value = route.query.type === 'train';
        deployVisible.value = route.query.type === 'deploy';
      }
    },
    { deep: true, immediate: true },
  );

  const editDetail = async () => {
    const data = await editModel({ id: state.model.id!, description: state.description, detail: state.detail });
    if (data === null) {
      state.modalLoading = false;
      message.success('模型介绍修改成功！');
      state.editDetailVisible = false;
      await getInfo();
    }
  };

  const isManage = computed(() => {
    return route.name === 'manageDetail';
  });

  const closeVisible = async () => {
    state.activeKey = 2;
    await getInfo();
    deployVisible.value = false;
  };
</script>

<template>
  <a-spin :spinning="spinning">
    <div class="info">
      <div>
        <div>
          <div>{{ state.model.name }}</div>
          <div v-if="state.model.tags && state.model.tags.length">
            <a-tag v-for="(n, i) in state.model.tags.filter((e) => e.name)" :key="i" :bordered="false" color="cyan">{{
              n.name
            }}</a-tag>
          </div>
        </div>
        <div v-if="isManage">
          <a-button size="small" @click="handleClick('edit')">编辑</a-button>
          <a-button v-if="['new', 'draft'].includes(state.model.status!)" size="small" @click="handleClick('publish')"
            >发布</a-button
          >
        </div>
        <div v-else>
          <a-button v-if="state.model.ops!.includes('deploy')" @click="handleOperation('deploy')">部署</a-button>
          <a-button v-if="state.model.ops!.includes('train')" @click="handleOperation('train')">{{
            machineLearningList.includes(state.model.category!) ? '训练' : '精调'
          }}</a-button>
          <a-button v-if="state.model.ops!.includes('experience')" @click="handleOperation('experience')"
            >体验</a-button
          >
        </div>
      </div>
      <a-tabs v-model:active-key="state.activeKey">
        <a-tab-pane :key="1" tab="模型介绍">
          <div class="overflow-scroll introduce">
            <a-empty v-if="!(state.model.detail || state.model.description)" style="margin-top: 15%" />
            <div v-else>
              <div class="m-b-44px">
                <div class="font-size-32px color-#3f4a54 font-bold m-b-25px">模型简介</div>
                <div class="font-size-16px color-#3f4a54 m-b-25px lh-30px">{{ state.model.description }}</div>
              </div>
              <MdPreview :model-value="state.allDetail" style="height: 100%"></MdPreview>
            </div>
          </div>
        </a-tab-pane>
        <a-tab-pane v-if="!isManage" :key="2" tab="部署实例">
          <DeployList :single-model="true" :model-id="state.model.id" :category="state.model.category"></DeployList>
        </a-tab-pane>
      </a-tabs>
    </div>
  </a-spin>

  <a-drawer v-model:open="visible" width="800" :mask-closable="false">
    <template #title>
      训练
      <a-tooltip>
        <template #title>使用默认数据或自己的业务数据进行模型训练，从而得到您的专属场景模型。</template>
        <QuestionCircleOutlined class="ml-5px" />
      </a-tooltip>
    </template>
    <Train :model="state.model" :close-visible="() => (visible = false)" />
  </a-drawer>
  <a-drawer v-model:open="deployVisible" width="800" :mask-closable="false">
    <template #title>
      部署
      <a-tooltip>
        <template #title>使用预置开源的模型并将其部署为在线服务，以进行实时的推理调用。</template>
        <QuestionCircleOutlined class="ml-5px" />
      </a-tooltip>
    </template>
    <Deploy :model="state.model" :close-visible="closeVisible" />
  </a-drawer>

  <!-- 模型详情编辑 -->
  <a-modal centered :visible="state.editDetailVisible" :width="'60%'" @cancel="state.editDetailVisible = false">
    <template #title>
      <div class="title">
        <exclamation-circle-outlined class="title-icon" />
        <span style="margin-right: 10px">编辑模型介绍</span>
        <a-tooltip>
          <template #title>
            # 简介 这是模型的简介 **** # 模型训练 这是模型的训练 **** # 模型部署 这是模型的部署 **** # 调用方式
            ```python from openai import OpenAI ``` ****
          </template>
          <question-circle-filled class="span-mg-left" />
        </a-tooltip>
      </div>
    </template>
    <template #footer>
      <slot name="footer">
        <a-button @click="state.editDetailVisible = false">
          <span>取消</span>
        </a-button>
        <a-button type="primary" @click="editDetail()">
          <span>确定</span>
        </a-button>
      </slot>
    </template>
    <div class="max-h-600px overflow-scroll">
      <a-form ref="modelRef" :model="state" class="defcourse" label-align="right" layout="vertical">
        <a-form-item label="模型简介" name="description">
          <MdEditor v-model="state.description" style="height: 300px" @on-upload-img="() => {}"></MdEditor>
        </a-form-item>
        <a-form-item label="模型详情" name="detail">
          <MdEditor v-model="state.detail" @on-upload-img="() => {}"></MdEditor>
        </a-form-item>
      </a-form>
    </div>
  </a-modal>
</template>

<style lang="less" scoped>
  .info {
    display: flex;
    flex-direction: column;
    height: 100%;

    > div:nth-child(1) {
      display: flex;
      align-items: center;
      justify-content: space-between;

      > div:nth-child(1) {
        > div:nth-child(1) {
          margin-bottom: 10px;
          font-size: 20px;
          font-weight: 500;
        }

        > div:nth-child(2) {
          margin-bottom: 10px;
          font-size: 16px;
        }
      }

      > div:nth-child(2) {
        display: flex;

        button {
          margin-right: 20px;
        }
      }
    }

    > div:nth-child(2) {
      display: flex;
      flex: 1;
      flex-direction: column;
      height: 0;
      margin-top: 20px;

      .introduce {
        height: 100%;

        > div {
          display: flex;
          flex-direction: column;
          min-height: 100px;

          > div:nth-child(2) {
            flex: 1;
            margin-bottom: 10px;
          }

          svg {
            overflow: visible;
          }
        }

        > div:nth-child(4) {
          .codemodel {
            margin-bottom: 20px;

            > div:nth-child(1) {
              display: flex;
              justify-content: space-between;
              margin: 10px 0;
            }
          }
        }
      }
    }

    .textbefo {
      position: relative;
      padding: 5px 0 5px 20px;
      // padding-left: 40px;
      margin: 10px 0 20px;
      font-size: 15px;
      font-weight: 600;
    }

    .textbefo::before {
      position: absolute;
      top: 10px;
      left: 0;
      display: inline-block;
      width: 5px;
      height: 14px;
      content: '';
      background: rgb(38 61 252 / 82%);
    }

    :deep(.ant-tabs-content) {
      height: 100%;
    }
  }
</style>
