<template>
  <svg class="icon" :class="iconClass" :style="iconStyle" aria-hidden="true">
    <use :href="iconHref" />
  </svg>
</template>

<script setup lang="ts">
  import { computed } from 'vue';

  interface IconProps {
    name: string; // 图标名称
    size?: number | string; // 图标大小
    color?: string; // 图标颜色
    className?: string; // 自定义类名
  }

  const props = withDefaults(defineProps<IconProps>(), {
    size: 16,
    color: 'currentColor',
    className: '',
  });

  // 计算图标的href
  const iconHref = computed(() => {
    return `#icon-${props.name}`;
  });

  // 计算图标的样式
  const iconStyle = computed(() => {
    const size = typeof props.size === 'number' ? `${props.size}px` : props.size;
    return {
      width: size,
      height: size,
      color: props.color,
    };
  });

  // 计算图标的类名
  const iconClass = computed(() => {
    return {
      'icon-component': true,
      [props.className]: !!props.className,
    };
  });

  // 可用的图标列表
  // 根据iconfont.js文件，可用的图标包括：
  // - xingzhuang (形状)
  // - beijing (背景)
  // - warning (警告)
  // - chenggong (成功)
  // - zhinengti (智能体)
  // - fabu (发布)
  // - k8s-jiqun (K8s集群)
  // - tubiaozhizuosvg (图表制作)
  // - moxingbiaoqian (模型标签)
  // - moxingjicheng (模型继承)
  // - jxgl (机器学习)
  // - jiankong (监控)
  // - rizhi (日志)
  // - moxingbushu (模型部署)
  // - moxingxunlian (模型训练)
  // - moxingjishi (模型集市)
</script>

<style scoped>
  .icon {
    display: inline-block;
    fill: currentColor;
    vertical-align: middle;
    overflow: hidden;
  }

  .icon-component {
    /* 基础样式 */
  }
</style>
