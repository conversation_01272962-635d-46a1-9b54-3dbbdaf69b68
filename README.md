## 一 安装 commitlint 和 husky 及其配置

后端项目没安装过 node 的话，需要先安装 [node,推荐版本 v18.x,与 Jenkins 机器 node 版本一致](https://nodejs.org/zh-cn/download)

`npm install -D husky  @commitlint/{cli,config-conventional}   `
`npm install -D husky  @commitlint/{cli,config-conventional}   `

## 二 创建配置文件

在项目根目录下创建 `commitlint.config.js`：

```js
module.exports = {
  extends: ['@commitlint/config-conventional'],
  rules: {
    // 例如允许中文或长度规则等
    'subject-empty': [2, 'never'],
    'header-max-length': [2, 'always', 100],
    'subject-case': [0], // 允许中文或大小写自由
    // 关键规则：subject 必须以 -数字 结尾
    'header-pattern': [2, 'always', '^(feat|fix|docs|style|refactor|perf|test|chore|revert): .+-[0-9]+$'],
    'type-enum': [2, 'always', ['feat', 'fix', 'docs', 'style', 'refactor', 'perf', 'test', 'chore', 'revert']],
  },
  plugins: [
    {
      rules: {
        'header-pattern': require('./header-pattern'),
      },
    },
  ],
};
```

创建 ` header-pattern.js`：

```js
module.exports = (parsed, when = 'always', value = '.*') => {
  const header = parsed.header || '';

  // 如果 value 是字符串，则转换成正则表达式
  const regex = typeof value === 'string' ? new RegExp(value) : value;

  const isMatch = regex.test(header);
  const pass = when === 'always' ? isMatch : !isMatch;

  return [pass, `header ${when === 'always' ? 'must match' : 'must not match'} pattern ${regex}`];
};
```

## 三 添加 commit-msg 钩子

```
# husky v7.x版本
npx husky install
npx husky add .husky/commit-msg 'npx commitlint --edit "$1"'
# husky v9.x版本
npx husky init
然后手动创建 .husky/commit-msg 文件，并添加以下内容
```

将内容全部替换为

```bash
#!/bin/bash

npx --no -- commitlint --edit "$1" >/dev/null || {

  arr=(
    "(1)feat: 新增用户登录功能 #议题Id;   "
    "(2)fix: 修复登录跳转错误 #议题Id;   "
    "(3)docs: 更新项目文档 #议题Id;  "
    "(4)chore: 升级npm依赖库 #议题Id; "
    "(5)perf: 优化页面加载速度 #议题Id; "
    "(6)refactor: 用户登录重构 #议题Id;"
    "(7)style: 用户登录样式调整 #议题Id; "
    "(8)test: 用户登录测试 #议题Id; "
    "(9)revert: 撤销用户登录提交 #议题Id;"
  )

  joined=$(IFS=';' echo "${arr[*]}")
  echo "提交信息不符合规范，请使用规范的格式提交！正确示例：${joined}"
  exit 1
}

```

## 四 添加 pre-commit 钩子

.husky 没有 pre-commit 的话，

- husky v7.x 执行下面的语句: `npx husky add .husky/pre-commit 'npx lint-staged --allow-empty --verbose'`
- husky v9.x 执行完 `npx husky init`之后就会生成.husky/pre-commit 文件

将内容全部替换为

```bash
#!/bin/bash


# Branch命名检查
# 获取当前Branch的名称
CURRENT_BRANCH=$(git rev-parse --abbrev-ref HEAD)

# 检查是否成功获取Branch名称
if [ -z "$CURRENT_BRANCH" ]; then
  echo "错误：无法获取当前Branch名称。请确保在Git仓库中运行此脚本。"
  exit 1
fi

# 定义Branch命名规范的正则表达式
# 正则匹配规则，支持中文、字母、数字、连字符（使用 Perl 模式）
PATTERN="^((feat|hotfix|bugfix)/[0-9]+-[a-zA-Z0-9一-龥\\-]+|release/(v\d+\.\d+\.\d+(-[a-zA-Z0-9一-龥\-]+)?|\d{8})|(master|dev))$"

# 校验分支名称
if ! echo "$CURRENT_BRANCH" | grep -Pq "$PATTERN"; then

  arr=(
    "(1)feat/215-用户登录界面;   "
    "(2)hotfix/284-修复用户名重复问题;   "
    "(3)bugfix/233-修复404问题;   "
    "(4)release/v1.0.0|release/20250516;  "
    "(5)master;   "
    "(6)dev")

  # 使用分号连接
  joined=$(IFS=';' echo "${arr[*]}")

  echo "分支名称 '$CURRENT_BRANCH' 不符合命名规范。命名规则:[issue_type]/[issue_id]-description, 请使用以下命名规范之一：$joined"

  exit 1
fi

# 注意这里加了 || true
files=$(git diff --cached --name-only --diff-filter=AM | grep -E '\.(ts|tsx|vue)$' || true)

if [ -z "$files" ]; then
  exit 0
fi

set +e
output=$(echo "$files" | xargs -r npx eslint 2>&1)
exit_code=$?

if [ $exit_code -ne 0 ]; then
  echo "ESLint 检查未通过，请修复后再提交！"
  echo "$output"
  exit $exit_code
fi

```

## 5. 提交信息关联议题id

在.husky目录下新建一个文件prepare-commit-msg，内容如下：
脚本里面的项目路径 `research-and-development/algorithm/6-ai-service-platform`,是gitlab填写议题链接的路径部分，请根据实际情况修改

```bash
#!/bin/sh

# 仅当手动写 commit message 时执行
if [ "$2" = "merge" ] || [ "$2" = "squash" ]; then
  exit 0
fi

file="$1"
msg=$(cat "$file")

# 查找是否包含 #数字 格式的 Issue 编号
issue_number=$(echo "$msg" | grep -Eo '#[0-9]+' | head -n1 | sed 's/#//')

if [ -n "$issue_number" ]; then
  # 项目路径
  project_path="research-and-development/algorithm/6-ai-service-platform"

  # 如果已经包含路径，则不重复添加
  echo "$msg" | grep -q "$project_path" && exit 0

  echo "" >>"$file"
  echo "Related to $project_path#$issue_number" >>"$file"
fi

```

现在提交代码,提交语最后面写上 `#议题id` ,举例 `feat: 跨项目提交测试 #194 `,提交到远程之后，远程仓库议题下面会多一个链接，点击链接就能看到提交内容

![alt text](议题关联.png)

# git hook提交代码时进行代码校验配置方法

## ✅ 1. 安装Husky v9

```
npm install --save-dev husky
```

---

## ✅ 2. 初始化Husky v9

```
npx husky init
```

这会自动：

- 创建 `.husky/` 目录
- 添加 `pre-commit` hook 示例
- 添加 `"prepare": "husky install"` 脚本

---

## ✅ 3. 配置 `package.json`

```
{
  "scripts": {
    "lint": "run-s lint:*",
    "lint:eslint":  "eslint src/**/*.{ts,tsx,vue}  --debug",
    "lint:prettier": "prettier --check ./",
    "lint:style":  "stylelint \"src/**/*.{vue,css,less}\" --fix",
  },
}
```

---

## ✅ 4. 修改 `.husky/pre-commit` hook

```
npm run lint
```

---

## ✅ 5. 测试是否生效

修改 `src` 下的某个 `.ts` / `.tsx` 文件，故意触发代码质量问题，然后：

```
git add .
git commit -m "Test husky v9"
```

会看到 ESLint 自动运行并格式化文件，**如果有未修复的错误，提交会被中断**。

![1745213330368](image/README/1745213330368.png)

测试一下
