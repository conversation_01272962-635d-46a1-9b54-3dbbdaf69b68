import request from '@/utils/request';

export interface IAddLabel {
  category_name: string;
  category_seq: number;
  tag_name: string;
  tag_seq: number
}

export function addLabel(data: IAddLabel) {
  return request.$Axios.post('/model/tag', data);
}

export function fetchAllLabel() {
  return request.$Axios.get('/model/tag_gather');
}

export interface IUpdateTag extends IAddLabel {
  id: string
}
export function updateTag(oid: string, data: IUpdateTag) {
  return request.$Axios.put(`/model/tag/${oid}`, data);
}
// 更新多个标签
export function updateTags(data: IUpdateTag[]) {
  return request.$Axios.put(`/model/tag_gather`, data);
}

export interface IUpdataeCatagory {
  fr: string,
  to: string
}
export function updateCatagory(data: IUpdataeCatagory) {
  return request.$Axios.put(`/model/tag_category`, data);
}


// 删除标签
export const deleteTag = (oid: string) => {
  return request.$Axios.del(`/model/tag/${oid}`);
}
// 删除分类
export const deleteCatagory = (data: { category_name: string }) => {
  return request.$Axios.post(`/model/tag_category`, data);
}
