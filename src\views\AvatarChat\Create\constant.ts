export const DigitalHumanTips = [
  '1、请尽量保持正对摄像头拍摄',
  '2、保持完整头部及双肩出现在镜头内',
  '3、人脸端正，且不可有遮挡脸部等动作',
  '4、保持背景干净整洁，不要出现其他人像',
  '5、请确保上传的图片清晰，否则可能影响定制效果',
];
export const UploadAvatarTips = [
  '1、虚拟形象定制基于右侧数字人换脸实现',
  '2、虚拟形象定制后不可修改',
  '3、默认您同意使用该虚拟形象合成视频',
  '4、虚拟形象定制后不可用于商业用途',
];

export const TrainModes = [
  {
    disabled: false,
    value: 'fast',
    title: '快速训练',
    tips: '效果一般，训练时间约5分钟',
  },
  {
    disabled: true,
    value: 'precision',
    title: '精准训练',
    tips: '效果较佳，训练时间约5小时',
  },
];

export const theme = {
  components: {
    Radio: {
      // 自定义单选按钮的原点高亮色和边框属性
      dotColorChecked: '#40a9ff', // 高亮时的圆点颜色
      dotBorderColorChecked: '#40a9ff', // 高亮时的边框颜色
      dotSize: 6, // 圆点大小
      radioSize: 12, // 单选框大小
    },
    Spin: {
      contentHeight: 298,
    },
  },
};

export interface RefProps {
  openModal: () => void;
}

export interface DigitalHumanItemProps {
  url: string;
  gender: string;
}
export interface StepProps {
  step: number;
  title: string;
  tooltips?: string[];
}
