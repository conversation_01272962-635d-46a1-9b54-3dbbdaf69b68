<script setup lang="ts">
  import { ref, reactive, onMounted } from 'vue';
  import type { ColumnType } from 'ant-design-vue/es/table';
  import { useRoute, useRouter } from 'vue-router';
  import { fetchClusterHistory, fetchHistoryYamlConfig } from '@/api/k8s';
  import type { IClusterHistory } from '@/interface/kubernetes';
  import { copyText, monthsBetween } from '@/utils/common';
  import { VAceEditor } from 'vue3-ace-editor';
  import 'ace-builds/src-noconflict/theme-chrome';
  import 'ace-builds/src-noconflict/theme-monokai.js';
  import 'ace-builds/src-noconflict/mode-python.js';
  import 'ace-builds/src-noconflict/mode-sh.js';
  import type { IAceOptions } from '@/interface';
  import yaml from 'js-yaml';

  const router = useRouter();
  const route = useRoute();
  const loading = ref(false);
  const dataSource = reactive<IClusterHistory[]>([]);
  const YAMLState: { visible: boolean; value: string; title: string; spinning: boolean } = reactive({
    visible: false,
    title: 'Deployment',
    spinning: false,
    value: '',
  });
  const columns: ColumnType[] = [
    { title: '副本集', dataIndex: 'replicaset_name' },
    { title: '镜像标签', dataIndex: 'images' },
    { title: '已创建', dataIndex: 'creation_time' },
    { title: '版本', dataIndex: 'revision' },
    { title: '期望', dataIndex: 'desired' },
    { title: '当前', dataIndex: 'current' },
    { title: '就绪', dataIndex: 'ready' },
    { title: '操作', dataIndex: 'operation' },
  ];

  const aceOptions: IAceOptions = {
    enableBasicAutocompletion: true,
    enableSnippets: true,
    enableLiveAutocompletion: true,
    tabSize: 4,
    enableEmmet: true,
    fontSize: 15,
    showPrintMargin: false, // 去除编辑器里的竖线
    highlightActiveLine: true,
    useWorker: true,
    readOnly: false,
  };
  const fetchList = async () => {
    loading.value = true;
    const data: IClusterHistory[] = await fetchClusterHistory(route.params.name as string);
    dataSource.length = 0;
    dataSource.push(...data);
    loading.value = false;
  };
  const fetchYamlConfigReq = async (record: IClusterHistory) => {
    YAMLState.visible = true;
    YAMLState.spinning = true;
    const data = await fetchHistoryYamlConfig(record.replicaset_name);
    YAMLState.value = yaml.dump(data);
    YAMLState.spinning = false;
  };

  onMounted(() => {
    fetchList();
  });
</script>

<template>
  <a-table :data-source="dataSource" :columns="columns" :pagination="false" :loading="loading">
    <template #bodyCell="{ column, text, record }">
      <div v-if="column.dataIndex === 'operation'">
        <a @click="fetchYamlConfigReq(record)">YAML</a>
      </div>
      <div v-else-if="['creation_time'].includes(column.dataIndex)">
        {{ monthsBetween(text) }}
      </div>
      <div v-else-if="column.dataIndex === 'images'">
        {{ text.join('、') }}
      </div>
      <div v-else>{{ text }}</div>
    </template>
  </a-table>
  <a-modal
    v-model:open="YAMLState.visible"
    :title="YAMLState.title"
    width="60%"
    centered
    :footer="false"
    @cancel="
      YAMLState.visible = false;
      YAMLState.value = '';
    "
  >
    <div class="h-800px overflow-scroll">
      <a-spin :spinning="YAMLState.spinning">
        <div class="w-100% h-32px flex flex-justify-end">
          <a @click="copyText(YAMLState.value)"> 复制到剪切板 </a>
        </div>
        <v-ace-editor
          v-model:value="YAMLState.value"
          lang="sh"
          theme="monokai"
          :options="aceOptions"
          style="height: calc(100% - 32px); border: 1px solid #dbd3d3"
        />
      </a-spin>
    </div>
  </a-modal>
</template>

<style scoped lang="less"></style>
