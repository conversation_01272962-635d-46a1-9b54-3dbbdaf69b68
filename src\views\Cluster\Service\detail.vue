<script setup lang="ts">
  import { ref } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { ArrowLeftOutlined } from '@ant-design/icons-vue';
  import Info from './Components/info.vue';
  import Pods from '@/views/Cluster/Components/pods.vue';
  import Event from '@/views/Cluster/Components/event.vue';
  import Workload from '@/views/Cluster/Workload/index.vue';
  const router = useRouter();
  const route = useRoute();
  const activeKey = ref<string>('1');
</script>

<template>
  <div class="h-100%">
    <div class="text-xl m-b-20px">
      <ArrowLeftOutlined @click="router.back()" />
      <span class="m-l-10px">{{ route.params.name }}</span>
    </div>
    <a-tabs v-model:active-key="activeKey" type="card">
      <a-tab-pane key="1" tab="详情">
        <Info />
      </a-tab-pane>
      <a-tab-pane key="2" tab="容器组">
        <Pods />
      </a-tab-pane>
      <a-tab-pane key="3" tab="事件">
        <Event />
      </a-tab-pane>
      <a-tab-pane key="4" tab="工作负载">
        <Workload />
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<style scoped lang="less">
  :deep(.ant-tabs) {
    height: calc(100% - 50px);
  }
  :deep(.ant-tabs-content-holder) {
    overflow: auto;
  }
</style>
