import type { IPage } from ".";

export interface IFetchKnowledgedbList extends IPage {
  name?: string
}

export interface ICreateKnowledgedb {
  name: string;
  description?: string;
  embedding_model: string;
  files: IFiles[]
}
export interface IFiles {
  file_name: string;
  url: string;
}


export interface IKnowledgedb {
  name: string;
  description: string;
  embedding_model: string;
  files: IFiles[];
  creator_id: string;
  updater_id: null | string;
  id: string;
  created_at: string;
  updated_at: null | string;
  deleted_at: null | string;
}

export interface IKnowledgeFileItem {
  name: string;
  file_uuid: string;
  url: string;
  type: string;
  word_count: null;
  row_count: null;
  col_count: null;
  status: string;
  error_msg: string;
  creator_id: string;
  updater_id: number;
  id: string;
  created_at: string;
  updated_at: string;
  deleted_at: null;
}

export interface IKnowledgeFilterItem {
  id: string;
  name: string;
  created_at: string;
  files: IFileFilter[]
}

export interface IFileFilter {
  name: string;
  id: string;
  status: string;
}