<script setup lang="ts">
  import { ref, onMounted } from 'vue';
  import { fetchServicesDetail } from '@/api/k8s';
  import { useRoute } from 'vue-router';
  import type { IServiceDetailResponse } from '@/interface/kubernetes';
  import type { ColumnType } from 'ant-design-vue/es/table';
  import { convertIsoTimeToLocalTime } from '@/utils/common';
  const route = useRoute();
  const info = ref<IServiceDetailResponse>();
  const spinning = ref(false);

  const columns: ColumnType[] = [
    { title: 'URL 匹配方式', dataIndex: 'path_type' },
    { title: 'URL 映射', dataIndex: 'path' },
    { title: '服务名称', dataIndex: 'service_name' },
    { title: '服务端口', dataIndex: 'ports' },
  ];
  const getDetail = async () => {
    spinning.value = true;
    const data: IServiceDetailResponse = await fetchServicesDetail(route.params.name as string);
    info.value = data;
    spinning.value = false;
  };
  onMounted(() => {
    getDetail();
  });
</script>

<template>
  <div class="h-100%">
    <a-spin :spinning="spinning">
      <a-form :label-col="{ style: { width: '120px' } }" label-align="left">
        <div class="textbefo">基本信息</div>
        <a-row>
          <a-col :span="8">
            <a-form-item label="名称空间">{{ info?.namespace || '--' }}</a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="名称">{{ info?.name || '默认' }}</a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="创建时间">{{
              info?.creation_time ? convertIsoTimeToLocalTime(info.creation_time) : '--'
            }}</a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="8">
            <a-form-item label="服务类型">{{ info?.type || '--' }}</a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="Cluster IP">{{ info?.cluster_ip || '--' }}</a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="会话保持">{{ info?.session_affinity || '--' }}</a-form-item>
          </a-col>
        </a-row>

        <div class="textbefo">注解 / 标签</div>
        <a-form-item label="注解">
          <div v-for="key in Object.keys(info?.annotations || {})" :key="key" class="m-b-10px">
            <span class="bg-#36435c text-#fff p-x-5px">{{ key }}</span>
            <span class="bg-#eff4f9 p-l-10px">{{
              info?.annotations && info.annotations[key] ? JSON.stringify(JSON.parse(info.annotations[key])) : ''
            }}</span>
          </div>
        </a-form-item>
        <a-form-item label="标签">
          <div v-for="key in Object.keys(info?.labels || {})" :key="key" class="m-b-10px">
            <span class="bg-#36435c text-#fff p-x-5px">{{ key }}</span>
            <span class="bg-#eff4f9 p-l-10px">
              {{ info?.labels[key] || '' }}
            </span>
          </div>
        </a-form-item>
        <a-form-item label="标签选择器">
          <div v-for="key in Object.keys(info?.label_selector || {})" :key="key" class="m-b-10px">
            <span class="bg-#36435c text-#fff p-x-5px">{{ key }}</span>
            <span class="bg-#eff4f9 p-l-10px">{{ info?.label_selector[key] || '' }}</span>
          </div>
        </a-form-item>

        <div class="textbefo">路由</div>
        <a-form-item label="域名">{{ info?.ingress.ingress_name || '--' }}</a-form-item>
      </a-form>
      <a-form layout="vertical">
        <a-form-item label="路由配置：">
          <a-table :data-source="info?.ingress.routes" :columns="columns" :pagination="false">
            <template #bodyCell="{ column, text, record }">
              <div v-if="column.dataIndex === 'ports'">
                <span class="bg-#234883 text-#fff p-x-5px rounded-l-[10px]">{{ record.service_port }}</span>
                <span class="bg-#657fa8 text-#fff p-x-5px rounded-r-[10px]">{{ record.protocol }}</span>
              </div>
              <div v-else>{{ text }}</div>
            </template>
          </a-table>
        </a-form-item>
      </a-form>
    </a-spin>
  </div>
</template>

<style scoped lang="less">
  :deep(.ant-form-item) {
    margin-bottom: 0;
  }
</style>
