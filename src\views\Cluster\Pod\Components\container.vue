<script setup lang="ts">
  import { ref, reactive, onMounted, computed } from 'vue';
  import type { ColumnType } from 'ant-design-vue/es/table';
  import { fetchPodContainerList, fetchPodContainerLogs } from '@/api/k8s';
  import { useRoute } from 'vue-router';
  import type { IPodContainerItemResponse } from '@/interface/kubernetes';
  import { getRandomCharacters } from '@/utils/common';
  import type { IAceOptions } from '@/interface';
  import { VAceEditor } from 'vue3-ace-editor';
  import 'ace-builds/src-noconflict/theme-chrome';
  import 'ace-builds/src-noconflict/theme-monokai.js';
  import 'ace-builds/src-noconflict/mode-python.js';
  import 'ace-builds/src-noconflict/mode-sh.js';
  const route = useRoute();
  const loading = ref(false);
  const dataSource = reactive<IPodContainerItemResponse[]>([]);
  const columns: ColumnType[] = [
    { title: '容器名称', dataIndex: 'name', fixed: 'left', width: 200 },
    { title: '镜像', dataIndex: 'image', width: 200 },
    { title: '镜像抓取策略', dataIndex: 'image_pull_policy' },
    { title: 'CPU 需求/限制 ', dataIndex: 'gpu_limit' },
    { title: '内存需求/限制', dataIndex: 'memory_limit' },
    { title: '重启次数', dataIndex: 'restart_count' },
    { title: '状态', dataIndex: 'status' },
    { title: '容器端口', dataIndex: 'ports' },
    { title: '命令与参数', dataIndex: 'command', fixed: 'right' },
    { title: '环境变量', dataIndex: 'env', fixed: 'right' },
    { title: '数据卷挂载', dataIndex: 'volume_mounts', fixed: 'right' },
    { title: '操作', dataIndex: 'operation', fixed: 'right' },
  ];
  const currentRecord = ref<IPodContainerItemResponse>();
  const args = computed(() => (currentRecord.value?.args ? currentRecord.value?.args.join('') : '暂无数据'));
  const command = computed(() => (currentRecord.value?.command ? currentRecord.value?.command.join('') : '暂无数据'));
  interface IModalState {
    command: boolean;
    env: boolean;
    volume_mounts: boolean;
    log: boolean;
  }
  const modalState: IModalState = reactive({
    command: false,
    env: false,
    volume_mounts: false,
    log: false,
  });
  const logs = ref('');
  const aceOptions: IAceOptions = {
    enableBasicAutocompletion: true,
    enableSnippets: true,
    enableLiveAutocompletion: true,
    tabSize: 4,
    enableEmmet: true,
    fontSize: 15,
    showPrintMargin: false, // 去除编辑器里的竖线
    highlightActiveLine: true,
    useWorker: true,
    readOnly: true,
    wrap: true,
  };
  const checkLog = async (record: IPodContainerItemResponse) => {
    const data: string = await fetchPodContainerLogs({
      pod_name: route.params.name as string,
      container_name: record.name,
    });
    logs.value = data;
    modalState.log = true;
  };
  const handleCheck = (record: IPodContainerItemResponse, dataIndex: keyof IModalState) => {
    currentRecord.value = record;
    modalState[dataIndex] = true;
  };

  const getPodContainerList = async () => {
    loading.value = true;
    const data: IPodContainerItemResponse[] = await fetchPodContainerList(route.params.name as string);
    dataSource.length = 0;
    dataSource.push(...data);
    loading.value = false;
  };
  onMounted(() => {
    getPodContainerList();
  });
</script>

<template>
  <a-table :data-source="dataSource" :columns="columns" :pagination="false" :scroll="{ x: 1500 }" :loading="loading">
    <template #bodyCell="{ column, text, record }">
      <div v-if="column.dataIndex === 'operation'">
        <a class="table-a-btn" @click="checkLog(record)">查看日志</a>
      </div>
      <div v-else-if="['command', 'env', 'volume_mounts'].includes(column.dataIndex)">
        <a class="table-a-btn" @click="handleCheck(record, column.dataIndex)">查看</a>
      </div>
      <div v-else-if="column.dataIndex === 'ports'">
        <div v-if="record.ports.length">
          <div v-for="item in record.ports" :key="item + getRandomCharacters()">
            <span class="bg-#234883 text-#fff p-x-5px rounded-l-[10px]">{{ item.container_port }}</span>
            <span class="bg-#657fa8 text-#fff p-x-5px rounded-r-[10px]">{{ item.protocol }}</span>
          </div>
        </div>
        <div v-else>--</div>
      </div>
      <div v-else-if="column.dataIndex === 'gpu_limit'">
        <div>{{ record.cpu_request || '--' }} / {{ record.cpu_limit || '--' }}</div>
      </div>
      <div v-else-if="column.dataIndex === 'memory_limit'">
        <div>{{ record.memory_request || '--' }} / {{ record.memory_limit || '--' }}</div>
      </div>
      <div v-else>{{ text || '--' }}</div>
    </template>
  </a-table>
  <a-modal v-model:open="modalState.command" title="命令和参数" width="60%" centered :footer="false">
    <div class="textbefo">命令</div>
    <v-ace-editor
      v-model:value="command"
      lang="sh"
      theme="monokai"
      :options="aceOptions"
      style="height: 300px; border: 1px solid #dbd3d3"
    />
    <div class="textbefo">参数</div>
    <v-ace-editor
      v-model:value="args"
      lang="sh"
      theme="monokai"
      :options="aceOptions"
      style="height: 300px; border: 1px solid #dbd3d3"
    />
  </a-modal>
  <a-modal v-model:open="modalState.env" title="环境与变量" width="60%" centered :footer="false">
    <div v-for="record in currentRecord?.env" :key="record.name" class="env-item flex">
      <div>{{ record.name }}</div>
      <span>=</span>
      <div>{{ record.value }}</div>
    </div>
  </a-modal>
  <a-modal v-model:open="modalState.volume_mounts" title="数据卷挂载" width="60%" centered :footer="false">
    <div v-for="volume in currentRecord?.volume_mounts || []" :key="volume.name" class="volume-box m-b-10px">
      <div class="header flex bg-#e9edf3 p-10px">
        <span class="text-#fff p-x-5px m-r-10px" :style="{ backgroundColor: volume.read_only ? '#ff8807' : '#0fbe8f' }">
          {{ volume.read_only ? '只读' : '读写' }}
        </span>
        <div>
          <span class="border border-solid border-#646464 border-1px p-x-2px">mount_path</span>
          <span class="border border-solid border-#646464 border-1px bg-#646464 p-x-2px text-#fff">{{
            volume.mount_path
          }}</span>
        </div>
      </div>
      <div class="Container">
        <div><span>name:</span>{{ currentRecord?.volumes.find((item) => item.name === volume.name)?.name }}</div>
        <div><span>HostPath:</span></div>
        <div class="p-l-10px">
          <span>path:</span>{{ currentRecord?.volumes.find((item) => item.name === volume.name)?.host_path?.path }}
        </div>
        <div class="p-l-10px">
          <span>type:</span>
          {{ currentRecord?.volumes.find((item) => item.name === volume.name)?.host_path?.type }}
        </div>
      </div>
    </div>
  </a-modal>

  <a-modal v-model:open="modalState.log" title="日志" width="60%" centered :footer="false">
    <v-ace-editor
      v-model:value="logs"
      lang="sh"
      theme="monokai"
      :options="aceOptions"
      style="height: 600px; border: 1px solid #dbd3d3"
    />
  </a-modal>
</template>

<style scoped lang="less">
  .volume-box {
    border: 1px solid #a7b6cd;
    border-radius: 10px;
    overflow: hidden;
  }
  .env-item {
    background-color: #e9edf3;
    border-radius: 10px;
    padding: 5px 10px;
    margin-bottom: 10px;
    > div {
      width: calc(50% - 10px);
      padding: 0 10px;
      background-color: #f5f7fa;
      color: #333;
    }
    > span {
      margin: 0 10px;
    }
  }
  .Container {
    padding: 5px;
    > div {
      > span {
        color: #111188;
        padding-right: 5px;
      }
    }
  }
</style>
