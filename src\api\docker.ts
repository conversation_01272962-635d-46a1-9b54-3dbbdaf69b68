
import request from '@/utils/request';

export function fetchDockerList(data: {page: any,limit: any}) {
  return request.$Axios.get('/docker_image/list', data);
}

export function deleteDocker(id: string) {
  return request.$Axios.del(`/docker_image/${id}`,{ id });
}

export function dockerDropDown() {
  return request.$Axios.get(`/docker_image/filter_list`);
}

export function dockerStatus(data: any) {
  return request.$Axios.put(`/docker_image/${data.operation}/${data.oid}`,data);
}