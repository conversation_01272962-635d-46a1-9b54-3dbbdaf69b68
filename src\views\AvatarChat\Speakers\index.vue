<script setup lang="ts">
  import { ref, onMounted } from 'vue';
  import { reactive, onBeforeUnmount } from 'vue';
  import { getSpeakersList } from '@/api/avatarChat';

  import stopIcon from '@/assets/image/base/pictures/stop.png';
  import playIcon from '@/assets/image/base/pictures/play.png';

  const speakersData = ref<Record<string, any[]>>({}); // 说话人音色列表
  const tagsData = reactive(['普通话', '方言', '外语']);
  // 映射标签到数据键
  const tagToKeyMap = {
    普通话: 'mandarin',
    方言: 'dialect',
    外语: 'foreign',
  };

  // 当前选中的语言类型
  const selectedLanguage = ref('mandarin');
  const selectedSpeaker = ref<string | null>(null);
  const hoverStates = reactive<Record<number, boolean>>({});
  const selectedTag = ref<string | null>('普通话');
  const isPlaying = reactive<Record<number, boolean>>({});
  const isOpenModal = ref(false);

  // 定义 props 和 emits
  const props = defineProps<{
    handleCloseModal?: () => void;
  }>();

  const emit = defineEmits<{
    'speaker-selected': [speaker: { name: string; description: string }];
  }>();

  const openSpeakersModal = () => {
    isOpenModal.value = true;
    // trainMode.value = 'fast';
    // uploadAvatarUrl.value = '';
    // preViewUrl.value = '';
    // selectedDigitalMan.value = { index: 0, url: '', gender: '' };
    // isNameEntered.value = true;
    // isAvatarUploaded.value = true;
    // queryDigitalHumanList();
  };

  defineExpose({
    openSpeakersModal,
  });

  const handleOk = () => {
    // 如果还没有选中声音，可以选择第一个
    if (!selectedSpeaker.value) {
      const currentSpeakers = speakersData.value[selectedLanguage.value] || [];
      if (currentSpeakers.length > 0) {
        handleSpeakerSelect(currentSpeakers[0].name);
      }
    }
    isOpenModal.value = false;
  };

  const handleCancel = () => {
    isOpenModal.value = false;
  };

  // 说话人音色数据
  const getSpeakersData = async () => {
    try {
      const data = await getSpeakersList();
      speakersData.value = data; // 设置音色数据
    } catch (error) {
      console.error('获取音色列表失败:', error);
    }
  };

  // 页面加载时调用 getBackgroundData
  onMounted(() => {
    getSpeakersData();
  });

  // 管理音频元素
  const audioElements = reactive<Record<number, HTMLAudioElement | null>>({});

  const handleChange = (tag: string) => {
    selectedTag.value = tag; // 设置当前选中的标签
    // 获取对应的数据键
    const key = tagToKeyMap[tag as keyof typeof tagToKeyMap];
    if (key) {
      selectedLanguage.value = key;

      // 清空所有播放状态和音频元素
      Object.keys(isPlaying).forEach((index) => {
        isPlaying[Number(index)] = false;
      });
      Object.values(audioElements).forEach((audio) => {
        audio?.pause();
        audio?.remove();
      });
      Object.keys(audioElements).forEach((index) => {
        audioElements[Number(index)] = null;
      });
    } else {
      console.warn(`未找到与标签 "${tag}" 对应的语言类型`);
    }
  };

  // 播放音频
  const playAudio = (index: number, sampleFileUrl: string) => {
    // 先停止所有正在播放的音频
    Object.keys(isPlaying).forEach((key) => {
      const keyIndex = Number(key);
      if (isPlaying[keyIndex] && keyIndex !== index) {
        isPlaying[keyIndex] = false;
        if (audioElements[keyIndex]) {
          audioElements[keyIndex]?.pause();
        }
      }
    });

    if (!audioElements[index]) {
      audioElements[index] = new Audio(sampleFileUrl);
      // 添加 ended 事件监听器
      audioElements[index]!.addEventListener('ended', () => {
        isPlaying[index] = false; // 停止播放状态
      });
    }

    const audio = audioElements[index];
    if (audio) {
      if (audio.paused) {
        audio
          .play()
          .then(() => {
            isPlaying[index] = true;
          })
          .catch((err) => {
            console.error('音频播放失败:', err);
          });
      } else {
        audio.pause();
        isPlaying[index] = false;
      }
    }
  };

  // const emit = defineEmits(['speaker-selected']);
  // const handleSpeakerSelect = (name: string) => {
  //   selectedSpeaker.value = name;
  //   emit('speaker-selected', name);
  // };

  // // 初始化默认选中普通话的第一个说话人
  // const initializeDefaultSelection = () => {
  //   // 如果已经有选中的说话人，使用它
  //   if (selectedSpeaker) {
  //     selectedSpeaker.value = selectedSpeaker;
  //     emit('speaker-selected', selectedSpeaker);
  //     return;
  //   }

  //   // 否则默认选择普通话的第一个说话人
  //   const mandarinSpeakers = speakersData.value.mandarin;
  //   if (mandarinSpeakers && mandarinSpeakers.length > 0) {
  //     selectedSpeaker.value = mandarinSpeakers[0].name;
  //     emit('speaker-selected', mandarinSpeakers[0].name);
  //   }
  // };

  // 选择说话人
  const handleSpeakerSelect = (speakerName: string) => {
    selectedSpeaker.value = speakerName;

    // 找到选中的说话人数据
    const currentSpeakers = speakersData.value[selectedLanguage.value] || [];
    const selectedSpeakerData = currentSpeakers.find((speaker) => speaker.name === speakerName);

    if (selectedSpeakerData) {
      // 按照指定格式回显：普通话-灵动女声
      // 如果不存在description，则使用name字段
      const displayName = selectedSpeakerData.description || selectedSpeakerData.name;
      const displayText = `${selectedTag.value}-${displayName}`;

      emit('speaker-selected', {
        name: selectedSpeakerData.name,
        description: displayText,
      });

      // 立即关闭弹窗
      isOpenModal.value = false;
    }
  };
  // 组件卸载时销毁音频元素
  onBeforeUnmount(() => {
    Object.values(audioElements).forEach((audio) => {
      audio?.pause();
      audio?.remove();
    });
  });

  // 初始化默认选择
  // initializeDefaultSelection();
</script>

<template>
  <a-modal
    :open="isOpenModal"
    title="选择声音"
    :width="1122"
    :mask-closable="false"
    centered
    :style="{ height: '800px', background: '#ffffff', borderRadius: '8px' }"
    :body-style="{ overflow: 'hidden' }"
    :footer="null"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <div class="speakers-modal">
      <div class="speakers-modal-header">
        <div class="speakers-container">
          <div class="speakers-tag">
            <a-tag
              v-for="tag in tagsData"
              :key="tag"
              :class="{ selected: selectedTag === tag }"
              @click="() => handleChange(tag)"
            >
              {{ tag }}
            </a-tag>
          </div>
          <div class="speakers-container-image">
            <div
              v-for="(item, index) in speakersData[selectedLanguage]"
              :key="index"
              class="imageBox"
              :class="{ selected: selectedSpeaker === item.name }"
              @mouseenter="hoverStates[index] = true"
              @mouseleave="hoverStates[index] = false"
              @click="handleSpeakerSelect(item.name)"
            >
              <div class="img-container" :class="{ grayscale: hoverStates[index] || isPlaying[index] }">
                <img class="img" :src="item.image_url" :alt="`${item.name} ${index}`" />
                <!-- 播放区域 - 点击试听 -->
                <div
                  v-if="hoverStates[index] || isPlaying[index]"
                  class="play-area"
                  @click.stop="playAudio(index, item.sample_file_url)"
                >
                  <img v-if="!isPlaying[index]" :src="playIcon" alt="播放" />
                  <img v-else :src="stopIcon" alt="停止" />
                </div>
              </div>
              <div class="name">{{ item?.description || item?.name }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- <template #footer>
      <div class="modal-footer">
        <a-button @click="handleCancel">取消</a-button>
        <a-button type="primary" :disabled="!selectedSpeaker" @click="handleOk">确认</a-button>
      </div>
    </template> -->
  </a-modal>
</template>

<style lang="less" scoped>
  .speakers-modal {
    border-top: #f0f1f2 solid 1px;

    .speakers-container {
      width: 100%;
      height: 100%;
      background: #ffffff;

      .speakers-tag {
        margin-bottom: 10px;
        display: flex;
        justify-content: flex-start;
        height: 32px;
        margin: 10px 20px;
        // background: #ffffff !important;

        :deep(.ant-tag) {
          font-family:
            PingFangSC,
            PingFang SC;
          font-weight: 400;
          padding: 7px 14px;
          background: #ffffff;

          font-size: 14px;
          color: #969799;
          line-height: 20px;
          text-align: right;
          border: none;
          &:hover {
            color: #000000;
          }
          &.selected {
            background: #f2f8ff;
            color: #000000;
          }
        }
      }

      .speakers-container-image {
        display: flex;
        justify-content: flex-start;
        flex-wrap: wrap;
        max-height: 623px; /* 设置固定高度 */
        overflow-y: auto; /* 启用纵向滚动 */
        overflow-x: hidden; /* 禁用横向滚动 */
        background: #ffffff;
        // box-shadow: 4px 2px 24px 0px rgba(123, 123, 123, 0.1);

        /* 隐藏滚动条 */
        ::-webkit-scrollbar {
          display: none; /* 隐藏滚动条 */
        }

        -ms-overflow-style: none; /* 适用于 IE 和 Edge */
        scrollbar-width: none; /* 适用于 Firefox */

        .imageBox {
          width: 134px;
          height: 128px;
          cursor: pointer;
          overflow: hidden;
          position: relative;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          border-radius: 8px;
          margin: 10px;
          background: #fafafa;
          /* box-shadow: 4px 2px 24px 0px rgba(123, 123, 123, 0.1); */

          .name{
            margin-top: 4px;
          }

          .img {
            width: 60px;
            height: 60px;
            /* margin: 2px 0px; */
            /* border-radius: 8px; */
          }

          &:hover {
            border: 1px solid #1777ff;
          }

          &.selected {
            border: 2px solid #1777ff;
          }

          .img-container {
            position: relative;
          }

          .grayscale {
            filter: grayscale(100%);
          }

          .play-area {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            display: flex;
            justify-content: center;
            align-items: center;
            width: 60px;
            height: 60px;
            background: rgba(0, 0, 0, 0.5);
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;

            &:hover {
              background: rgba(0, 0, 0, 0.7);
            }

            img {
              width: 30px;
              height: 30px;
            }
          }
        }
      }
    }
  }
</style>
