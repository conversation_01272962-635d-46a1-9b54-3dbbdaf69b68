<script setup lang="ts">
  import { ref, reactive, nextTick, onMounted, watch } from 'vue';
  import type { IIntegrationDetail, IIntegrationTaskResponse } from '@/interface/integration';
  import sendBtn from '@/assets/image/base/pictures/sendbtn.png';
  import sendDisableBtn from '@/assets/image/base/pictures/sendDisable.png';
  import { createIntegrationTask, getSvcStatus } from '@/api/integration';
  import { useRoute } from 'vue-router';
  import { convertIsoTimeToLocalTime } from '@/utils/common';
  import { CheckOutlined, CloseOutlined } from '@ant-design/icons-vue';
  //@ts-expect-error
  import MarkdownIt from 'markdown-it';
  const md = new MarkdownIt({
    html: true, // 允许HTML标签
    linkify: true, // 自动转换URL为链接
    typographer: true, // 美化排版
  });

  interface IProps {
    info: IIntegrationDetail;
    activeKey: string;
  }
  const props = defineProps<IProps>();
  const route = useRoute();
  const messageRef = ref();
  const isLoading = ref(false);
  const open = ref(false);
  const loadingOpen = ref(false);
  const typingInterval = ref();
  interface ISvcItem {
    status: string;
    svc: string;
  }
  const svcList = ref<ISvcItem[]>([]);
  const inputMessage = ref('');
  interface IMessage {
    role: string;
    content: string;
    created_at?: string;
    svc?: string;
  }
  const messages = reactive<IMessage[]>([]);
  const handleSend = async (value: string) => {
    if (!value || !value.trim()) {
      open.value = true;
      return;
    }
    if (isLoading.value) {
      loadingOpen.value = true;
      return;
    }
    const userMessage = { role: 'user', content: value };
    messages.push(userMessage);
    inputMessage.value = '';
    await nextTick(() => {
      if (messageRef.value) {
        messageRef.value[messageRef.value.length - 1].scrollIntoView({ behavior: 'auto', block: 'end' });
      }
    });
    const assistantMessage = { role: 'assistant', content: '', created_at: '' };
    messages.push(assistantMessage);
    isLoading.value = true;
    try {
      const data: IIntegrationTaskResponse = await createIntegrationTask(String(route.params.id), value);
      const answer = md.render(data.content);
      let index = 0;
      async function typeEffect() {
        if (index < answer.length) {
          messages[messages.length - 1].content += answer[index];
          // 将当前字符添加到元素中
          index++;
          // 设置下一次调用的时间间隔
          typingInterval.value = setTimeout(typeEffect, 50); // 每50毫秒打一个字

          await nextTick(() => {
            if (messageRef.value) {
              messageRef.value[messageRef.value.length - 1].scrollIntoView({ behavior: 'auto', block: 'end' });
            }
          });
        } else {
          isLoading.value = false;
          messages[messages.length - 1].created_at = data.created_at;
          messages[messages.length - 1].svc = data.svc;
        }
      }
      typeEffect();
    } catch {
      isLoading.value = false;
      messages[messages.length - 1].content = '服务异常，请稍后再试。。。';
      loadingOpen.value = false;
    }
  };
  const handleChange = () => {
    open.value = false;
    loadingOpen.value = false;
  };

  const fetchSvcStatus = async () => {
    const data: ISvcItem[] = await getSvcStatus(String(route.params.id));
    svcList.value = data;
  };

  onMounted(() => {
    fetchSvcStatus();
  });

  watch(
    () => props.activeKey,
    (key) => {
      if (key !== 'exploration') {
        open.value = false;
        loadingOpen.value = false;
      }
    },
    { deep: true },
  );
</script>

<template>
  <div class="container">
    <div class="left">
      <div class="title">集成服务</div>
      <div class="h-[calc(100%-30px)] overflow-scroll">
        <div v-for="item in svcList" :key="item.svc" class="service-item">
          <CheckOutlined v-if="item.status === 'running'" color="green" />
          <CloseOutlined v-else color="red" />
          <div class="ml-10px">{{ item.svc }}</div>
        </div>
      </div>
    </div>
    <div class="right">
      <div class="preview-content">
        <div ref="containerRef" class="chat-container overflow-scroll">
          <div class="messages flex flex-col">
            <div
              v-for="(message, index) in messages"
              :key="index"
              ref="messageRef"
              class="message"
              :style="{ justifyContent: message.role === 'user' ? 'flex-end' : 'start' }"
            >
              <div class="max-w-80%">
                <div v-if="message.content" :class="message.role" class="message-content">
                  <div v-html="message.content.replace(/<citation>.*?<\/citation>/g, '')"></div>
                </div>
                <div v-else class="message-content"><a-spin /></div>
                <div class="flex justify-end mt-10px">
                  <div v-if="message.created_at">时间：{{ convertIsoTimeToLocalTime(message.created_at) }}</div>
                  <div v-if="message.svc">较优模型服务：{{ message.svc }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="chat-box">
          <a-textarea
            v-model:value="inputMessage"
            class="custom-textarea"
            placeholder="请输入你的问题"
            :auto-size="{ minRows: 1, maxRows: 4 }"
            @change="handleChange"
            @press-enter.prevent="handleSend(inputMessage)"
          />

          <div class="flex flex-justify-end">
            <a-tooltip :arrow-point-at-center="true" :open="open || loadingOpen">
              <template #title>{{
                open ? '请输入您的问题' : loadingOpen ? '上一个问题还没回答完，请稍后' : ''
              }}</template>
              <img
                v-if="!inputMessage || !inputMessage.trim()"
                class="send-disabled-icon"
                :src="sendDisableBtn"
                alt=""
              />
              <img v-else class="send-icon" :src="sendBtn" alt="" @click="handleSend(inputMessage)" />
            </a-tooltip>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
  .container {
    display: flex;
    height: calc(100% - 10px);
  }
  .left {
    width: 30%;
    height: 100%;
    border: 1px solid #ddd;
    padding: 10px;
    .title {
      font-weight: bold;
      font-size: 18px;
      margin-bottom: 10px;
    }
    .service-item {
      width: 100%;
      height: 40px;
      border: 1px solid #ddd;
      display: flex;
      align-items: center;
      padding: 10px;
      margin-bottom: 10px;
      border-radius: 10px;
      cursor: pointer;
      // &:hover {
      //   border-color: #1890ff;
      //   color: #1890ff;
      //   background-color: #eef3fe;
      // }
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
  .right {
    width: calc(70% - 10px);
    height: 100%;
    padding: 10px;
    margin-left: 10px;
    background-color: #f5f5f5;
  }
  .preview-content {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .chat-container {
    // height: calc(100% - 50px);
    flex: 1;
    overflow-anchor: auto; /* 关键属性，让滚动条自动跟随新内容 */
    .messages {
      display: flex;
      flex-direction: column;
      .message {
        display: flex;
        margin-bottom: 10px;
        .message-content {
          padding: 10px;
          // max-width: 80%;
          line-height: 20px;
          word-break: break-all;
          background-color: #fff;
          border: 1px solid #eee;
          border-radius: 10px;
        }
        .user {
          border-bottom-right-radius: 0;
        }
        .assistant {
          border-bottom-left-radius: 0;
        }
      }
    }
  }
  .chat-box {
    position: relative;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    width: 100%;
    height: auto;
    padding: 0 10px;
    background-color: #fff;
    border-radius: 6px;
    box-shadow: 0 0 0 2px rgba(5, 145, 255, 0.1);

    :deep(.custom-textarea) {
      position: relative;
      box-sizing: border-box;
      height: auto;
      padding: 10px;
      font-family: PingFangSC, 'PingFang SC';
      font-size: 16px;
      font-weight: 400;
      background: rgb(255 255 255 / 20%);
      border-radius: 6px;
      overflow: hidden; /* 隐藏滚动条 */
      resize: none; /* 禁止用户调整大小 */

      /* 隐藏滚动条 */
      &::-webkit-scrollbar {
        display: none;
      }

      /* 占位符样式 */
      &::placeholder {
        color: #cccccc;
      }
    }
    .send-icon,
    .send-disabled-icon {
      width: 32px;
      height: 32px;
      cursor: pointer;
    }
    .send-disabled-icon {
      cursor: not-allowed;
    }
  }

  :deep(.ant-input) {
    border: none;
    &:focus {
      box-shadow: none !important;
    }
  }
</style>
