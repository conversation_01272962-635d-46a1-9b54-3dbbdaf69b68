<script setup lang="ts">
  import { nextTick, reactive, ref, onMounted, computed, onUnmounted, watch } from 'vue';
  import {
    HistoryOutlined,
    SettingOutlined,
    SwapOutlined,
    CopyOutlined,
    RedoOutlined,
    EditOutlined,
    CloseOutlined,
    PlusOutlined,
  } from '@ant-design/icons-vue';
  import { message } from 'ant-design-vue';
  import CustomTextarea from '@/views/Exploration/Components/textarea.vue';
  import Service from '@/views/Exploration/Components/service.vue';
  import ServiceSetting from '@/views/Exploration/Components/serviceSetting.vue';
  import History from '@/views/Exploration/Components/history.vue';
  import { copyText, setLocalItem, getLocalItem } from '@/utils/common';
  import {
    sendMessage,
    updateDialogTitle,
    fetchDialogList,
    fetchHistoryList,
    fetchServiceListByModelId,
  } from '@/api/exploration';
  import { useRoute } from 'vue-router';
  import { v4 as uuid } from 'uuid';
  import type {
    IServiceItem,
    ISendMessageProps,
    IServices,
    ISendMessageResponse,
    IDialogList,
  } from '@/interface/exploration';

  const route = useRoute();
  const listRef = ref<HTMLDivElement[] | null>(null);
  const textareaRef = ref();
  const settingRef = ref();
  const serviceRef = ref();
  const titleRef = ref();
  const typingInterval = ref();
  const changeDialogState = reactive<{ title: string; visible: boolean }>({
    title: '',
    visible: false,
  });
  const spinning = ref(false);
  interface IState {
    open: boolean;
    serviceVisible: boolean;
    historyVisible: boolean;
    dialogTitleVisible: boolean;
    serviceType: 'change' | 'add' | undefined;
    selectIndex: number;
  }
  const state = reactive<IState>({
    open: false,
    serviceVisible: false,
    historyVisible: false,
    dialogTitleVisible: false,
    serviceType: 'add',
    selectIndex: -1,
  });

  const services = reactive<IServices[]>([]);
  const currentService = reactive<IServiceItem>({
    id: '',
    service_name: '',
    model_category: '',
  });
  const dialogTitle = ref<string>('');
  const disabled = computed(() => (services.length ? !services[0].id : true));

  const handleAddModel = () => {
    state.serviceVisible = true;
    state.serviceType = 'add';
  };

  // 获取服务列表
  const fetchServiveListReq = async () => {
    const { modelId, serviceId, service_name, model_category } = route.query;
    if (modelId || serviceId) {
      // 有模型id 根据模型获取预设服务，默认选中第一个
      if (modelId) {
        spinning.value = true;
        const data: IServiceItem[] = await fetchServiceListByModelId(route.query.modelId as string);
        if (data && data.length) {
          services.push({
            ...data[0],
            uuid: uuid().replace(/-/g, ''),
            messageList: [],
          });
          Object.assign(currentService, data[0]);
        }
        spinning.value = false;
      } else {
        // 由模型部署页面点击去体验进去 没有模型id
        services.push({
          id: serviceId as string,
          service_name: service_name as string,
          model_category: model_category as string,
          uuid: uuid().replace(/-/g, ''),
          messageList: [],
        });
        Object.assign(currentService, { id: serviceId, service_name });
      }
    }
    if (services.length) {
      setLocalItem('SERVICES', JSON.stringify(services));
    }
  };

  // 重亲提问
  const handleRedo = (n: IServices, index: number) => {
    const question = n.messageList[index - 1].content;
    const src = n.messageList[index - 1].image || '';
    textareaRef.value.changeState('start', true);
    const content = src
      ? [
          { type: 'image_url', image_url: { url: src } },
          { type: 'text', text: question },
        ]
      : [{ type: 'text', text: question }];
    handleSend(content);
  };

  const handleSend = async (value: unknown) => {
    textareaRef.value.changeState('loading', false);
    textareaRef.value.changeState('start', true);
    // 重置编辑问题的输入框
    if (state.selectIndex > -1) {
      state.selectIndex = -1;
    }
    services.forEach((service) => {
      service.messageList.push({
        // @ts-expect-error
        content: value.find((item) => item.type === 'text').text,
        role: 'user',
        image:
          // @ts-expect-error
          value.find((item) => item.type === 'image_url')
            ? // @ts-expect-error
              value.find((item) => item.type === 'image_url')!.image_url.url
            : '',
      });
      service.messageList.push({
        content: '',
        role: 'assistant',
      });
      service.finished = false;
    });

    // 滚动到最底端
    await nextTick(() => {
      if (listRef.value) {
        listRef.value.forEach((container) => {
          container.scrollIntoView({ behavior: 'auto', block: 'end' });
        });
      }
    });
    // 更新会话标题
    if (!dialogTitle.value) {
      // 截取前20个字符作为会话标题
      //@ts-expect-error
      const title = value.find((item) => item.type === 'text').text;
      dialogTitle.value = title.length > 20 ? title.slice(0, 20) : title;
    }
    const promiseList: Promise<unknown>[] = [];
    services.forEach((service) => {
      const params: ISendMessageProps = {
        deploy_id: service.id,
        dialog_id: service.uuid,
        headers: {},
        body: {
          messages: [
            {
              content: value,
              role: 'user',
            },
          ],
          ...settingRef.value.formState,
          n: 1,
          stream: false,
        },
      };
      if (settingRef.value.prompt) {
        params.body.messages.unshift({
          content: settingRef.value.prompt,
          role: 'system',
        });
      }
      const promise = new Promise(() => {
        sendMessage(params)
          .then((res: ISendMessageResponse) => {
            const { response, dialog_id } = res;
            const currentDialog: IServices = services.find((item) => item.uuid === dialog_id) as IServices;
            if (response) {
              const { choices } = response;
              const answer = choices[0].message.content;
              let index = 0;
              function typeEffect() {
                // 已停止不走这段逻辑
                if (textareaRef.value.state.start) {
                  if (index < answer.length) {
                    // 将当前字符添加到元素中
                    currentDialog.messageList[currentDialog.messageList.length - 1].content += answer[index];
                    index++;
                    // 设置下一次调用的时间间隔
                    typingInterval.value = setTimeout(typeEffect, 50); // 每100毫秒打一个字
                  } else {
                    service.finished = true;
                  }
                }
              }
              typeEffect();
            } else {
              currentDialog.messageList[currentDialog.messageList.length - 1].content = '服务繁忙，请稍后再试。。。';
              service.finished = true;
            }
          })
          .catch(() => {
            const currentDialog: IServices = services.find((item) => item.uuid === service.uuid) as IServices;
            currentDialog.messageList[currentDialog.messageList.length - 1].content = '服务繁忙，请稍后再试。。。';
            service.finished = true;
          });
      });
      promiseList.push(promise);
    });
  };

  const handleStop = () => {
    clearTimeout(typingInterval.value);
    typingInterval.value = null;
    textareaRef.value.changeState('start', false);
    services.forEach((service) => {
      const assistants = service.messageList.filter((item) => item.role === 'assistant');
      if (!assistants[assistants.length - 1].content) {
        assistants[assistants.length - 1].content = '已停止';
      }
    });
    setLocalItem('SERVICES', JSON.stringify(services));
  };
  const handleChangeService = (data: { service: IServiceItem; type: 'add' | 'change' }) => {
    const { service, type } = data;
    if (type === 'change') {
      Object.assign(services[0], service);
    } else {
      services.push({
        ...service,
        uuid: uuid().replace(/-/g, ''),
        messageList: [],
      });
    }
    setLocalItem('SERVICES', JSON.stringify(services));
    state.serviceVisible = false;
    serviceRef.value.clearState();
  };
  const hendleAddNewDialog = (record: IServices) => {
    const { id, service_name, model_category } = record;
    const index = services.findIndex((item) => item.id === id);
    const newDialog = {
      id,
      service_name,
      model_category,
      uuid: uuid().replace(/-/g, ''),
      messageList: [],
    };
    services.splice(index, 1, newDialog);
    setLocalItem('SERVICES', JSON.stringify(services));
  };
  const hendleOpenHistory = (record: IServices) => {
    const { id, service_name } = record;
    Object.assign(currentService, { id, service_name });
    state.historyVisible = true;
  };
  const hendleOpenSetting = (record: IServices) => {
    const { id, service_name } = record;
    Object.assign(currentService, { id, service_name });
    state.open = true;
  };

  const handleChangeDialogTitle = async () => {
    changeDialogState.visible = true;
    changeDialogState.title = dialogTitle.value;
    await nextTick(() => {
      if (titleRef.value) {
        titleRef.value.focus();
      }
    });
  };
  const confirmChangeDialogTitle = async (e: Event) => {
    const target = e.target as HTMLInputElement;
    if (target.value.length > 20) {
      return message.warn('长度过长');
    }
    if (target.value === dialogTitle.value) {
      changeDialogState.visible = false;
      return;
    }
    const { uuid } = services[0];
    await updateDialogTitle({
      dialog_id: uuid,
      title: target.value,
    });
    message.success('修改成功');
    Object.assign(changeDialogState, { title: '', visible: false });
    dialogTitle.value = target.value;
  };
  const handleSelectHistory = async (id: string, title: string) => {
    const data: IDialogList = await fetchDialogList(id);
    dialogTitle.value = title;
    const contents = data.map((item) => item.contents);
    const messages: { role: string; content: string }[] = [];
    contents.forEach((item) => {
      item.forEach((i) => {
        if (['user', 'assistant'].includes(i.role)) {
          const { role, content } = i;
          messages.push({ role, content });
        }
      });
    });
    const service = services.find((item) => item.id === currentService.id) as IServices;
    // 更新对话上下文
    service.messageList.length = 0;
    service.messageList.push(...messages);
    // 更新对话id
    service.uuid = data[0].dialog_id;
    state.historyVisible = false;
    setLocalItem('SERVICES', JSON.stringify(services));
    await nextTick(() => {
      if (listRef.value) {
        const container = listRef.value[0];
        container.scrollIntoView({ behavior: 'auto', block: 'end' });
      }
    });
  };
  onMounted(async () => {
    if (getLocalItem('SERVICES')) {
      Object.assign(currentService, JSON.parse(getLocalItem('SERVICES'))[0]);
      JSON.parse(getLocalItem('SERVICES')).forEach(
        async (
          service: IServiceItem & { uuid: string; messageList: { role: string; content: string }[] },
          index: number,
        ) => {
          const data: { dialog_id: string; title: string }[] = await fetchHistoryList(service.id);
          if (data.length) {
            // 去第一个对比服务的title作为会话标题
            if (index === 0) {
              const dialog = data.find((item) => item.dialog_id === service.uuid);
              dialogTitle.value = dialog ? dialog.title : '';
            }
            const dialogId = data.find((item) => item.dialog_id === service.uuid)?.dialog_id as string;
            // 如果能找到会话id，回显当前会话的上下文
            if (dialogId) {
              const history: IDialogList = await fetchDialogList(dialogId);
              const contents = history.map((item) => item.contents);
              const messages: { role: string; content: string }[] = [];
              contents.forEach((item) => {
                item.forEach((i) => {
                  if (['user', 'assistant'].includes(i.role)) {
                    const { role, content } = i;
                    messages.push({ role, content });
                  }
                });
              });
              service.messageList.length = 0;
              service.messageList.push(...messages);
            }
          }
        },
      );
      services.length = 0;
      services.push(...JSON.parse(getLocalItem('SERVICES')));
    } else {
      fetchServiveListReq();
    }
  });

  onUnmounted(() => {
    setLocalItem('SERVICES', '');
  });

  watch(
    () => services,
    () => {
      if (services.every((service) => service.finished) && textareaRef.value) {
        textareaRef.value.changeState('start', false);
      }
    },
    { deep: true, immediate: true },
  );
</script>

<template>
  <div class="h-100%">
    <a-spin :spinning="spinning">
      <div class="header">
        <div>
          <a-input
            v-if="changeDialogState.visible"
            ref="titleRef"
            v-model:value="changeDialogState.title"
            style="width: 300px"
            @blur="confirmChangeDialogTitle"
          />

          <div v-else class="dialog-title" @dblclick="handleChangeDialogTitle">
            <a-tooltip v-if="dialogTitle && dialogTitle.length > 16">
              <template #title>{{ dialogTitle }}</template>
              <span>{{ `${dialogTitle.slice(0, 16)}...` }}</span>
            </a-tooltip>
            <span v-else>{{ dialogTitle }}</span>
          </div>
        </div>
        <a-button type="primary" :disabled="services.length > 1" @click="handleAddModel"
          >添加对比模型 {{ `${services.length}/2` }}</a-button
        >
      </div>
      <div
        class="wrapper flex flex-justify-center"
        :style="{
          flex: 1,
          width: services.length > 1 ? '100%' : '70%',
          height: `calc(100% - 205px)`,
        }"
      >
        <div
          class="exploration flex overflow-scroll"
          :style="{
            flex: 1,
            justifyContent: services.length > 1 ? 'space-between' : 'center',
          }"
        >
          <div
            v-for="(service, index) in services"
            :key="service.id"
            class="exploration-wrapper min-w-450px"
            :style="{
              flex: 1,
              width: services.length > 1 ? `calc(50% - 10px)` : '100%',
            }"
          >
            <div class="exploration-header">
              <div>{{ service.service_name }}</div>
              <div class="h-32px min-w-230px flex flex-justify-end">
                <a-tooltip>
                  <template #title>新对话</template>
                  <a-button type="text" :disabled="disabled" @click="hendleAddNewDialog(service)">
                    <PlusOutlined />
                  </a-button>
                </a-tooltip>
                <a-tooltip>
                  <template #title>历史会话</template>
                  <a-button type="text" :disabled="disabled" @click="hendleOpenHistory(service)">
                    <HistoryOutlined />
                  </a-button>
                </a-tooltip>
                <a-tooltip>
                  <template #title>模型设置</template>
                  <a-button type="text" :disabled="disabled" @click="hendleOpenSetting(service)">
                    <SettingOutlined />
                  </a-button>
                </a-tooltip>
                <a-tooltip>
                  <template #title>切换模型</template>
                  <a-button
                    type="text"
                    @click="
                      state.serviceVisible = true;
                      state.serviceType = 'change';
                    "
                  >
                    <SwapOutlined />
                  </a-button>
                </a-tooltip>
                <a-tooltip>
                  <template #title>关闭模型</template>
                  <a-button v-if="services.length > 1" type="text" @click="services.splice(index, 1)">
                    <CloseOutlined />
                  </a-button>
                </a-tooltip>
              </div>
            </div>
            <div class="exploration-content overflow-scroll p-x-10px bg-#f6f6f6">
              <div ref="listRef">
                <div
                  v-for="(n, i) in service.messageList"
                  :key="i"
                  class="dialogue-item"
                  :style="{ justifyContent: n.role === 'user' ? 'flex-end' : 'start' }"
                >
                  <div v-if="state.selectIndex === i" class="flex mix-h-140px w-100%">
                    <CloseOutlined style="margin: 0 10px" @click="state.selectIndex = -1" />
                    <CustomTextarea
                      :disabled="disabled"
                      :defaultValue="n"
                      :category="currentService.model_category"
                      @send="handleSend"
                      @stop="handleStop"
                    />
                  </div>
                  <div v-else class="dialogue-content">
                    <div class="dialogue-text" :class="{ question: n.role === 'user' }">
                      <span v-if="n.content">
                        <div v-if="n.image" class="img-box" :style="{ backgroundImage: `url(${n.image})` }"></div>
                        {{ n.content }}
                      </span>
                      <a-spin v-else></a-spin>
                    </div>
                    <div class="dialogue-setting-show">
                      <div class="dialogue-setting-hover">
                        <div
                          class="dialogue-setting"
                          :style="{ justifyContent: n.role === 'user' ? 'flex-end' : 'start' }"
                        >
                          <CopyOutlined @click="copyText(n.content)" />
                          <template v-if="n.role === 'user'"> <EditOutlined @click="state.selectIndex = i" /></template>
                          <template v-else>
                            <RedoOutlined @click="handleRedo(service, i)" />
                            <!-- <LikeOutlined />
                          <DislikeOutlined /> -->
                          </template>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="exploration-input mix-h-140px w-70%">
        <CustomTextarea
          ref="textareaRef"
          :disabled="disabled"
          :category="currentService.model_category"
          @send="handleSend"
          @stop="handleStop"
        />
      </div>
    </a-spin>
  </div>

  <History
    :id="currentService.id"
    :visible="state.historyVisible"
    @select="handleSelectHistory"
    @cancel="state.historyVisible = false"
  />
  <ServiceSetting
    :id="currentService.id"
    ref="settingRef"
    :visible="state.open"
    :service-name="currentService.service_name"
    @cancel="state.open = false"
  />
  <Service
    ref="serviceRef"
    :ids="services.map((item) => item.id)"
    :visible="state.serviceVisible"
    :type="state.serviceType"
    @cancel="state.serviceVisible = false"
    @ok="handleChangeService"
  ></Service>
</template>

<style lang="less" scoped>
  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    margin-bottom: 10px;
    // width: 0%;
    // border: 1px solid;
    .dialog-title {
      font-size: 18px;
      font-weight: bold;
      cursor: pointer;
    }
  }

  .wrapper {
    // height: calc(100% - 42px);
    flex: 1;
  }

  .exploration {
    width: 100%;
    height: 100%;

    .exploration-wrapper {
      display: flex;
      flex-direction: column;
      width: 100%;
      height: 100%;
      margin-right: 20px;

      &:last-child {
        margin-right: 0;
      }

      .exploration-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 44px;
        padding: 5px 10px;
        cursor: pointer;
        background-color: rgb(204 204 204 / 50%);
        border: 1px solid rgb(204 204 204 / 20%);
        border-top-left-radius: 10px;
        border-top-right-radius: 10px;

        > div:nth-child(2) {
          display: flex;
        }
      }

      .exploration-content {
        flex: 1;
        margin-bottom: 15px;
        // margin-bottom: 40px;
        cursor: pointer;
        border-bottom-right-radius: 10px;
        border-bottom-left-radius: 10px;

        .dialogue-item {
          position: relative;
          box-sizing: border-box;
          display: flex;
          width: 100%;
          margin: 15px 0;
          font-size: 16px;
          line-height: 28px;
          color: #262626;
          text-align: left;
          white-space: pre-wrap;

          &:hover {
            .dialogue-content {
              .dialogue-setting-show {
                .dialogue-setting-hover {
                  display: block;
                }
              }
            }
          }

          .dialogue-content {
            display: flex;
            flex-direction: column;

            .dialogue-text {
              padding: calc((44px - 28px) / 2) 20px;
              background-color: #e9ecf0;
              border-radius: 14px;
            }

            .dialogue-setting-show {
              height: 32px;

              .dialogue-setting-hover {
                display: none;

                .dialogue-setting {
                  display: flex;
                  // display: none;
                  padding: 10px 5px;

                  > span {
                    margin: 0 5px;
                    cursor: pointer;
                  }
                }
              }
            }

            .question {
              overflow: hidden;
              background-color: #eff6ff;
            }
          }
        }
      }
    }

    .exploration-input {
      width: 70%;
    }

    // .right {
    //   display: flex;
    //   // flex: 1;
    //   flex-direction: column;
    //   align-items: center;
    //   width: 100%;
    //   height: 100%;
    // }
  }

  :deep(.right-input) {
    position: absolute !important;
    top: -35px !important;
    right: 0 !important;
  }

  :deep(.ant-spin-nested-loading) {
    width: 100%;
    height: 100%;
  }

  :deep(.ant-spin-container) {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    height: 100%;
  }

  :deep(.ant-spin) {
    width: 100%;
    height: 100%;
  }
  .img-box {
    position: relative;
    width: 100px;
    height: 100px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;

    .closeIcon {
      position: absolute;
      top: 0;
      right: 0;
      cursor: pointer;
    }
  }
</style>
