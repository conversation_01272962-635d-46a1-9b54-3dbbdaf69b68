<script setup lang="ts">
  import { DownOutlined, UpOutlined } from '@ant-design/icons-vue';
  import { ref, computed, watch } from 'vue';
  import { useAnswer } from '@/stores/anster';
  import { message } from 'ant-design-vue';

  interface IProps {
    list: any[];
    tipLength: number;
    projectData: any[];
  }

  const emits = defineEmits<{
    (event: 'complete'): void;
    (event: 'answer-wrong'): void;
    (event: 'answer-right'): void;
    (event: 'next'): void;
    (event: 'scroll', id: string): void;
  }>();

  const props = defineProps<IProps>();

  const { state, jumpAnswer, pushAnswerQuestion, nextAnswer, setInPreBack } = useAnswer();

  // 当前题目下标
  const currentIndex = computed(() => state.currentIndex);

  const currentAnswer = computed(() => props.list[currentIndex.value]);

  const expand = ref(false);

  // 是已作答的题目
  const isFinishAnswer = computed(() => state.questionList.some((it) => it.id === currentAnswer.value.id));

  const imgUrl = computed(() => props.projectData.find((it) => it.id === currentAnswer.value.image_info_id).image_url);

  function handleSelectAnswer(item: any) {
    const id = currentAnswer.value.id;
    if (isFinishAnswer.value) return;
    console.log(111111111, item);
    const isRight = currentAnswer.value.true_answer === item.key;
    if (isRight) {
      emits('answer-right');
    } else {
      emits('answer-wrong');
    }
    pushAnswerQuestion({
      id,
      rightAnswer: currentAnswer.value.true_answer,
      selectAnswer: item.key,
      isRight,
    });
  }

  function handleNext() {
    if (state.questionList[currentIndex.value]?.id) {
      nextAnswer();
      emits('next');
    }
  }

  function handleContinue() {
    if (state.questionList.length < state.allQuestionList.length) {
      jumpAnswer(state.questionList.length);
      setInPreBack(false);
    }
  }

  function handleComplete() {
    emits('complete');
  }

  function handleExpand() {
    if (!state.questionList[state.currentIndex]?.id) return message.warning('请先作答');
    const flag = expand.value;
    expand.value = !expand.value;
    setTimeout(() => {
      if (!flag) {
        emits('scroll', 'ppt-image');
      }
    }, 100);
  }

  watch(
    () => state.currentIndex,
    () => {
      expand.value = false;
    },
  );
</script>

<template>
  <div class="w-full h-full in-test">
    <h2>课堂测验</h2>
    <div class="que-body">
      <div class="que-title flex items-center">
        <div class="mark mr-2">
          <span>{{ currentIndex + 1 }}</span>
          <span class="text-4">/</span>
          <span class="text-4 text-#636466">{{ props.list.length }}</span>
        </div>
        <div>{{ currentAnswer.question }}</div>
      </div>
      <div class="que-options">
        <div
          v-for="(item, idx) in Object.entries(currentAnswer.answer || {}).map(([key, value]) => ({ key, value }))"
          :key="idx"
          class="item"
          :class="{
            'mt-6': idx > 0,
            correct: isFinishAnswer ? item.key === state.questionList[currentIndex].rightAnswer : false,
            error: isFinishAnswer
              ? !state.questionList[currentIndex].isRight &&
                item.key !== currentAnswer.true_answer &&
                state.questionList[currentIndex].selectAnswer === item.key
              : false,
          }"
          @click="handleSelectAnswer(item)"
        >
          {{ item.key }}：{{ item.value }}
        </div>
        <!-- <div class="item mt-6 correct">B. 蒋介石和李宗仁</div>
        <div class="item mt-6 error">C. 毛泽东和周恩来</div>
        <div class="item mt-6">D. 阎锡山和冯玉祥</div> -->
      </div>
      <div v-if="state.questionList.some((it) => it.id === currentAnswer.id)" class="mt-12 flex justify-between">
        <a-button
          v-if="props.list.length === state.questionList.length"
          type="primary"
          class="h-10"
          @click="handleComplete"
          >完成</a-button
        >
        <a-button v-else-if="state.inPreBack" type="primary" class="h-10" @click="handleContinue">继续答题</a-button>
        <a-button v-else type="primary" class="h-10" @click="handleNext">下一题</a-button>
        <div>
          <span
            style="cursor: pointer; font-size: 14px; line-height: 40px; user-select: none"
            class="txt"
            @click="handleExpand"
          >
            {{ expand ? '收起' : '查看' }}PPT
            <UpOutlined v-if="expand" />
            <DownOutlined v-else />
          </span>
        </div>
      </div>
      <div v-if="expand" class="ppt-image">
        <img class="w-full my-10" style="border-radius: 8px" :src="imgUrl" />
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
  .in-test {
    display: flex;
    flex-direction: column;
    padding: 0 120px;
    h2 {
      text-align: center;
      font-size: 24px;
      margin: 50px 0;
      font-weight: 600;
      color: #18191a;
    }
    .que-body {
      .que-title {
        font-size: 20px;
        font-weight: 600;
        color: #18191a;
        line-height: 30px;
        margin-bottom: 24px;
      }
      .que-options {
        .item {
          height: 48px;
          background: #ffffff;
          border-radius: 6px;
          border: 1px solid #e6e6e6;
          padding-left: 16px;
          display: flex;
          align-items: center;
          position: relative;
          cursor: pointer;
          &:hover {
            border: 1px solid #1777ff;
          }
          &.correct {
            background: #e5ffd8;
            border: 1px solid #4ecc10;
            &::after {
              content: '';
              position: absolute;
              width: 20px;
              height: 18px;
              right: 16px;
              top: 50%;
              transform: translateY(-50%);
              background: url('@/assets/image/base/answer/correct.webp');
              background-size: cover;
            }
          }
          &.error {
            background: #ffe4e4;
            border: 1px solid #ff3c3c;
            &::after {
              content: '';
              position: absolute;
              width: 18px;
              height: 19px;
              right: 16px;
              top: 50%;
              transform: translateY(-50%);
              background: url('@/assets/image/base/answer/error.webp');
              background-size: cover;
            }
          }
        }
      }
      .txt {
        &:hover {
          color: #1777ff;
        }
      }
    }
  }
</style>
