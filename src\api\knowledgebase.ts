import type { IFetchKnowledgedbList, ICreateKnowledgedb, IFiles } from '@/interface/knowledge';
import request from '@/utils/request';

export const fetchKnowledgedbList = (data: IFetchKnowledgedbList) => {
  return request.$Axios.get(`/knowledge_db/list`, data);
}
// 创建知识库
export const createKnowledgedb = (data: ICreateKnowledgedb) => {
  return request.$Axios.post(`/knowledge_db/create`, data);
}
// 删除知识库
export const deleteKnowledgedb = (db_id: string) => {
  return request.$Axios.post(`/knowledge_db/delete/${db_id}`);
}
// 知识库文件列表
export const fetchFileList = (data: { db_id: string; name?: string; status?: string }) => {
  const { db_id, name, status } = data
  return request.$Axios.get(`/knowledge_db/file/list/${db_id}`, { name, status });
}
// 知识库详情
export const fetchKnowledgedbDetail = (db_id: string) => {
  return request.$Axios.get(`/knowledge_db/detail/${db_id}`);
}
// 编辑知识库
export const editKnowledgedb = (data: { db_id: string; name: string; description?: string }) => {
  const { db_id, name, description } = data
  return request.$Axios.post(`/knowledge_db/update/${db_id}`, { name, description });
}
// 导入文件
export const addKnowledgeFiles = (data: { db_id: string; files: IFiles[] }) => {
  const { db_id, files } = data
  return request.$Axios.post(`/knowledge_db/add/file/${db_id}`, { files });
}
// 删除文件
export const delateKnowledgeFile = (file_id: string) => {
  return request.$Axios.post(`/knowledge_db/file/delete/${file_id}`);
}
// 筛选文件
export const fetchKnowledgeFilterList = () => {
  return request.$Axios.get(`/knowledge_db/filter_list`);
}