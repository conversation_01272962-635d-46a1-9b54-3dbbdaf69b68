// test/setup.ts 或每个测试文件顶部配置
import { config } from '@vue/test-utils';
// 手动注册 Ant Design Vue 全部组件
import * as antdComponents from 'ant-design-vue';

import { beforeAll, afterAll } from 'vitest';
import { spawn } from 'child_process';

// 批量注册 antd 所有组件
Object.entries(antdComponents).forEach(([key, value]) => {
  // console.log([key, value]);
  if (key !== 'default') {
    config.global.components[value.name] = value;
  }
});

let viteProcess: any;

beforeAll((done) => {
  viteProcess = spawn('npx', ['vite', '--port', '5173'], { shell: true });
  // window.history.pushState({}, 'Test page', 'http://localhost:5173/');
  window.location.href = 'http://localhost:5173/';
  // viteProcess.stdout.on('data', (data) => {
  //   if (data.toString().includes('ready')) {
  //     done();
  //   }
  // });
});

afterAll(() => {
  if (viteProcess) viteProcess.kill();
});
