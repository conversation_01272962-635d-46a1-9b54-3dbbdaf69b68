export const DeployStatus: { label: string; value: string; color: string }[] = [
  { label: '创建中', value: 'creating', color: 'orange' },
  { label: '运行中', value: 'running', color: 'green' },
  { label: '已停止', value: 'stopped', color: 'orange' },
  { label: '失败', value: 'failed', color: 'red' },
  { label: '创建失败', value: 'create_failed', color: 'red' },
  { label: '已删除', value: 'deleted', color: 'red' },
  { label: '启动失败', value: 'exited', color: 'red' },
  { label: '异常', value: 'abnormal', color: 'red' },
];

export const DEPLOY_STATE_MAP = {
  CREATING: 'creating', // 创建中
  RUNNING: 'running', // 运行中
  STOPPED: 'stopped', // 已停止
  FAILED: 'failed', // 失败
  CREATE_FAILED: 'create_failed', // 创建失败
  DELETED: 'deleted', // 已删除
  EXITED: 'exited', // 启动失败
  ABNORMAL: 'abnormal', // 异常
}

export const deployMethod = [
  { label: 'vLLM 加速部署', value: 'vllm', desc: 'vLLM 是一个业界流行的用于LLM推理加速的库。仅支持API推理，推理方式请参考模型介绍。' },
  {
    label: 'Transformers 标准部署',
    value: 'transformers',
    desc: '不使用任何推理加速的标准部署。支持API推理，推理方式请参考模型介绍。',
  },
  {
    label: 'SGLang 部署',
    value: 'sglang',
    desc: '专为大模型与多模态场景设计的高性能服务框架，聚焦低延迟、高并发部署，适用于实时对话与多模态应用。',
  },
];
