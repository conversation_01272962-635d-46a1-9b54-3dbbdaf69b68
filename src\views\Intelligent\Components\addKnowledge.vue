<script setup lang="ts">
  import { fetchKnowledgeFilterList } from '@/api/knowledgebase';
  import type { IKnowledgeFilterItem, IKnowledgedb } from '@/interface/knowledge';
  import { RedoOutlined } from '@ant-design/icons-vue';
  import { ref, reactive, onMounted, computed } from 'vue';
  import { useRouter } from 'vue-router';
  import { convertIsoTimeToLocalTime, debounce } from '@/utils/common';

  interface IProps {
    select: IKnowledgeFilterItem[];
  }
  const props = defineProps<IProps>();

  const router = useRouter();
  const DEFAULT_NAME = undefined;
  const name = ref(DEFAULT_NAME);
  const dataSource = reactive<IKnowledgeFilterItem[]>([]);
  // const pageParame: IPage = reactive({ page: 1, limit: 10 });
  // const hasMoreData = ref(true);
  const isLoading = ref(false);
  const loading = ref(false);
  const error = ref<Error | null>(null);
  const isFinished = ref(false);
  const containerRef = ref();
  const selected_knowledge_db = reactive<IKnowledgeFilterItem[]>(props.select);
  const handleAdd = () => {
    const href = `${location.origin}${location.pathname}#/knowledge/add`;
    window.open(href);
  };

  // const onSearch = async () => {
  //   loading.value = true;
  //   Object.assign(pageParame, { page: 1 });
  //   const data = await fetchData();
  //   // const { items } = data;
  //   dataSource.length = 0;
  //   dataSource.push(...data);
  //   loading.value = false;
  // };

  const fetchData = async () => {
    loading.value = true;
    const data: IKnowledgeFilterItem[] = await fetchKnowledgeFilterList();
    dataSource.length = 0;
    const res: IKnowledgeFilterItem[] = name.value ? data.filter((item) => item.name.includes(name.value!)) : data;
    dataSource.push(...res);
    loading.value = false;
  };
  const debouncedSearch = debounce(fetchData);
  // const loadMoreItems = async () => {
  //   if (!hasMoreData.value) {
  //     return;
  //   }
  //   loading.value = true;
  //   const data = await fetchData();
  //   // const { items, total } = data;
  //   dataSource.push(...items);
  //   if (dataSource.length >= total) {
  //     isFinished.value = true;
  //   }
  //   loading.value = false;
  // };

  // const handleFresh = async () => {
  //   loading.value = true;
  //   Object.assign(pageParame, { page: 1 });
  //   const data = await fetchData();
  //   const { items } = data;
  //   dataSource.length = 0;
  //   dataSource.push(...items);
  //   loading.value = false;
  // };
  // const handleScroll = async () => {
  //   if (isLoading.value || isFinished.value) return;
  //   const el = containerRef.value;

  //   if (
  //     containerRef.value.scrollHeight - containerRef.value.scrollTop - containerRef.value.clientHeight <= 10 &&
  //     hasMoreData.value
  //   ) {
  //     // 当滚动到页面底部附近时
  //     try {
  //       isLoading.value = true;
  //       await loadMoreItems();
  //     } catch (err) {
  //       error.value = err as Error;
  //     } finally {
  //       isLoading.value = false;
  //     }
  //   }
  // };

  const handleRemove = (record: IKnowledgeFilterItem) => {
    const index = selected_knowledge_db.findIndex((item) => record.id === item.id);
    selected_knowledge_db.splice(index, 1);
  };
  // onMounted(() => {
  //   loadMoreItems();
  //   if (containerRef.value) {
  //     containerRef.value.addEventListener('scroll', handleScroll);
  //   }

  //   onUnmounted(() => {
  //     if (containerRef.value) {
  //       containerRef.value.removeEventListener('scroll', handleScroll);
  //     }
  //   });
  //   // spinning.value = true;
  //   // // 初始加载更多内容
  //   // spinning.value = false;
  // });

  onMounted(() => {
    fetchData();
  });

  defineExpose({
    selected_knowledge_db,
  });
</script>

<template>
  <div class="flex flex-justify-end">
    <a-input-search
      v-model:value="name"
      placeholder="搜索知识库名称"
      allowClear
      style="width: 400px"
      @change="debouncedSearch"
      @blur="fetchData"
    ></a-input-search>
    <a-button
      @click="
        name = undefined;
        fetchData();
      "
      style="margin: 0 10px"
      ><RedoOutlined
    /></a-button>
    <a-button type="primary" @click="handleAdd">创建知识库</a-button>
  </div>
  <a-spin :spinning="loading">
    <div class="container overflow-scroll mt-10px" ref="containerRef">
      <div class="box">
        <div v-if="dataSource.length">
          <div v-for="item in dataSource" :key="item.id" class="knowledge-db flex justify-between items-center py-10px">
            <div class="flex flex-col">
              <div>{{ item.name }}</div>
              <a-popover>
                <template #content>
                  <p class="title-text">
                    {{
                      item.files.filter((item) => item.status === 'available').length
                        ? `${item.name} 可用文件`
                        : '暂无可用文件'
                    }}
                  </p>
                  <div
                    v-for="file in item.files.filter((item) => item.status === 'available')"
                    :key="file.id"
                    class="mb-5px"
                  >
                    {{ file.name }}
                  </div>
                </template>
                <a-tag color="green" style="margin: 5px 0; width: 40px"
                  >{{ item.files.filter((item) => item.status === 'available').length }}个</a-tag
                >
              </a-popover>
              <div class="text-#797979">创建时间：{{ convertIsoTimeToLocalTime(item.created_at) }}</div>
            </div>
            <div>
              <a-button
                v-if="!selected_knowledge_db.map((db) => db.id).includes(item.id)"
                @click="selected_knowledge_db.push(item)"
                >添加</a-button
              >
              <div v-else>
                <span class="text-#797979 mr-10px">已添加</span>
                <a-button danger @click="handleRemove(item)">移除</a-button>
              </div>
            </div>
          </div>
          <div class="flex justify-center mt-10px text-#797979 text-12px">
            <div v-if="isLoading" class="loading">加载中...</div>
            <div v-else-if="isFinished" class="finished">没有更多数据了</div>
            <div v-else-if="error" class="error">加载失败: {{ error.message }}</div>
          </div>
        </div>
        <div v-else class="empty">
          <a-empty
            :description="name === DEFAULT_NAME ? '你还没有创建知识库哦，快去创建吧' : '暂无相关知识库'"
          ></a-empty>
        </div>
      </div>
    </div>
  </a-spin>
</template>

<style scoped lang="less">
  .box {
    height: 400px;
    .knowledge-db {
      border-bottom: 1px solid #ddd;
    }
  }
  .empty {
    height: 400px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .title-text {
    font-weight: bold;
  }
</style>
