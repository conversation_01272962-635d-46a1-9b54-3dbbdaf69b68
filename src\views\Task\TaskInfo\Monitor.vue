<!--
 * @Author: deng<PERSON>sheng 
 * @Date: 2025-02-26 16:25:46
 * @LastEditTime: 2025-04-15 17:22:33
 * @LastEditors: <EMAIL>
 * @FilePath: \ai-platform-frontend\src\views\Task\TaskInfo\Monitor.vue
 * @Description: 任务监控
-->
<script setup lang="ts">
  import dayjs from 'dayjs';
  import type { Dayjs } from 'dayjs';
  import { init, type EChartsType } from 'echarts';
  import { CustomForm } from '@/components';
  import { onMounted, reactive, ref } from 'vue';
  import { getSystemResource } from '@/api/model';
  import { useRoute } from 'vue-router';
  import type { IFormItem } from '@/components/CustomForm/index.vue';

  const props = withDefaults(defineProps<{ created: string; runTime: number; status: string }>(), {
    created: dayjs(new Date()).format('YYYY-MM-DD') + ' 00:00:00',
    runTime: 0,
    status: '',
  });

  const route = useRoute();
  const echarts = ref<HTMLDivElement[]>([]);
  const echartsInstance: EChartsType[] = [];
  const state = reactive({
    options: [
      {
        titleText: 'CPU 使用率',
        yAxisUnit: '%',
        seriesKey: 'cpu_percent',
      },
      {
        titleText: 'Memory 使用率',
        yAxisUnit: '%',
        seriesKey: 'memory_percent',
      },
      {
        titleText: '磁盘写入速率',
        yAxisUnit: 'MiB/s',
        seriesKey: '',
      },
      {
        titleText: '磁盘读取速率',
        yAxisUnit: 'MiB/s',
        seriesKey: '',
      },
      {
        titleText: 'GPU 使用率',
        yAxisUnit: '%',
        seriesKey: '',
      },
      {
        titleText: 'GPU Memory 使用率',
        yAxisUnit: '%',
        seriesKey: '',
      },
    ],
    search: '',
    timeFilter: '', //0 按分钟 / 1 按小时 / 2 按日
    datas: [],
    isEmpty: false,
    oldlen: 0,
    customFormValue: {
      timeFilter: 0,
      search: [
        dayjs(props.created),
        props.status == 'running' ? dayjs(new Date()) : dayjs(props.created).add(props.runTime, 'second'),
      ],
    },
  });

  const formConfig: IFormItem[] = [
    {
      field: 'timeFilter',
      type: 'select',
      label: '时间精度',
      placeholder: '请输入关键字',
      options: [
        { label: '按日', value: 2 },
        { label: '按时', value: 1 },
        { label: '按分钟', value: 0 },
      ],
    },
    {
      field: 'search',
      type: 'dateRange',
      label: '时间段',
      placeholder: ['请选择', '请选择'],
      options: [
        { label: '按日', value: 'd' },
        { label: '按时', value: 'h' },
        { label: '按分钟', value: 'm' },
      ],
    },
  ];

  const getInfoList = async (start_time: string, end_time: string, time_unit: number = 0) => {
    const { taskid } = route.query;

    if (state.datas.length) state.oldlen = state.datas.length;

    const list = await getSystemResource({ id: String(taskid), start_time, end_time, time_unit });
    // const list = []
    if (list) state.datas = list;
    state.isEmpty = state.oldlen == 0 && !state.datas.length;

    if (echartsInstance.length) {
      const len = state.datas.length;
      echartsInstance.map((e: EChartsType, i: number) => {
        const option = {};
        if (len) {
          // @ts-expect-error
          option.series = {
            data: state.datas.map((d) => {
              const { seriesKey, yAxisUnit } = state.options[i];
              if (seriesKey) return yAxisUnit == '%' && d[seriesKey] > 100 ? 100 : d[seriesKey];
              else return 0;
            }),
            type: 'line',
          };
          // @ts-expect-error
          option.xAxis = {
            type: 'category',
            // @ts-expect-error
            data: state.datas.map((e) => e.record_time),
          };
        } else {
          // @ts-expect-error
          option.series = {
            data: Array.from(Array(state.oldlen), (_) => 0),
            type: 'line',
          };
        }
        e.setOption(option);
        e.resize();
      });
    } else if (!state.isEmpty) initechart();
  };

  onMounted(async () => {
    const { search, timeFilter } = state.customFormValue;
    await getInfoList(
      search[0].format('YYYY-MM-DD') + ' 00:00:00',
      search[1].format('YYYY-MM-DD') + ' 23:59:59',
      timeFilter,
    );
    if (!state.datas.length) state.isEmpty = true;
    if (echarts.value && !state.isEmpty) {
      initechart();
    }
  });

  window.addEventListener('resize', () => {
    if (echartsInstance) echartsInstance.map((e) => e?.resize());
  });

  const initechart = () => {
    echarts.value.map((e, i: number) => {
      const { titleText, seriesKey, yAxisUnit } = state.options[i];
      const echartInstance = init(e);
      echartInstance.setOption({
        xAxis: {
          type: 'category',
          // data: Array.from(Array(5), (_, i) => dayjs(new Date()).format('YYYY-MM-DD hh:mm:ss'))
          // @ts-expect-error
          data: state.datas.map((e) => e.record_time),
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            formatter: (value: string) => `${value} ${yAxisUnit}`,
          },
        },
        tooltip: {
          show: true,
          trigger: 'axis', // 或 'item'
          // @ts-expect-error
          formatter: (params) => {
            let content = '';
            params.forEach((item: { name: string; data: string }) => {
              content += `<div>时间: ${item.name}<br/>值: ${item.data} ${yAxisUnit}</div>`;
            });
            return content;
          },
        },
        title: {
          text: titleText,
          textStyle: {},
          textAlign: 'left',
        },
        series: [
          {
            data: state.datas.map((d) => {
              if (seriesKey) return yAxisUnit == '%' && d[seriesKey] > 100 ? 100 : d[seriesKey];
              else return 0;
            }),
            type: 'line',
          },
        ],
      });
      echartsInstance.push(echartInstance);
    });
  };

  const onFinish = async (value: { search: Dayjs[]; timeFilter: number }) => {
    const { search, timeFilter } = value;
    if (search)
      await getInfoList(
        dayjs(search[0]).format('YYYY-MM-DD') + ' 00:00:00',
        dayjs(search[1]).format('YYYY-MM-DD') + ' 23:59:59',
        timeFilter || 0,
      );
  };

  // @ts-expect-error
  const selectChang = (e) => {
    state.customFormValue = {
      timeFilter: e,
      search: e
        ? [dayjs(new Date()).subtract(7, 'day'), dayjs(new Date())]
        : [
            dayjs(props.created),
            props.status == 'running' ? dayjs(new Date()) : dayjs(props.created).add(props.runTime, 'second'),
          ],
    };
  };

  // watch(() => getLocalItem('taskinfotab'), async (val: any) => {  // 无效
  //   const { search, timeFilter } = state.customFormValue
  //   if (val * 1 == 3) await getInfoList(search[0].format("YYYY-MM-DD") + " 00:00:00", search[1].format("YYYY-MM-DD") + " 23:59:59", timeFilter)
  // }, { deep: true, immediate: true })
</script>

<template>
  <div class="monitor">
    <CustomForm
      :form-items="formConfig"
      :universal="{ funName: ['change'], fun: [selectChang] }"
      :value="state.customFormValue"
      @on-finish="onFinish"
      @on-rest="onFinish(state.customFormValue)"
    />
    <div class="overflow-scroll">
      <div v-for="(n, i) in state.options.length" :key="i" ref="echarts">
        <a-empty v-if="state.isEmpty" style="margin-top: 15%" />
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
  .monitor {
    display: flex;
    flex-direction: column;
    height: 100%;

    > div:nth-child(1) {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 20px;
    }

    > div:nth-child(2) {
      display: flex;
      flex-wrap: wrap;

      > div {
        width: calc((100% - 20px) / 2);
        height: 350px;
        padding-top: 15px;
        // height: 550px;
        margin-bottom: 20px;
        border: 1px solid rgb(0 0 0 / 20%);
      }

      > div:nth-child(2n) {
        margin-left: 20px;
      }
    }
  }
</style>
