import type { IOptions } from "@/components/CustomForm/index.vue";

export const UPDATE_STRATEGY_MAP: { [key: string]: unknown; } = {
  "RollingUpdate": "滚动更新"
}
export const POD_STATUS_MAP: { label: string; value: string, color: string }[] = [
  { label: '准备中', value: 'Pending', color: '#d5d5d5' },
  { label: '运行中', value: 'Running', color: '#47d89e' },
  { label: '已成功', value: 'Succeeded', color: '#d5d5d5' },
  { label: '已失败', value: 'Failed', color: '#d5d5d5' },
  { label: '未知', value: 'Unknown', color: '#d5d5d5' },
]

export const POD_OPTIONS: IOptions[] = [
  { label: 'Pending', value: 'Pending' },
  { label: 'Running', value: 'Running' },
  { label: 'Succeeded', value: 'Succeeded' },
  { label: 'Failed', value: 'Failed' },
  { label: 'Unknown', value: 'Unknown' },
]