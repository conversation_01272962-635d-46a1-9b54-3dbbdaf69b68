<!--
 * @Author: dengfusheng 
 * @Date: 2025-06-04 09:16:03
 * @LastEditTime: 2025-06-26 13:53:31
 * @LastEditors: <EMAIL>
 * @FilePath: \ai-platform-frontend\src\views\Kubernetes\index.vue
 * @Description: 
-->
<script setup lang="ts">
  import { ref, reactive, onMounted, computed, watch, onUnmounted } from 'vue';
  import dayjs from 'dayjs';
  import { init } from 'echarts';
  import type { TableProps } from 'ant-design-vue';
  import paramList from './param.json';
  import { getDeploymentsList, getOpsmonitor, getPodsList, getServicesList } from '@/api/kubernetes';
  import type { anyObj } from '@/interface/kubernetes';
  import { useRoute, useRouter } from 'vue-router';
  import { Empty } from 'ant-design-vue';
  import { HistoryOutlined } from '@ant-design/icons-vue';

  const simpleImage = Empty.PRESENTED_IMAGE_SIMPLE;

  // echartsRef
  const survey = ref(null);
  const avggroup = ref(null);
  const health = ref(null);

  const route = useRoute();
  const router = useRouter();

  const echartsInstance: anyObj = Array.from(Array(14), () => null);

  const sortedInfo = ref(); // 表格排序

  const state = reactive<anyObj>({
    echartsList: { survey: [], avggroup: [] },
    selectGpu: ['all'],
    selectType: 'custom',
    model: [
      {
        name: '查看工作负载列表',
        sername: '负载类型',
        leftName: '工作负载总数',
        type: 'health',
        path: '/cluster-manage/workload',
      },
      {
        name: '查看容器组列表',
        sername: '容器状态',
        leftName: '容器组总数',
        type: 'health',
        path: '/cluster-manage/pod',
      },
      // {
      //   name: '查看服务列表',
      //   sername: '---',
      //   leftName: '服务与路由总数',
      //   type: 'health',
      //   path: '/cluster-manage/service',
      // },

      { name: 'GPU平均使用率', unit: '%', type: 'avg' },
      { name: '显存平均使用率', unit: '%', type: 'avg' },
      { name: '显存带宽平均使用率', unit: '%', type: 'avg' },
      { name: 'GPU平均温度', unit: '℃', type: 'avg' },
      { name: 'GPU平均功率', unit: 'W', type: 'avg', max: 500 },

      { name: 'GPU使用率', unit: '%', type: 'survey' },
      { name: '显存使用率', unit: '%', retain: 1, type: 'survey' },
      { name: '显存带宽使用率', unit: '%', type: 'survey' },
      { name: '显存使用量', unit: 'GIB', retain: 1, type: 'survey' },
      { name: 'GPU消耗功率', unit: 'W', type: 'survey' },
      { name: 'GPU温度', unit: '℃', type: 'survey' },
      { name: 'GPU的SM时钟频率', unit: 'GHz', retain: 2, type: 'survey' },
      { name: 'GPU内存时钟频率', unit: 'GHz', retain: 2, type: 'survey' },
    ],
    condition: { search: [dayjs().startOf('day'), dayjs()] },
    timing: 5 * 60,
    timer: null,
    gpus: [],
    echartsColor: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4'],
  });

  const selectTypeOptions: anyObj = [
    { label: '自定义', value: 'custom' },
    { label: '最近1小时', value: '1' },
    { label: '最近3小时', value: '3' },
    { label: '最近6小时', value: '6' },
    { label: '最近12小时', value: '12' },
    { label: '最近1天', value: '24' },
    { label: '最近2天', value: '48' },
    { label: '最近3天', value: '72' },
    { label: '最近1周', value: '168' },
    { label: '最近15天', value: '360' },
    { label: '最近30天', value: '720' },
  ];

  const selectGpuOptions: anyObj = computed(() => {
    // const modelObj = state.model.find((e: anyObj) => e.tableData && e.tableData.length);
    const results: anyObj = [
      { label: '全部', value: 'all', disabled: state.selectGpu?.some((g) => g.includes('GPU')) },
      ...state.gpus.map((g) => ({
        label: `GPU ${g}`,
        value: `GPU ${g}`,
        disabled: state.selectGpu.includes('all'),
      })),
    ];
    return results;
  });

  const sorter = (key: string) => (a: anyObj, b: anyObj) => {
    const sumA = [...a[key].toString()].reduce((sum, char) => sum + char.charCodeAt(0), 0);
    const sumB = [...b[key].toString()].reduce((sum, char) => sum + char.charCodeAt(0), 0);
    return sumA - sumB;
  };

  const columns = computed(() => (i: number) => {
    const { columnKey, dom_i, order } = sortedInfo.value || {};
    return [
      {
        title: 'Name',
        dataIndex: 'name',
        key: 'name',
        sorter: sorter('name'),
        sortOrder: columnKey === 'name' && dom_i == i && order,
        width: '25%',
      },
      {
        title: 'Mean',
        dataIndex: 'mean',
        key: 'mean',
        sorter: sorter('mean'),
        sortOrder: columnKey === 'mean' && dom_i == i && order,
        width: '18%',
        ellipsis: true,
      },
      {
        title: 'Max',
        dataIndex: 'max',
        key: 'max',
        sorter: sorter('max'),
        sortOrder: columnKey === 'max' && dom_i == i && order,
        width: '18%',
        ellipsis: true,
      },
      {
        title: 'Last',
        dataIndex: 'last',
        key: 'last',
        sorter: sorter('last'),
        sortOrder: columnKey === 'last' && dom_i == i && order,
        width: '19%',
        ellipsis: true,
      },
    ];
  });

  const formConfig: Array<anyObj> = [
    {
      field: 'search',
      type: 'dateRange',
      dateFormat: 3,
      label: '时间段',
      placeholder: ['请选择', '请选择'],
    },
  ];

  const onFinish = (rest: boolean = false) => {
    if (rest) state.condition.search = undefined;
    getMasterData(rest);
  };

  const selectTypeChange = () => {
    if (state.selectType == 'custom') {
      state.timer && clearInterval(state.timer);
    } else {
      state.condition.search = [dayjs().subtract(state.selectType * 1, 'hour'), dayjs()];
      getMasterData();
    }
  };

  const selectGpuChange = (e: boolean) => {
    if (state.selectGpu.length == 0) state.selectGpu = ['all'];
    if (e === false) getMasterData();
  };

  const getMasterData = async (rest: boolean = false) => {
    if (state.timer === null || state.timer) {
      state.timer && clearInterval(state.timer);
      state.timer = setInterval(() => {
        if (state.selectType != 'custom') {
          state.condition.search = [dayjs().subtract(state.selectType * 1, 'hour'), dayjs()];
          getMasterData();
        }
      }, state.timing * 1000);
    }

    const { search } = state.condition;
    let from: string, to: string;
    if (search) {
      from = dayjs(search[0]).valueOf() + '';
      to = dayjs(search[1]).valueOf() + '';
    } else {
      from = dayjs().startOf('day').valueOf() + '';
      to = dayjs().valueOf() + '';
    }

    const { data } = await getOpsmonitor({
      url: `https://opsmonitor.shukeyun.com/api/datasources/uid/deamf410un6rkd/resources/api/v1/label/gpu/values?start=${from.slice(0, 10)}&end=${to.slice(0, 10)}`,
      body: {},
      method: 'GET',
    });
    state.gpus = data;
    for (const m of state.model) {
      const { type, name, unit, retain } = m;

      if (type == 'health' && !rest) {
        let value: Array<anyObj> = [],
          total: number = 0;
        if (name == '查看工作负载列表') {
          const { items } = await getDeploymentsList({ page: 1, limit: 100 });
          value = Array.from(
            items
              .sort((a, b) => Number(b.key === 'Deployment') - Number(a.key === 'Deployment'))
              .reduce((map, item) => {
                // 1/1 replicas available
                const key = item.status.slice(0, 1) == 1;
                map.set(key, (map.get(key) || 0) + 1);
                return map;
              }, new Map()),
            ([key, count]) => ({ name: key ? '运行中' : '未运行', value: count }),
          );
          total = items.length;
        } else if (name == '查看容器组列表') {
          const { items } = await getPodsList({ page: 1, limit: 100 });
          value = Array.from(
            items
              .sort((a, b) => Number(b.key === 'Running') - Number(a.key === 'Running'))
              .reduce((map, item) => {
                const key = item.phase;
                map.set(key, (map.get(key) || 0) + 1);
                return map;
              }, new Map()),
            ([key, count]) => ({ name: key == 'Running' ? '运行中' : '未运行', value: count }),
          );
          total = items.length;
        } else if (name == '查看服务列表') {
          const { items } = await getServicesList({ page: 1, limit: 100 });
          value = Array.from(
            items.reduce((map, item) => {
              const key = item.type;
              map.set(key, (map.get(key) || 0) + 1);
              return map;
            }, new Map()),
            ([key, count]) => ({ name: key, value: count }),
          );
          total = items.length;
        }
        m.total = total;
        initechart(
          state.model.filter((f) => f.type == 'health').findIndex((n) => n == m),
          { value },
          type,
        );
        continue;
      }
      let params = JSON.parse(JSON.stringify(paramList)).find((e: anyObj) => e.key == name);
      if (!params) continue;

      params.from = from;
      params.to = to;
      let GPUS: anyObj = state.gpus;
      if (state.selectGpu.length && !state.selectGpu.includes('all'))
        GPUS = state.selectGpu.map((g: string) => g.slice(-1));
      params.queries.map((q: anyObj) => (q.expr = q.expr.replace(/gpu=~"\(([^)]+)\)"/g, `gpu=~"(${GPUS.join('|')})"`)));
      delete params.key;
      m.loading = true;
      getOpsmonitor({
        url: 'https://opsmonitor.shukeyun.com/api/ds/query?ds_type=prometheus',
        // url: name == 'GPU平均功率' ? '' : 'https://opsmonitor.shukeyun.com/api/ds/query?ds_type=prometheus',
        body: params,
        method: 'POST',
      })
        .then(
          ({
            results,
            results: {
              A: { frames },
            },
          }: anyObj) => {
            if (!(results && results?.A?.frames)) {
              m.loading = false;
              m.empty = true;
            }
            let tableData: Array<object> = [],
              avgVal: number = 0,
              xData: Array<object> = [],
              yData: Array<object> = [];
            frames.map((e) => {
              let {
                data: {
                  values: [x, y],
                },
                schema: {
                  fields: [
                    ,
                    {
                      config: { displayNameFromDS },
                    },
                  ],
                },
              } = e;

              if (type == 'survey') {
                if (name == '显存使用率') y = y.map((e) => e * 100);
                if (name == '显存使用量') y = y.map((e) => (e / 1073741824).toFixed(1) * 1);
                if (['GPU内存时钟频率', 'GPU的SM时钟频率'].includes(name))
                  y = y.map((e) => (e / 1000000000).toFixed(1) * 1);
                tableData.push({
                  key: name,
                  name: displayNameFromDS,
                  mean: (y.reduce((sum, num) => sum + num, 0) / y.length).toFixed(retain || 0) + unit,
                  max: Math.max(...y).toFixed(retain || 0) + unit,
                  last: y[y.length - 1].toFixed(retain || 0) + unit,
                });
                xData = x.map((e) => dayjs(e).format('YYYY-MM-DD HH:mm'));
                yData.push({ data: y, key: displayNameFromDS });
              } else {
                if (name == '显存平均使用率') y = y.map((e) => e * 100);
                avgVal = y.reduce((a, b) => a + b) / y.length;
              }
            });

            if (type == 'survey') {
              m.tableData = tableData;
              m.yData = yData;
            }
            initechart(
              state.model.filter((f) => f.type == type).findIndex((n) => n == m),
              type == 'avg' ? { value: avgVal.toFixed(0) * 1, ...m } : { xData, yData, ...m },
              type,
            );
          },
        )
        .catch(() => {
          m.loading = false;
          m.empty = true;
        });
    }
  };

  const debounce = (fn: any) => {
    fn();
    // let timer: number | undefined = undefined;
    // return (...args) => {
    //   clearTimeout(timer);
    //   timer = setTimeout(() => fn(...args), delay);
    // };
  };

  const handleChange: TableProps['onChange'] = (sorter: object, i: unknown) => {
    sortedInfo.value = { ...sorter, dom_i: i };
  };

  const initechart = (index: number, obj: object, type: string) => {
    if (type == 'avg' && avggroup.value && avggroup.value[index]) {
      let colorSection = [
        [0.5, '#73bf69'],
        [0.7, '#eab839'],
        [0.9, '#ff780a'],
        [1, '#f2495c'],
      ];
      const echartInstance = init(avggroup.value[index]);
      const { value, name, unit, max } = obj;
      echartInstance.setOption({
        title: {
          text: name,
          textStyle: {
            fontSize: '14px',
          },
          textAlign: 'left',
          padding: [15, 0, 0, 15],
        },
        series: [
          // 三色渐变背景（不显示数值）
          {
            type: 'gauge',
            center: ['50%', '60%'],
            startAngle: 200,
            endAngle: -20,
            min: 0,
            max: max || 100,
            splitNumber: 12,
            progress: {
              show: false, // 不显示进度条
            },
            pointer: {
              show: false, // 不显示指针
            },
            axisLine: {
              show: true,
              lineStyle: {
                width: 8,
                color: colorSection,
              },
            },
            axisTick: {
              show: false, // 不显示刻度
            },
            splitLine: {
              show: false, // 不显示分割线
            },
            axisLabel: {
              show: false, // 不显示刻度标签
            },
            detail: {
              show: false, // 不显示数值
            },
            z: 1, // 底层
          },
          // 顶层：黑黄动条和数值显示
          {
            type: 'gauge',
            center: ['50%', '60%'],
            startAngle: 200,
            endAngle: -20,
            min: 0,
            max: max || 100,
            itemStyle: {
              color: colorSection[colorSection.findIndex((e) => e[0] * (max || 100) > value)][1],
            },
            radius: '68%',
            progress: {
              show: true,
              width: 25,
              roundCap: !true, // 动条头部为圆形
            },
            pointer: {
              show: false, // 不显示指针
            },
            axisLine: {
              show: false, // 不显示轴线
            },
            axisTick: {
              show: false, // 不显示刻度
            },
            splitLine: {
              show: false, // 不显示分割线
            },
            axisLabel: {
              show: false, // 不显示刻度标签
            },
            detail: {
              valueAnimation: true,
              width: '60%',
              lineHeight: 40,
              borderRadius: 8,
              offsetCenter: [0, '-15%'],
              fontSize: 25,
              fontWeight: 'bolder',
              formatter: `{value} ${unit}`,
              color: 'inherit',
            },
            data: [
              {
                value,
              },
            ],
            z: 3, // 顶层
          },
          // 背景
          {
            type: 'gauge',
            center: ['50%', '60%'],
            startAngle: 200,
            endAngle: -20,
            min: 0,
            max: 60,
            itemStyle: {
              color: '#ccc',
            },
            radius: '68%',
            progress: {
              show: true,
              width: 25,
              roundCap: !true, // 动条头部为圆形
            },
            pointer: {
              show: false, // 不显示指针
            },
            axisLine: {
              show: false, // 不显示轴线
            },
            axisTick: {
              show: false, // 不显示刻度
            },
            splitLine: {
              show: false, // 不显示分割线
            },
            axisLabel: {
              show: false, // 不显示刻度标签
            },
            detail: {
              show: false,
              valueAnimation: true,
            },
            data: [
              {
                value: 100,
              },
            ],
            z: 2,
          },
        ],
        group: 'avggroup',
      });
      // echartsInstance[index] = echartInstance;
      echartsInstance[state.model.findIndex((f) => f.type == type) + index] = echartInstance;
    } else if (type == 'survey' && survey.value && survey.value[index]) {
      const { xData, yData, name, unit, retain } = obj;
      const echartInstance = init(survey.value[index]);
      echartInstance.setOption(
        {
          xAxis: {
            type: 'category',
            data: xData.map((e) => e.split(' ')[1]),
          },
          yAxis: {
            type: 'value',
            axisLabel: {
              formatter: (value) => `${value} ${unit || '%'}`,
            },
            ...(name == 'GPU温度' ? { min: 20 } : {}),
          },
          grid: {
            left: 15,
            right: '2%',
            top: '14%',
            bottom: '4%',
            containLabel: true, // 防止坐标轴标签溢出
          },
          tooltip: {
            show: true,
            trigger: 'axis', // 或 'item'
            formatter: (params) => {
              const index = params[0].dataIndex;
              return (
                xData[index] +
                '<br/>' +
                params
                  .map(
                    (item) => `<div>
                    ${item.marker}
                    <span>${item.seriesName}</span>
                    <span style='float: right;margin-left: 25px;'>${item.value.toFixed(retain || 0)} ${unit || ''}</span>
                  </div>`,
                  )
                  .join('')
              );
            },
          },
          legend: {
            show: false, // 图例存在但不显示
            data: yData.map((e) => e.key),
          },
          title: {
            text: `${name}`,
            textStyle: {
              fontSize: '14px',
            },
            textAlign: 'left',
            padding: [15, 0, 0, 15],
          },
          series: yData.map((e) => ({
            data: e.data,
            type: 'line',
            name: e.key,
          })),
          group: 'survey',
        },
        { replaceMerge: ['series'] },
      );
      // echartsInstance[state.model.filter((f) => f.type).length + index] = echartInstance;
      echartsInstance[state.model.findIndex((f) => f.type == type) + index] = echartInstance;
    } else if (type == 'health' && health.value && health.value[index]) {
      const echartInstance = init(health.value[index]);
      const { value, sername } = obj;
      echartInstance.setOption({
        // tooltip: {
        //   trigger: 'item',
        // },
        color: ['#41c590', '#c2c2c2'],
        tooltip: {
          trigger: 'item',
          formatter: '{b} : {c} ({d}%)',
        },
        legend: {
          top: 'center',
          left: 'right',
          orient: 'vertical',
          itemGap: 20,
          align: 'left',
        },

        series: [
          {
            name: sername,
            type: 'pie',
            radius: ['30%', '60%'],
            avoidLabelOverlap: false,
            center: ['40%', '50%'],
            label: {
              show: false,
              position: 'center',
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 20,
                fontWeight: 'bold',
              },
            },
            labelLine: {
              show: false,
            },
            data: value,
          },
        ],
      });
      echartsInstance[state.model.findIndex((f) => f.type == type) + index] = echartInstance;
    }
  };

  const rowClick = (e, i) => ({
    onclick: () => {
      echartsInstance.map((e) => {
        // console.log(e?.getOption()?.title[0].text);
      });
      let echart = echartsInstance.find((f) => f?.getOption()?.title?.[0].text.includes(e.key));
      const legends = echart.getOption().legend[0];
      const { yData, tableData } = state.model.filter((e) => e.type == 'survey')[i];
      let alone = Object.values(legends.selected).filter((l) => l).length - 1; //单独展示

      tableData.map((t) => {
        if (t.activ) t.activ = undefined;
      });
      e.activ = !!alone || undefined;
      yData.map((y) => {
        if (alone) {
          if (y.key !== e.name)
            echart.dispatchAction({
              type: 'legendUnSelect',
              name: y.key,
            }); //移除
        } else
          echart.dispatchAction({
            type: 'legendSelect',
            name: y.key,
          }); //显示
      });
    },
  });

  const toPath = (path: string) => {
    router.push(path);
  };

  window.addEventListener('resize', () => {
    // console.log(echartsInstance);
    if (echartsInstance) echartsInstance.map((e) => e?.resize());
  });

  onMounted(async () => {
    await getMasterData();
  });

  watch(
    () => state.condition.search,
    (val) => {
      if (val === null) onFinish(true);
    },
    { deep: true },
  );

  onUnmounted(() => {
    clearInterval(state.timer);
  });
</script>

<template>
  <div id="kubernetes">
    <div>
      <!-- 集群健康 -->
      <div class="title">当前集群监控</div>
      <div class="health">
        <div v-for="(n, i) in state.model.filter((e) => e.type == 'health')" :key="i">
          <div></div>
          <div>
            <div @click="toPath(n.path)">
              <div style="color: rgb(4 220 249)">{{ n.leftName }}</div>
              <div>{{ n.total }}</div>
            </div>
            <div ref="health"></div>
          </div>
        </div>
      </div>
    </div>
    <div></div>
    <div>
      <!-- 用量概览 -->
      <div class="survey-condition title" style="border-top: 0px solid rgba(204, 204, 204, 0.6)">
        <div>整体硬件用量概览</div>
        <!-- <CustomForm
          :form-items="formConfig"
          :value="state.condition"
          @on-finish="() => debounce(onFinish(), 2000)"
          @on-rest="() => debounce(() => onFinish(true), 500)"
        /> -->
        <a-select
          v-model:value="state.selectGpu"
          mode="multiple"
          max-tag-count="responsive"
          style="width: 200px; margin-right: 20px"
          :options="selectGpuOptions"
          @dropdown-visible-change="selectGpuChange"
        ></a-select>
        <a-select
          v-model:value="state.selectType"
          style="width: 150px; margin-right: 20px"
          :options="selectTypeOptions"
          @change="selectTypeChange"
        ></a-select>
        <a-range-picker
          v-model:value="state.condition.search"
          :disabled="state.selectType != 'custom'"
          :placeholder="formConfig[0].placeholder"
          format="YYYY-MM-DD HH:mm:ss"
          :show-time="true"
          @ok="() => onFinish()"
        />
        <div style="display: flex; margin-left: 20px">
          <HistoryOutlined />
          <a-select
            v-model:value="state.timing"
            disabled
            style="width: 80px; margin-left: 10px"
            :options="[{ label: '5 min', value: 300 }]"
            @change="() => {}"
          ></a-select>
        </div>
      </div>
      <div :class="{ survey: true, 'overflow-scroll': true }">
        <div class="avg-echarts-list">
          <div v-for="(n, i) in state.model.filter((e) => e.type == 'avg')" :key="i" ref="avggroup">
            <a-spin v-if="n.loading" class="loading" />
            <a-empty v-if="n.empty" :image="simpleImage" class="empty" />
          </div>
        </div>
        <div class="echarts-list">
          <div v-for="(n, i) in state.model.filter((e) => e.type == 'survey')" :key="i">
            <div ref="survey">
              <a-spin v-if="n.loading" class="loading" />
              <a-empty v-if="n.empty" :image="simpleImage" class="empty" />
            </div>
            <a-table
              :columns="columns(i)"
              :data-source="n?.tableData || []"
              :pagination="false"
              size="small"
              :custom-row="(e) => rowClick(e, i)"
              :scroll="{ x: 0, y: 280 }"
              @change="(_: any, __: any, sorter: any) => handleChange(sorter, i)"
            >
              <template #bodyCell="{ column, record, text }">
                <div v-if="column.dataIndex === 'name'" class="row-name">
                  <div :style="{ opacity: n.tableData.some((s) => s.activ) && !record.activ ? 0.4 : 1 }">
                    {{ text }}
                  </div>
                  <div
                    :style="{
                      backgroundColor:
                        state.echartsColor?.[n.tableData.findIndex((e) => e.name == text)] || 'transparent',
                    }"
                  ></div>
                  <!-- chart.getModel().get('color') -->
                </div>
              </template>
            </a-table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
  #kubernetes {
    display: flex;
    // flex-direction: column;
    width: 100%;
    height: 100%;

    > div:nth-child(1) {
      width: 30%;
    }
    > div:nth-child(2) {
      width: 20px;
      margin: -12px 20px -12px 0px;
      background-color: rgb(245, 245, 245);
    }
    > div:nth-child(3) {
      width: calc(70% - 20px - 20px * 2);
      height: 100%;
    }

    .health {
      display: flex;
      flex-direction: column;
      margin-right: 20px;
      margin-left: 8px;
      height: calc(100% - 60.7px);
      > div {
        height: 100%;
        width: 100%;
        display: flex;
        // background-color: rgba(204, 204, 220, 0.12);
        border: solid 1px rgb(204 204 220 / 30%);
        border-radius: 4px;
        // flex-direction: column;
        // width: 100%;

        > div:nth-child(1) {
        }
        > div:nth-child(2) {
          padding-right: 10%;
          cursor: pointer;
          flex: 1;
          width: 100%;
          display: flex;
          flex-direction: column;
          > div:nth-child(1) {
            width: 100%;
            margin-top: 20px;
            min-width: 70px;
            display: flex;
            justify-content: left;
            align-items: center;
            margin-left: 20px;
            > div:nth-child(2) {
              font-size: 25px;
              font-weight: 600;
              // margin-top: 20px;
            }
            > div:nth-child(1) {
              font-size: 14px;
              text-align: center;
               margin-right: 20px;
            }
          }
          > div:nth-child(2) {
            flex: 1;
          }
        }
      }
      > div:not(div:nth-last-child(1)) {
        margin: 30px 0% 20px 0;
      }
    }

    .survey-condition {
      display: flex;
      align-items: center;
      > div:nth-child(1) {
        flex: 40%;
      }
      margin-bottom: 20px;
    }
    .survey {
      display: flex;
      flex: 1;
      flex-direction: column;
      // min-height: 300px;
      height: calc(100% - 73px);
      .avg-echarts-list {
        display: flex;
        height: 250px;
        min-height: 250px;
        padding-bottom: 20px;

        > div {
          width: calc((100% - 40px) / 5);
          // width: 25%;
          height: 100%;
          // background-color: rgb(204 204 220 / 12%);
          border: solid 1px rgb(204 204 220 / 30%);
          border-radius: 4px;
          position: relative;
        }

        > div:not(div:nth-last-child(1)) {
          margin-right: 10px;
        }
      }

      .echarts-list {
        display: flex;
        flex: 1;
        flex-wrap: wrap;
        @gap: 0px;

        > div {
          display: flex;
          flex-direction: row;
          width: calc((100% - @gap) / 1);
          margin-bottom: 20px;
          height: 350px;
          // background-color: rgb(204 204 220 / 12%);
          border: solid 1px rgb(204 204 220 / 30%);
          border-radius: 4px;
          // border: 1px solid;

          > div:nth-child(1) {
            flex: 1;
            position: relative;
          }

          > div:nth-child(2) {
            width: 30%;
            min-width: 316px;
            margin-top: 20px;
          }
        }

        > div:nth-child(2n + 1) {
          // margin: 0 @gap @gap 0;
        }

        .row-name {
          position: relative;
          cursor: pointer;

          > div:nth-child(1) {
            margin-left: 15px;
          }

          > div:nth-child(2) {
            position: absolute;
            top: 6.5px;
            left: -5px;
            width: 15px;
            height: 10px;
            border-radius: 3px;
          }
        }
      }
    }
    .loading {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
    .empty {
      height: calc(100% - 64px);
      margin-top: 30%;
    }
    .title {
      font-size: 18px;
      font-weight: 600;
      padding-top: 20px;
    }
  }

  @media screen and (max-width: 1400px) {
    .health {
      //  margin-right: 4% !important;
    }
  }

  @media screen and (max-width: 1200px) {
    .echarts-list {
      display: flex;
      flex: 1;
      flex-wrap: wrap;
      > div {
        flex-direction: column !important;
        height: 700px !important;
        > div {
          width: 100% !important;
        }
      }
    }
    
    // overflow: hidden;
    // display: -webkit-box;
    // -webkit-box-orient: vertical;
    // -webkit-line-clamp: 1;


    .survey-condition {
      > div:nth-child(1) {
        flex: 24% !important;
      }
      margin-bottom: 2% !important;
    }
  }
</style>
