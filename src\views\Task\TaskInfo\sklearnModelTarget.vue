<script setup lang="ts">
  import { getSklearnModelMonitor } from '@/api/model';
  import type { EChartsType } from 'echarts';
  import { init } from 'echarts';
  import { reactive, ref, watch, onMounted } from 'vue';
  import { debounce } from 'lodash-es';
  import { useRoute } from 'vue-router';

  const props = defineProps<{ activeKey: number }>();
  const route = useRoute();
  const echarts = ref();
  const chartInstances: EChartsType[] = reactive([]);
  const dataSource = ref();
  const classes = ref<string[]>([]);
  const activeClass = ref('');
  const currentClass = reactive({
    fpr: [],
    tpr: [],
  });

  const renderEcharts = (fpr: number[], tpr: number[], category: string) => {
    const echartInstance = init(echarts.value);
    chartInstances.push(echartInstance);
    const len = tpr.length;
    const step = 1 / len;
    const array = Array.from({ length: len }, (_, i) => i * step);
    echartInstance.setOption({
      legend: {
        data: [category, 'Random cClassifier'],
        top: '10',
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '10%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        nameLocation: 'middle',
        nameGap: 30,
        data: fpr,
      },
      yAxis: {
        type: 'value',
      },
      tooltip: {
        show: true,
        trigger: 'axis',
      },
      series: [
        {
          data: tpr,
          type: 'line',
          name: category,
        },
        {
          data: array,
          type: 'line',
          name: 'Random cClassifier',
        },
      ],
    });
    echartInstance.resize();
  };

  const fetchData = async () => {
    const data = await getSklearnModelMonitor(String(route.query.taskid));
    dataSource.value = data;
    const { roc } = data;
    classes.value = Object.keys(roc || {});
    if (classes.value.length) {
      activeClass.value = classes.value[0];
    }
  };
  watch(
    () => activeClass.value,
    () => {
      if (activeClass.value) {
        const res = dataSource.value.roc[activeClass.value];
        const { fpr, tpr } = res;
        Object.assign(currentClass, { fpr, tpr });

        renderEcharts(currentClass.fpr, currentClass.tpr, activeClass.value);
      }
    },
    { deep: true, immediate: true },
  );
  watch(
    () => props.activeKey,
    (key) => {
      if (key !== 4) {
      } else {
        fetchData();
      }
    },
    { deep: true, immediate: true },
  );

  const resizeObserver = new ResizeObserver(
    // @ts-expect-error
    debounce((entries) => {
      for (const _ of entries) {
        console.log(_);
        chartInstances.forEach((item: EChartsType) => {
          item.resize();
        });
      }
    }, 300),
  );

  onMounted(() => {
    resizeObserver.observe(document.body);
  });
</script>

<template>
  <a-radio-group v-model:value="activeClass">
    <a-radio-button v-for="item in classes" :key="item" :value="item">{{ item }}</a-radio-button>
  </a-radio-group>
  <div ref="echarts" class="echart-box">
    <a-empty />
  </div>
</template>

<style scoped lang="less">
  .echart-box {
    height: calc(100% - 72px);
    margin-top: 10px;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 1px solid #ccc;
  }
</style>
