import type { IFetchK8sList, IFetchSystemMetrics, IFetchSystemMetricsWithoutContainer, IPodsContainerLogs, IPodsList, IServiceList } from '@/interface/kubernetes';
import request from '@/utils/request';

export const fetchK8sClusterList = (data: IFetchK8sList) => {
  return request.$Axios.get(`/k8s_cluster/deployments_list`, data);
}
// 获取yaml文件
export const fetchYamlConfig = (name: string) => {
  return request.$Axios.get(`/k8s_cluster/deployment/yaml_config?name=${name}`);
}
// 获取工作负载详情
export const fetchClusterDetail = (name: string) => {
  return request.$Axios.get(`/k8s_cluster/deployment/metadata_detail`, { name });
}
// 获取工作负载容器组列表
export const fetchClusterPods = (name: string) => {
  return request.$Axios.get(`/k8s_cluster/deployment/pods_list`, { name });
}
// 获取容器组yaml文件
export const fetchPodsYamlConfig = (name: string) => {
  return request.$Axios.get(`/k8s_cluster/pod/yaml_config`, { name });
}
// 获取工作负载服务与路由列表
export const fetchClusterServices = (name: string) => {
  return request.$Axios.get(`/k8s_cluster/deployment/services`, { name });
}
// 获取服务与路由yaml文件
export const fetchServiceYamlConfig = (name: string) => {
  return request.$Axios.get(`/k8s_cluster/service/yaml_config`, { name });
}
// 获取工作负载历史记录列表
export const fetchClusterHistory = (name: string) => {
  return request.$Axios.get(`/k8s_cluster/deployment/history_versions`, { name });
}
// 获取历史记录yaml文件
export const fetchHistoryYamlConfig = (name: string) => {
  return request.$Axios.get(`/k8s_cluster/replicaset/yaml_config`, { name });
}
// 获取工作负载事件列表
export const fetchClusterEvent = (data: { name: string, event_type?: string }) => {
  return request.$Axios.get(`/k8s_cluster/deployment/events`, data);
}
// 工作负载获取容器组下拉列表
export const fetchPodFilterList = (data: { deployment_name: string }) => {
  return request.$Axios.get(`/k8s_cluster/pod/filter_list`, data);
}
// 工作负载获取容器下拉列表
export const fetchContainerFilterList = (data: { pod_name: string }) => {
  return request.$Axios.get(`/k8s_cluster/pod/containers/filter_list`, data);
}
// 获取 Pod 容器的系统监控指标
export const fetchPodSystemMetrics = (data: IFetchSystemMetrics) => {
  return request.$Axios.post(`/k8s_cluster/deployment/pod/container/sys_metrics`, data);
}
// 获取 Pod 容器的系统监控指标 (无容器名称)
export const fetchPodSystemMetricsWithoutContainer = (data: IFetchSystemMetricsWithoutContainer) => {
  return request.$Axios.post(`/k8s_cluster/pod/sys_metrics`, data);
}
// 获取容器组列表
export const fetchPodsList = (data: IPodsList) => {
  return request.$Axios.get(`/k8s_cluster/pods_list`, data);
}
// 容器组获取容器列表
export const fetchPodContainerList = (pod_name: string) => {
  return request.$Axios.get(`/k8s_cluster/pod/containers_list`, { pod_name });
}
// 容器组容器日志
export const fetchPodContainerLogs = (data: IPodsContainerLogs) => {
  return request.$Axios.post(`/k8s_cluster/pods/containers/logs`, data);
}
// 获取容器组事件列表
export const fetchPodEvent = (data: { name: string, event_type?: string }) => {
  return request.$Axios.get(`/k8s_cluster/pods/events`, data);
}
// 获取服务与路由列表
export const fetchServicesList = (data: IServiceList) => {
  return request.$Axios.get(`/k8s_cluster/services_list`, data);
}
// 获取服务与路由详情
export const fetchServicesDetail = (name: string) => {
  return request.$Axios.get(`/k8s_cluster/service/detail`, { name });
}
// 服务与路由获取容器列表
export const fetchServicePodList = (name: string) => {
  return request.$Axios.get(`/k8s_cluster/services/pods_list`, { name });
}
// 获取服务与路由事件列表
export const fetchServiceEvent = (data: { name: string, event_type?: string }) => {
  return request.$Axios.get(`/k8s_cluster/services/events`, data);
}
// 获取容器组工作负载列表
export const fetchPodDeploymentList = (pod_name: string) => {
  return request.$Axios.get(`/k8s_cluster/pod/deployment`, { pod_name });
}
// 获取容器组服务与路由列表
export const fetchPodServiceList = (pod_name: string) => {
  return request.$Axios.get(`/k8s_cluster/pod/service`, { pod_name });
}
// 获取服务与路由工作负载列表
export const fetchServiceDeploymentList = (service_name: string) => {
  return request.$Axios.get(`/k8s_cluster/service/deployment`, { service_name });
}


