import { defineConfig } from '@playwright/test';

export default defineConfig({
  testDir: './e2e', // 测试用例文件夹
  timeout: 300 * 1000, // 每个测试最大超时
  workers: 4,
  use: {
    baseURL: 'http://localhost:5173',
    browserName: 'chromium',
    headless: true,
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',
    trace: 'retain-on-failure',
  },
  webServer: {
    command: 'vite preview --port 5173',
    port: 5173,
    reuseExistingServer: !process.env.CI,
  },
  reporter: [['html', { open: 'never' }]],
});
