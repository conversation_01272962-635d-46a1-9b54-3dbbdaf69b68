<script setup lang="ts">
  import type { IFormItem } from '@/components/CustomForm/index.vue';
  import type { IPage, IAceOptions } from '@/interface';
  import { CustomForm } from '@/components';
  import type { IKubernetesItem, IKubernetesList, ISearchState } from '@/interface/kubernetes.ts';
  import { TABLE_PAGINATION } from '@/json/common';
  import type { ColumnType, TablePaginationConfig } from 'ant-design-vue/es/table';
  import { RedoOutlined } from '@ant-design/icons-vue';
  import { ref, reactive, onMounted, onUnmounted, watch } from 'vue';
  import { copyText, formatTime, convertIsoTimeToLocalTime } from '@/utils/common';
  import { VAceEditor } from 'vue3-ace-editor';
  import 'ace-builds/src-noconflict/theme-chrome';
  import 'ace-builds/src-noconflict/theme-monokai.js';
  import 'ace-builds/src-noconflict/mode-python.js';
  import 'ace-builds/src-noconflict/mode-sh.js';
  import { fetchK8sClusterList, fetchPodDeploymentList, fetchServiceDeploymentList, fetchYamlConfig } from '@/api/k8s';
  import type { IFetchK8sList } from '@/interface/k8s';
  import yaml from 'js-yaml';
  import { useRoute, useRouter } from 'vue-router';
  const route = useRoute();
  const router = useRouter();
  const pagination = reactive({ ...TABLE_PAGINATION });
  const pageParame: IPage = reactive({ page: 1, limit: 10 });
  const tableHeight = ref(0);
  const spinning = ref(false);
  const DEFAULT_SEARCHSTATE: ISearchState = {
    name: '',
  };
  const searchState: ISearchState = reactive({ ...DEFAULT_SEARCHSTATE });
  const pollingInterval = ref();
  const countInterval = ref();
  const count = ref(10);

  const dataSource: IKubernetesItem[] = reactive([]);
  const YAMLState: { visible: boolean; value: string; title: string; spinning: boolean } = reactive({
    visible: false,
    title: 'Deployment',
    spinning: false,
    value: '',
  });
  const formConfig: IFormItem[] = [
    {
      field: 'name',
      type: 'input',
      label: '工作负载名称',
      placeholder: '请输入',
    },
  ];
  const columns: ColumnType[] = [
    { title: '工作负载名称', dataIndex: 'name' },
    { title: '运行模式', dataIndex: 'mode' },
    { title: '工作负载类型', dataIndex: 'type' },
    { title: '状态', dataIndex: 'status', width: 80 },
    { title: '创建时间', dataIndex: 'creation_time' },
    { title: '更新时间', dataIndex: 'last_update_time' },
    { title: '运行时长', dataIndex: 'runtime' },
    { title: '镜像', dataIndex: 'images' },
    { title: '容器组数量', dataIndex: 'replicas', width: 120 },
    { title: '操作', dataIndex: 'operation', width: 80 },
  ];
  const aceOptions: IAceOptions = {
    enableBasicAutocompletion: true,
    enableSnippets: true,
    enableLiveAutocompletion: true,
    tabSize: 4,
    enableEmmet: true,
    fontSize: 15,
    showPrintMargin: false, // 去除编辑器里的竖线
    highlightActiveLine: true,
    useWorker: true,
    readOnly: false,
  };
  const toggleTable = (_pagination: TablePaginationConfig) => {
    const { current, pageSize } = _pagination;
    Object.assign(pagination, { current, pageSize });
    Object.assign(pageParame, { page: current, limit: pageSize });
    handleRedo();
  };

  const onFinish = (values: Record<string, string>) => {
    const searchConfig: { [key: string]: string } = {};
    for (const key in values) {
      searchConfig[key] = values[key];
    }
    Object.assign(searchState, { ...searchConfig });
    pageParame.page = 1;
    handleRedo();
  };

  const onRest = () => {
    Object.assign(pagination, { ...TABLE_PAGINATION });
    Object.assign(pageParame, { page: 1, limit: 10 });
    Object.assign(searchState, { ...DEFAULT_SEARCHSTATE });
    handleRedo();
  };

  const getList = async () => {
    let data: IKubernetesItem[] = [];
    if (route.name === 'workload') {
      const params: IFetchK8sList = { ...pageParame };
      Object.keys(searchState).forEach((key) => {
        if (searchState[key as keyof ISearchState]) {
          params[key as keyof ISearchState] = searchState[key as keyof ISearchState];
        }
      });
      const res: IKubernetesList = await fetchK8sClusterList(params);
      const { items, total } = res;
      data = items;
      Object.assign(pagination, { current: pageParame.page, total: total });
    } else {
      if (route.name === 'service-detail') {
        data = await fetchServiceDeploymentList(String(route.params.name));
      }
      if (route.name === 'pod-detail') {
        data = await fetchPodDeploymentList(String(route.params.name));
      }
    }
    dataSource.length = 0;
    dataSource.push(...data);
  };

  // 开始轮询
  const startPolling = async () => {
    // 先立即获取一次数据
    await getList();
    // 设置定时轮询
    pollingInterval.value = setInterval(async () => {
      await getList();
    }, 11000);
    countInterval.value = setInterval(() => {
      count.value > 0 ? count.value-- : (count.value = 10);
    }, 1000);
  };

  // 停止轮询
  const stopPolling = () => {
    if (pollingInterval.value) {
      clearInterval(pollingInterval.value);
      pollingInterval.value = null;
    }
    if (countInterval.value) {
      clearInterval(countInterval.value);
      countInterval.value = null;
    }
  };

  // 刷新
  const handleRedo = () => {
    stopPolling();
    startPolling();
    // 重置倒计时
    count.value = 10;
  };

  const getTableHeight = () => {
    const tableItem = document.querySelector('.container');
    tableHeight.value = (tableItem?.clientHeight as number) - 46;
  };
  const fetchYamlConfigReq = async (record: IKubernetesItem) => {
    YAMLState.visible = true;
    YAMLState.spinning = true;
    const data = await fetchYamlConfig(record.name);
    YAMLState.value = yaml.dump(data);
    YAMLState.spinning = false;
  };
  const jumpDetail = (record: IKubernetesItem) => {
    router.push(`/cluster-manage/workload/${record.name}`);
  };

  onMounted(() => {
    startPolling();
    getTableHeight();
  });

  onUnmounted(() => {
    stopPolling();
  });

  watch(
    () => route.path,
    (path) => {
      if (path !== '/cluster-manage/workload') {
        stopPolling();
      }
    },
    { deep: true },
  );
</script>

<template>
  <router-view v-if="route.name === 'workload-detail' && route.params.name" />
  <a-spin v-else :spinning="spinning">
    <CustomForm
      v-if="!route.params.name"
      :form-items="formConfig"
      @on-finish="onFinish"
      @on-rest="onRest"
      style="margin-bottom: 0"
    />
    <div class="refresh">
      <a-button style="padding: 0">
        <div class="refresh-count">{{ count }}s</div>
        <div class="refresh-icon" @click="handleRedo">
          <RedoOutlined />
        </div>
      </a-button>
    </div>
    <a-table
      :data-source="dataSource"
      :columns="columns"
      :pagination="pagination"
      :scroll="{ y: tableHeight - 170 }"
      @change="toggleTable"
    >
      <template #bodyCell="{ column, text, record }">
        <div v-if="column.dataIndex === 'operation'">
          <a @click="fetchYamlConfigReq(record)">YAML</a>
        </div>
        <div v-else-if="['creation_time', 'last_update_time'].includes(column.dataIndex)">
          {{ convertIsoTimeToLocalTime(text) }}
        </div>
        <div v-else-if="column.dataIndex === 'name'">
          <a class="table-a-btn" @click="jumpDetail(record)">{{ text }}</a>
        </div>
        <div v-else-if="column.dataIndex === 'mode'">
          <a-tag>{{ record.name.startsWith('ai-platform') ? '中台托管' : '其他' }}</a-tag>
        </div>
        <div v-else-if="column.dataIndex === 'status'">
          <a-tag :color="text.split('/')[0] === '0' ? '#d5d5d5' : '#47d89e'">{{ text }}</a-tag>
        </div>
        <div v-else-if="column.dataIndex === 'images'">
          {{ text ? text.join('、') : '' }}
        </div>
        <div v-else-if="column.dataIndex === 'runtime'">
          {{ formatTime(Math.floor(text)) }}
        </div>
        <div v-else-if="column.dataIndex === 'replicas'">
          {{ text || 0 }}
        </div>
        <div v-else>{{ text }}</div>
      </template>
    </a-table>
  </a-spin>
  <a-modal
    v-model:open="YAMLState.visible"
    :title="YAMLState.title"
    width="60%"
    centered
    :footer="false"
    @cancel="
      YAMLState.visible = false;
      YAMLState.value = '';
    "
  >
    <div class="h-800px overflow-scroll">
      <a-spin :spinning="YAMLState.spinning">
        <div class="w-100% h-32px flex flex-justify-end">
          <a @click="copyText(YAMLState.value)"> 复制到剪切板 </a>
        </div>
        <v-ace-editor
          v-model:value="YAMLState.value"
          lang="sh"
          theme="monokai"
          :options="aceOptions"
          style="height: calc(100% - 32px); border: 1px solid #dbd3d3"
        />
      </a-spin>
    </div>
  </a-modal>
</template>

<style scoped lang="less">
  @import url('../index.less');
</style>
