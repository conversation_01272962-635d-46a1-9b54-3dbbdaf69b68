<script setup lang="ts">
  import {
    agentVersionDetail,
    getAgentDetail,
    getDebuggerFollowupQuestion,
    getFollowupQuestion,
    getLike,
    getDisLike,
  } from '@/api/agent';
  import type { IAgentItem, IMessage } from '@/interface/agent';
  import { ref, onMounted, reactive, onUnmounted, computed, nextTick } from 'vue';
  import { useRoute } from 'vue-router';
  import {
    RightCircleOutlined,
    StopOutlined,
    AudioOutlined,
    FileImageOutlined,
    CloseOutlined,
  } from '@ant-design/icons-vue';
  import { fetchEventSource } from '@microsoft/fetch-event-source';
  import sendBtn from '@/assets/image/base/pictures/sendbtn.png';
  import sendDisableBtn from '@/assets/image/base/pictures/sendDisable.png';
  import Recording from '@/assets/image/base/pictures/voice.gif';
  import { getLocalItem } from '@/utils/common';
  import { WebSocketClient as webSocketClass } from '@/utils/ws';
  import { recOpen, recStart, recStop } from '@/utils/recorder';
  import config from '@/config';
  //@ts-expect-error
  import MarkdownIt from 'markdown-it';
  import type { ColumnType } from 'ant-design-vue/es/table';
  import { message } from 'ant-design-vue';
  import { uploadImages } from '@/api/exploration';
  import Icon from '@/components/Icon/index.vue';
  const md = new MarkdownIt({
    html: true, // 允许HTML标签
    linkify: true, // 自动转换URL为链接
    typographer: true, // 美化排版
  });

  interface asrWsProps {
    answer: string;
    isEnd?: boolean;
    isLast?: boolean;
    isFinal?: boolean;
  }

  // const md = new MarkdownIt();
  const route = useRoute();
  interface IBasicState {
    name: string;
    logo: string;
    suggestedQuestions: string[];
    prologue: string;
    background: string;
    deploy_id: string;
    dialogue_rounds: number;
  }
  const image = ref('');
  const basicState = reactive<IBasicState>({
    name: '',
    logo: '',
    suggestedQuestions: [],
    prologue: '',
    background: '',
    deploy_id: '',
    dialogue_rounds: 0,
  });
  const isMultimodal = ref(false);
  const inputMessage = ref('');
  interface ICitationState {
    type: string;
    visible: boolean;
    content: string | [];
  }
  const citationsState = reactive<ICitationState>({
    visible: false,
    content: [],
    type: 'string',
  });

  // 用于控制流式请求
  const abortController = ref<AbortController | null>(null);
  const spinning = ref(false);
  const isLoading = ref(false);
  const open = ref(false);
  const deployOpen = ref(false);
  const loadingOpen = ref(false);
  const isRecording = ref(false);
  const isImageing = ref(false);
  const messageRef = ref();
  const containerRef = ref();
  const typingInterval = ref();
  const fileInputRef = ref();
  const columns = ref<ColumnType[]>([]);
  // const citations = ref([]);
  const isDebug = computed(() => route.name === 'intelligent-detail');
  const sendParams = reactive({
    instruction: '',
    knowledge_db_ids: [],
    memory_vars: [],
  });
  // 聊天消息数据
  const messages = reactive<IMessage[]>([]);

  // 点赞与点踩状态 - 使用对象存储每个消息的状态
  const feedbackState = reactive<Record<number, { isLike: boolean; isDislike: boolean }>>({});

  const handleChange = () => {
    if (inputMessage.value.trim()) {
      open.value = false;
      deployOpen.value = false;
      loadingOpen.value = false;
    }
  };
  // 发送消息
  const handleSend = async (value: string) => {
    open.value = false;
    deployOpen.value = false;
    loadingOpen.value = false;
    if (!value || !value.trim()) {
      open.value = true;
      return;
    }
    if (!basicState.deploy_id) {
      deployOpen.value = true;
      return;
    }
    const lastAssistantMessage = messages.filter((item) => item.role === 'assistant');
    const assistantMessageLength = lastAssistantMessage.length;
    if (isLoading.value || (assistantMessageLength > 0 && !lastAssistantMessage[assistantMessageLength - 1].content)) {
      loadingOpen.value = true;
      return;
    }
    const src = image.value ? JSON.parse(JSON.stringify(image.value)) : '';
    const userMessage = src ? { role: 'user', content: value, image: src } : { role: 'user', content: value };
    messages.push(userMessage);
    inputMessage.value = '';
    image.value = '';
    isImageing.value = false;

    await fetchAIResponse(value);
  };

  // 获取AI流式响应
  const fetchAIResponse = async (value: string) => {
    isLoading.value = true;
    abortController.value = new AbortController();
    const assistantMessage = { role: 'assistant', content: '', citations: [] };
    messages.push(assistantMessage);
    await nextTick(() => {
      if (messageRef.value) {
        messageRef.value[messageRef.value.length - 1].scrollIntoView({ behavior: 'auto', block: 'end' });
      }
    });
    let displayedText = '';
    try {
      const userMessage = messages.filter((item) => item.role === 'user');
      const lastUserMessage = userMessage[userMessage.length - 1];
      const src = lastUserMessage.image || '';
      await fetchEventSource(
        isDebug.value
          ? `/data/ai-platform-backend/api/v1/agent/debug/chat/${route.params.id}`
          : `/data/ai-platform-backend/api/v1/agent/chat/${route.params.id}`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization: getLocalItem('HQSK_AI_PLATFORM_FRONTEND_TOKEN'),
          },
          body: isDebug.value
            ? JSON.stringify({
                ...sendParams,
                history: messages.slice(0, -2).map((item) => ({ role: item.role, content: item.content })),
                content: src
                  ? [
                      { type: 'image_url', image_url: { url: src } },
                      { type: 'text', text: value },
                    ]
                  : [{ type: 'text', text: value }],
              })
            : JSON.stringify({
                history: messages.slice(0, -2).map((item) => ({ role: item.role, content: item.content })),
                content: src
                  ? [
                      { type: 'image_url', image_url: { url: src } },
                      { type: 'text', text: value },
                    ]
                  : [{ type: 'text', text: value }],
              }),
          signal: abortController.value.signal,
          // @ts-expect-error
          onopen(response: Response): Promise<void> | void {
            if (!response.ok) {
              throw new Error(`Server returned ${response.status}`);
            }
          },
          onmessage(event: { data: string }): void {
            if (event.data === '[DONE]') return;
            try {
              const content: string = event.data;
              displayedText += content;
            } catch (err) {
              console.error('Error parsing message:', err);
              throw err;
            }
          },
          onclose(): void {
            // isLoading.value = false;
            abortController.value = null;
          },
          onerror(err: Error): void {
            console.log('🚀 ~ onerror ~ err:', err);
            if (err.name === 'AbortError') {
              console.error('Stream error:', err);
              messages.push({
                role: 'assistant',
                content: '发生错误: ' + err.message,
              });
            }
            isLoading.value = false;
            abortController.value = null;
          },
        },
      );
    } catch (err: unknown) {
      if (err instanceof Error && err.name !== 'AbortError') {
        console.error('Request error:', err);
        messages.push({
          role: 'assistant',
          content: '请求错误: ' + err.message,
        });
      }
      isLoading.value = false;
      abortController.value = null;
    } finally {
      if (!displayedText) {
        messages[messages.length - 1].content = '服务异常，请稍后重试。。。';
        isLoading.value = false;
        return;
      }
      const regex = /<citation>(.*?)<\/citation>/g;
      const match = regex.exec(displayedText);
      if (match !== null && match[1]) {
        messages[messages.length - 1].citations = JSON.parse(match[1]);
      }
      const answer = md.render(displayedText.replace(/<citation>.*?<\/citation>/g, ''));
      const history = JSON.parse(JSON.stringify(messages));
      history[history.length - 1].content = answer;
      // await fetchFollowResponse(history);
      let index = 0;
      async function typeEffect() {
        if (index < answer.length) {
          messages[messages.length - 1].content += answer[index];
          // 将当前字符添加到元素中
          index++;
          // 设置下一次调用的时间间隔
          typingInterval.value = setTimeout(typeEffect, 50); // 每50毫秒打一个字
          await nextTick(() => {
            if (containerRef.value) {
              containerRef.value.scrollIntoView({ behavior: 'auto', block: 'end' });
            }
          });
        } else {
          isLoading.value = false;
          loadingOpen.value = false;
        }
      }
      typeEffect();
      await fetchFollowResponse(history);
    }
  };

  // 获取追问数据
  const fetchFollowResponse = async (history: { role: string; content: string }[]) => {
    if (basicState.dialogue_rounds) {
      const data = isDebug.value
        ? await getDebuggerFollowupQuestion(String(route.params.id), {
            // basicState.dialogue_rounds是轮数，一问一答为一轮 因此乘2
            history: history
              .map((item) => ({ role: item.role, content: item.content }))
              .slice(-(basicState.dialogue_rounds * 2)),
          })
        : await getFollowupQuestion(String(route.params.id), {
            // basicState.dialogue_rounds是轮数，一问一答为一轮 因此乘2
            history: history
              .map((item) => ({ role: item.role, content: item.content }))
              .slice(-(basicState.dialogue_rounds * 2)),
          });
      messages[history.length - 1].followup = data;
      await nextTick(() => {
        if (containerRef.value) {
          containerRef.value.scrollIntoView({ behavior: 'auto', block: 'end' });
        }
      });
    }
  };

  const handleClickCitation = (content: string | Record<string, string>) => {
    Object.assign(citationsState, { visible: true, content, type: typeof content });
    columns.value = getTableColumns();
  };

  const getTableColumns = () => {
    if (!citationsState.content) return [];
    const keys = Object.keys(citationsState.content);
    const columns: ColumnType[] = keys.map((key: string) => {
      return {
        title: key,
        dataIndex: key,
      };
    });
    return columns;
  };
  // 停止生成
  const stopGeneration = () => {
    if (abortController.value) {
      abortController.value.abort();
      abortController.value = null;
    }
    isLoading.value = false;
    if (messages.length && !messages[messages.length - 1].content) {
      messages[messages.length - 1].content = '已停止';
    }
    clearTimeout(typingInterval.value);
    typingInterval.value = null;
    open.value = false;
    deployOpen.value = false;
    loadingOpen.value = false;
  };
  const getAudioToTxt = (data: asrWsProps) => {
    let tempText = data?.answer?.replace(/no/, '');
    inputMessage.value = tempText;
  };
  // 语音识别 WS
  const asrWs = new webSocketClass(config.asr, (data: asrWsProps) => {
    if (data?.answer) {
      getAudioToTxt(data);
    }
  });
  const handleRecorder = () => {
    asrWs.connect();

    recOpen(
      () => {
        recStart();
        isRecording.value = true;
      },
      (res: string) => {
        console.error(res || '获取录音权限失败！');
        message.error('获取录音权限失败！');
      },

      (res) => {
        asrWs?.send(res);
      },
    );
  };

  const stopRecording = () => {
    recStop((res) => {
      asrWs?.send(res);
    });
    isRecording.value = false;
  };

  const cancelRecording = () => {
    recStop(() => {
      inputMessage.value = '';
      asrWs?.close();
    });
    isRecording.value = false;
  };

  const uploadProps = {
    // @ts-expect-error
    beforeUpload: (file: UploadProps['fileList'][number]) => {
      const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/jpg';
      if (!isJpgOrPng) {
        message.warn('上传的图片格式不支持！');
        return false;
      }
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        message.error('上传的图片太大！');
        return false;
      }
      return isJpgOrPng && isLt10M;
    },
    customRequest: async (detail: { file: File }) => {
      const file = detail.file;
      const formData = new FormData();
      formData.append('files', file);
      const res: { url: string }[] = await uploadImages(formData);
      isImageing.value = true;
      image.value = res[0].url;
    },
    multiple: false,
    fileList: [],
    accept: 'image/png,image/jpg,image/jpeg',
    showUploadList: false,
  };

  // @ts-expect-error
  const handleFileChange = async (e) => {
    const file = e.target.files[0];
    if (file) {
      // 这里可以添加文件验证逻辑
      if (file.size > 10 * 1024 * 1024) {
        message.error('文件大小不能超过10MB');
        return;
      }
      const formData = new FormData();
      formData.append('files', file);
      const res: { url: string }[] = await uploadImages(formData);
      image.value = res[0].url;
    }

    // 重置input，以便可以选择同一个文件多次
    e.target.value = null;
  };
  const handleDeleteImage = () => {
    image.value = '';
    isImageing.value = false;
  };
  const handleConfirmUpload = () => {
    console.log(fileInputRef.value);
    fileInputRef.value.click();
  };

  // 点赞处理
  const handleLike = async (message_index: number) => {
    // 初始化该消息的反馈状态
    if (!feedbackState[message_index]) {
      feedbackState[message_index] = { isLike: false, isDislike: false };
    }

    const currentState = feedbackState[message_index];

    // 如果当前是点赞状态，则取消点赞
    if (currentState.isLike) {
      currentState.isLike = false;
    } else {
      // 如果当前是点踩状态，先取消点踩
      if (currentState.isDislike) {
        currentState.isDislike = false;
      }
      // 设置为点赞状态
      currentState.isLike = true;

      // 调用点赞API
      await getLike(String(route.params.id), {
        feedback_type: 'good',
        message_index: message_index,
        tags: ['string'],
        detailed_content: 'string',
      });
    }
  };

  // 点踩处理
  const handleDislike = async (message_index: number) => {
    // 初始化该消息的反馈状态
    if (!feedbackState[message_index]) {
      feedbackState[message_index] = { isLike: false, isDislike: false };
    }

    const currentState = feedbackState[message_index];

    // 如果当前是点踩状态，则取消点踩
    if (currentState.isDislike) {
      currentState.isDislike = false;
    } else {
      // 如果当前是点赞状态，先取消点赞
      if (currentState.isLike) {
        currentState.isLike = false;
      }
      // 设置为点踩状态
      currentState.isDislike = true;

      // 调用点踩API
      await getDisLike(String(route.params.id), {
        feedback_type: 'bad',
        message_index: message_index,
        tags: ['string'],
        detailed_content: 'string',
      });
    }
  };
  // 组件卸载时中止请求
  onUnmounted(() => {
    stopGeneration();
    clearTimeout(typingInterval.value);
    typingInterval.value = null;
  });

  onMounted(async () => {
    spinning.value = true;
    try {
      const data: IAgentItem =
        route.name === 'intelligent-detail'
          ? await agentVersionDetail(String(route.params.id))
          : await getAgentDetail(String(route.params.id));
      const { icon_url, name, deploy_id, config, model_category } = data;
      isMultimodal.value = model_category === 'multimodal';
      const {
        instruction,
        memory_config,
        knowledge_db,
        opener_prompt,
        suggested_questions,
        background_image,
        followup_config,
      } = config;
      Object.assign(basicState, { logo: icon_url, name, deploy_id });
      if (isDebug.value) {
        Object.assign(sendParams, {
          instruction: instruction.value || '',
          // 在调试中，知识库开关开了才传知识库id
          knowledge_db_ids: knowledge_db.is_enable
            ? knowledge_db.value
              ? knowledge_db.value.map((item) => item.id)
              : []
            : [],
          memory_vars: memory_config.value
            ? memory_config.value.map((item) => {
                return {
                  ...item,
                  current_value: '',
                };
              })
            : [],
        });
      }
      if (opener_prompt.is_enable) {
        basicState.prologue = opener_prompt.value;
      }
      if (background_image.is_enable) {
        basicState.background = background_image.value;
      }
      if (suggested_questions.is_enable) {
        basicState.suggestedQuestions = suggested_questions.value;
      }
      if (followup_config && followup_config.is_enable) {
        basicState.dialogue_rounds = followup_config.value.dialogue_rounds;
      }
    } catch {
      message.warn('应用异常，请联系管理员');
    }

    spinning.value = false;
  });
</script>

<template>
  <div v-if="spinning" class="w-100% h-100% flex items-center justify-center">
    <a-spin />
  </div>
  <div v-else class="preview-container">
    <div
      class="preview-content"
      :style="{ width: isDebug ? '100%' : '70vw', backgroundImage: `url(${basicState.background})` }"
    >
      <div class="content">
        <div class="header">
          <!-- <img :src="basicState.logo" alt="" /> -->
          <div class="img-content" :style="{ backgroundImage: `url(${basicState.logo})` }" />
          <div class="text-24px mt-10px">{{ basicState.name }}</div>
        </div>
        <div v-if="basicState.prologue || basicState.suggestedQuestions.length" class="prologue overflow-scroll">
          <div v-if="basicState.prologue" class="mb-10px leading-24px">{{ basicState.prologue }}</div>
          <div v-if="basicState.suggestedQuestions.length" class="flex flex-col items-start">
            <div
              v-for="(item, index) in basicState.suggestedQuestions"
              :key="index"
              class="question-item"
              @click="handleSend(item)"
            >
              <div class="question-item-msg">{{ item }}</div>
              <RightCircleOutlined />
            </div>
          </div>
        </div>
      </div>
      <div class="chat-container overflow-scroll">
        <div ref="containerRef" class="messages flex flex-col">
          <div
            v-for="(message, index) in messages"
            :key="index"
            ref="messageRef"
            class="message"
            :style="{
              justifyContent: message.role === 'user' ? 'flex-end' : 'start',
              flexDirection: message.role === 'user' ? 'row' : 'column',
            }"
          >
            <div v-if="message.content" :class="message.role" class="message-content">
              <div
                v-if="message.role === 'user' && message.image"
                class="img-box"
                :style="{ backgroundImage: `url(${message.image})` }"
              ></div>
              <div v-html="message.content.replace(/<citation>.*?<\/citation>/g, '')"></div>
              <div v-if="message.role === 'assistant' && message.citations && message.citations.length">
                <div class="ml-10px mb-10px text-#797979">{{ `切片（${message.citations.length}）` }}</div>
                <div class="citation flex">
                  <div
                    v-for="(item, index) in message.citations"
                    :key="item"
                    class="citation-item"
                    @click="handleClickCitation(item)"
                    @cancel="citationsState.visible = false"
                  >
                    {{ typeof item === 'string' ? item : `切片信息${index + 1}` }}
                  </div>
                </div>
              </div>
            </div>
            <div v-else class="message-content"><a-spin /></div>
            <!-- 反馈按钮放在最后，只对助手回答显示 -->
            <div v-if="message.role === 'assistant' && message.content" class="feedback-buttons">
              <Icon
                :name="feedbackState[index]?.isLike ? 'xuanzhong-manyi' : 'manyi'"
                class="feedback-icon like-icon"
                @click="handleLike(index)"
              />
              <Icon
                :name="feedbackState[index]?.isDislike ? 'xuanzhong-bumanyi' : 'bumanyi'"
                size="24"
                class="feedback-icon dislike-icon"
                @click="handleDislike(index)"
              />
            </div>

            <div v-show="message.role === 'assistant' && message.followup && message.followup.length">
              <!-- followup -->
              <div class="followup flex flex-col">
                <div class="ml-10px m-y-10px text-#797979">继续问</div>
                <div v-for="item in message.followup" :key="item" class="followup-item" @click="handleSend(item)">
                  {{ item }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="chat-box"
        :style="{
          flexDirection: isImageing ? 'column' : 'row',
          padding: isImageing || isRecording ? '10px' : '0 10px',
        }"
      >
        <div v-if="isImageing">
          <div class="img-box" :style="{ backgroundImage: `url(${image})` }">
            <div class="closeIcon" @click="handleDeleteImage">
              <CloseOutlined />
            </div>
          </div>
        </div>
        <div class="flex items-center w-100%" :style="{ flexDirection: isRecording ? 'column' : 'row' }">
          <a-textarea
            v-model:value="inputMessage"
            class="custom-textarea"
            placeholder="请输入你的问题"
            :auto-size="{ minRows: 1, maxRows: 4 }"
            @change="handleChange"
            @press-enter.prevent="handleSend(inputMessage)"
          />

          <div v-if="isRecording" class="voice-box">
            <div class="custom-textarea">
              <a-button class="exit-recording" @click="cancelRecording">清空</a-button>

              <div class="recording-status">
                <img class="waveform" :src="Recording" alt="录音波形" />
                <span class="recording-text">录音中</span>
              </div>
              <a-button type="primary" class="exit-recording1" @click="stopRecording">确认</a-button>
            </div>
          </div>

          <div v-else class="flex flex-justify-end items-center">
            <template v-if="isMultimodal">
              <!-- 隐藏的文件输入 -->
              <input ref="fileInputRef" type="file" style="display: none" @change="handleFileChange" />
              <a-popconfirm
                v-if="image"
                title="文件覆盖提示?"
                description="新上传的文件会覆盖原有文件，是否继续上传"
                @confirm="handleConfirmUpload"
              >
                <FileImageOutlined style="font-size: 24px; color: #797979; cursor: pointer" />
              </a-popconfirm>
              <a-upload v-else class="avatar-uploader" v-bind="uploadProps" list-type="text">
                <FileImageOutlined style="font-size: 24px; color: #797979; cursor: pointer" />
              </a-upload>
              <div class="divider m-x-10px"></div>
            </template>
            <AudioOutlined style="font-size: 24px; color: #797979" @click="handleRecorder" />
            <div class="divider m-x-10px"></div>
            <a-tooltip :arrow-point-at-center="true" :open="open || deployOpen || loadingOpen">
              <template #title>{{
                open ? '请输入您的问题' : deployOpen ? '模型未配置，请配置后再输入' : loadingOpen ? '停止生成' : ''
              }}</template>
              <a-button
                v-if="isLoading || (messages.length && !messages[messages.length - 1].content)"
                type="primary"
                shape="circle"
              >
                <StopOutlined @click="stopGeneration" />
              </a-button>
              <template v-else>
                <img
                  v-if="!basicState.deploy_id || !inputMessage || !inputMessage.trim()"
                  class="send-disabled-icon"
                  :src="sendDisableBtn"
                  alt=""
                  :preview="false"
                />
                <img
                  v-else
                  class="send-icon"
                  :src="sendBtn"
                  alt=""
                  :preview="false"
                  @click="handleSend(inputMessage)"
                />
              </template>
            </a-tooltip>
          </div>
        </div>
      </div>
      <span class="flex justify-center align-center mb-10px text-#999999 text-12px">以上内容均由AI生成，仅供参考</span>
    </div>
  </div>

  <a-modal v-model:open="citationsState.visible" width="50%" centered title="回答来源" :footer="false">
    <div v-if="citationsState.type === 'string'" class="citation-model overflow-scroll">
      {{ citationsState.content }}
    </div>
    <a-table v-else :data-source="[citationsState.content]" :columns="columns" :scroll="{ x: 'max-content' }">
      <template #bodyCell="{ text }">
        {{ String(text) }}
      </template>
    </a-table>
  </a-modal>
</template>

<style scoped lang="less">
  .preview-container {
    width: 100%;
    height: 100%; /* 高度自适应 */
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
  .preview-content {
    height: 100%; /* 高度自适应 */
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    padding: 20px;
    padding-bottom: 0;
    display: flex;
    flex-direction: column;
  }
  .content {
    padding-bottom: 15px;
  }
  .header {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 20px;
    // img {
    //   width: 96px;
    //   height: 96px;
    // }

    .img-content {
      width: 96px; /* 宽度自适应 */
      height: 96px; /* 高度自适应 */
      background-color: #fff;
      border-radius: 10px;
      // background-image: url('your-image.jpg');
      background-size: contain;
      background-repeat: no-repeat;
      background-position: center;
    }
  }
  .prologue {
    background: #fff;
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 10px;
    max-height: 200px;
    .question-item {
      display: flex;
      cursor: pointer;
      justify-content: space-between;
      width: auto;
      padding: 10px;
      background-color: #f2f8ff;
      color: #1777ff;
      border-radius: 20px;
      margin-bottom: 5px;
      .question-item-msg {
        margin-right: 10px;
      }
    }
  }

  .chat-container {
    flex: 1;
    .messages {
      display: flex;
      flex-direction: column;
      overflow-anchor: auto; /* 关键属性，让滚动条自动跟随新内容 */
      .message {
        display: flex;
        margin-bottom: 10px;
        .message-content {
          padding: 10px;
          max-width: 80%;
          line-height: 20px;
          word-break: break-all;
          background-color: #fff;
          border: 1px solid #eee;
          border-radius: 10px;
        }
        .user {
          border-bottom-right-radius: 0;
        }
        .assistant {
          border-bottom-left-radius: 0;
        }
      }
    }
  }
  .chat-box {
    position: relative;
    box-sizing: border-box;
    display: flex;
    // align-items: center;
    width: 100%;
    height: auto;
    padding: 0 10px;
    margin-bottom: 6px;
    background-color: #fff;
    border-radius: 6px;
    box-shadow: 0 0 0 2px rgba(5, 145, 255, 0.1);

    :deep(.custom-textarea) {
      position: relative;
      box-sizing: border-box;
      height: auto;
      padding: 10px;
      font-family: PingFangSC, 'PingFang SC';
      font-size: 16px;
      font-weight: 400;
      background: rgb(255 255 255 / 20%);
      border-radius: 6px;
      overflow: hidden; /* 隐藏滚动条 */
      resize: none; /* 禁止用户调整大小 */

      /* 隐藏滚动条 */
      &::-webkit-scrollbar {
        display: none;
      }

      /* 占位符样式 */
      &::placeholder {
        color: #cccccc;
      }
    }
    .divider {
      width: 1px;
      height: 24px;
      background: #646566;
      border-radius: 1px;
    }
    .video-icon,
    .send-icon,
    .send-disabled-icon {
      width: 32px;
      height: 32px;
      cursor: pointer;
    }
    .send-disabled-icon {
      cursor: not-allowed;
    }
    .voice-box {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      height: 48px;
      padding: 0 16px;
      background: rgba(233, 236, 242, 0.7);
      border-radius: 6px;
      backdrop-filter: blur(4px);

      .custom-textarea {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;

        .exit-recording {
          font-size: 14px;
          font-weight: 500;
          color: #666;
          margin-right: 12px;
        }
        .exit-recording1 {
          font-size: 14px;
          font-weight: 500;
          color: #fff;
          margin-right: 12px;
        }

        .recording-status {
          display: flex;
          align-items: center;
          gap: 8px;

          .waveform {
            height: 24px;
            width: auto;
          }

          .recording-text {
            font-size: 14px;
            font-weight: 500;
            color: #1777ff;
          }
        }

        .send-icon {
          width: 32px;
          height: 32px;
          cursor: pointer;
        }
      }
    }
  }

  :deep(.ant-input) {
    border: none;
    &:focus {
      box-shadow: none !important;
    }
  }
  .citation-item,
  .followup-item {
    max-width: calc(50% - 10px);
    white-space: nowrap; /* 禁止换行 */
    overflow: hidden; /* 隐藏溢出内容 */
    text-overflow: ellipsis;
    cursor: pointer;
    background-color: #f2f8ff;
    color: #1777ff;
    border-radius: 20px;
    margin-bottom: 5px;
    padding: 10px;
    margin: 0 5px;
  }

  .followup-item {
    // max-width: calc(100% - 10px);
    background-color: #fff;
    color: #797979;
    margin-bottom: 10px;
  }
  .citation-model {
    max-height: 400px;
    padding: 10px;
  }

  .img-box {
    position: relative;
    width: 100px;
    height: 100px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;

    .closeIcon {
      position: absolute;
      top: 0;
      right: 0;
      cursor: pointer;
    }
  }
  .feedback-buttons {
    display: flex;
    gap: 10px;
    margin-top: 8px;
    margin-left: 10px;

    .feedback-icon {
      cursor: pointer;
      font-size: 16px;
      color: #797979;
      transition: color 0.2s;

      // &:hover {
      //   color: #1890ff;
      // }

      // &.like-icon:hover {
      //   color: #52c41a;
      // }

      // &.dislike-icon:hover {
      //   color: #ff4d4f;
      // }
    }
  }
</style>
