<script setup lang="ts">
  import { LeftOutlined, PlayCircleOutlined } from '@ant-design/icons-vue';
  import { useRouter } from 'vue-router';
  import QuestionTip from './components/questionTip.vue';
  import { useAnswer } from '@/stores/anster';
  import { ref, onMounted, onUnmounted, computed, nextTick } from 'vue';
  import BeforeAnswer from './components/before.vue';
  import MainTest from './components/mainTest.vue';
  import { generateStaticVideo, getAnswer, getProject } from '@/api/answer';
  import GiveUp from './components/giveUp.vue';
  import Complete from './components/complete.vue';
  import { initStaticMessages } from './constant';
  import { message } from 'ant-design-vue';
  import { videoManager } from './tool';

  const { state, setAllQuestionList, resetAnswerStore } = useAnswer();
  const status = ref(1);
  const loading = ref(false);
  const animating = ref(false);

  const router = useRouter();
  const answerData = ref<Array<any>>([]);
  const staticMessageData = ref<any>([]);
  const dynamic_video = ref({
    id_ref: 'question_video',
    video_url: '',
  });
  const projectData = ref<Array<any>>([]);
  const contentRef = ref();
  const isStart = ref(false);

  function mockSaveData() {
    answerData.value = [
      {
        id: 109,
        created_at: '2024-10-12T11:52:46.586045+08:00',
        updated_at: '2024-10-12T11:53:04.986908+08:00',
        deleted_at: null,
        is_activated: true,
        image_info_id: 144,
        question: '202X-Company的使命是什么1？',
        answer: {
          A: '通过不断地学习和进步，为客户提供最优质的服务',
          B: '致力于成为行业领导者',
          C: '专注于产品开发',
          D: '追求技术创新',
        },
        true_answer: 'A',
        video_url: 'https://minio-prod.shukeyun.com/avatar/avatar/h5/virtual_classroom/KbLwjQeE6g_0/final.mp4',
        speaker: 'xingxing',
        model_name: 'avatar01_2d',
        is_last: false,
      },
      {
        id: 119,
        created_at: '2024-10-12T11:52:46.586045+08:00',
        updated_at: '2024-10-12T11:53:04.986908+08:00',
        deleted_at: null,
        is_activated: true,
        image_info_id: 144,
        question: '202X-Company的使命是什么2？',
        answer: {
          A: '通过不断地学习和进步，为客户提供最优质的服务',
          B: '致力于成为行业领导者',
          C: '专注于产品开发',
          D: '追求技术创新',
        },
        true_answer: 'A',
        video_url: 'https://minio-prod.shukeyun.com/avatar/avatar/h5/virtual_classroom/KbLwjQeE6g_0/final.mp4',
        speaker: 'xingxing',
        model_name: 'avatar01_2d',
        is_last: false,
      },
      {
        id: 129,
        created_at: '2024-10-12T11:52:46.586045+08:00',
        updated_at: '2024-10-12T11:53:04.986908+08:00',
        deleted_at: null,
        is_activated: true,
        image_info_id: 144,
        question: '202X-Company的使命是什么3？',
        answer: {
          A: '通过不断地学习和进步，为客户提供最优质的服务',
          B: '致力于成为行业领导者',
          C: '专注于产品开发',
          D: '追求技术创新',
        },
        true_answer: 'A',
        video_url: 'https://minio-prod.shukeyun.com/avatar/avatar/h5/virtual_classroom/KbLwjQeE6g_0/final.mp4',
        speaker: 'xingxing',
        model_name: 'avatar01_2d',
        is_last: false,
      },
    ];
    setAllQuestionList(answerData.value);
  }

  function fetchAnswerData() {
    getAnswer({ proj_id: router.currentRoute.value.query.modelId, all: '' }).then((res: any) => {
      if (res.list) {
        answerData.value = res.list;
        setAllQuestionList(res.list);
      }
      // if (location.href.includes('localhost')) {
      //   mockSaveData();
      // }
    });
  }

  function handleGiveUp() {
    manageStaticVideo('result_block');
    status.value = 3;
  }

  const tipData = computed(() => {
    const list = state.questionList;
    if (list.length === answerData.value.length) return list;
    return list.concat(answerData.value[list.length + 1]);
  });

  const openAnimate = (id: string) => {
    const dom = document.getElementById(id) as HTMLVideoElement;
    if (dom) {
      dom.currentTime = 0;
    }
    animating.value = true;
    setTimeout(() => {
      animating.value = false;
    }, 500);
  };

  const stopHidden = (id: string) => {
    const pre_ref = document.getElementById(id) as HTMLVideoElement;
    videoManager.stop(pre_ref);
    videoManager.hidden(pre_ref, false);
  };

  function manageStaticVideo(next_id: string) {
    staticMessageData.value.forEach((item: any) => {
      stopHidden(item.id_ref);
    });

    stopHidden(dynamic_video.value.id_ref);

    const next_ref = document.getElementById(next_id) as HTMLVideoElement;
    videoManager.show(next_ref);
    videoManager.init(next_ref);
    videoManager.play(next_ref);
  }

  function manageDynamicVideo(next_url: string) {
    staticMessageData.value.forEach((item: any) => {
      stopHidden(item.id_ref);
    });
    stopHidden(dynamic_video.value.id_ref);

    dynamic_video.value.video_url = next_url;
    nextTick(() => {
      const next_ref = document.getElementById(dynamic_video.value.id_ref) as HTMLVideoElement;
      videoManager.show(next_ref);
      videoManager.init(next_ref);
      videoManager.play(next_ref);
    });
  }

  function finishQuestion() {
    if (state.questionList.some((it) => !it.isRight)) {
      manageStaticVideo('result_wrong');
    } else {
      manageStaticVideo('result_right');
    }
  }

  function handleComplete() {
    finishQuestion();
    status.value = 4;
  }

  function nextQuestion() {
    manageDynamicVideo(answerData.value[state.currentIndex]?.video_url);
  }

  function handleBegin() {
    nextQuestion();
    status.value = 2;
  }

  function handleScroll(id: string) {
    console.log(id);
    nextTick(() => {
      const element = document.getElementById(id);
      // console.log(1111111, contentRef.value);
      nextTick(() => {
        contentRef.value?.scrollTo(0, 1000, {
          behavior: 'smooth',
        });
      });
    });
  }

  function handleHelloPlay() {
    const next_ref = document.getElementById('hello_source') as HTMLVideoElement;
    if (next_ref.style.display !== 'block') return;
    videoManager.play(next_ref);
    isStart.value = true;
  }

  onUnmounted(() => {
    resetAnswerStore();
  });

  onMounted(() => {
    // resetAnswerStore();
    fetchAnswerData();

    // 先获取项目信息，确定数字人设置
    getProject({ id: router.currentRoute.value.query.modelId }).then((res: any) => {
      console.log(res);
      const list = res.images.map((item: any) => ({
        id: item.id,
        image_url: item.image_url,
        video_url: item.videos.video_url,
      }));
      projectData.value = list;

      // 从第一个分镜获取数字人设置，确保整个答疑流程使用相同的数字人
      const firstImage = res.images[0];
      const speaker = firstImage?.speaker || 'xingxing';
      const modelName = firstImage?.type === '3d' ? 'avatar01_3d' : 'avatar01_2d';

      const params = initStaticMessages.map((item) => ({
        text: item.message,
        speaker: speaker,
        model_name: modelName,
        id_ref: item.id_ref,
      }));

      loading.value = true;
      Promise.all(params.map((param: any) => generateStaticVideo(param)))
        .then((data: any) => {
          const statics: any = [];
          for (const item of params) {
            const asyncItem = data.find((it: any) => it.text === item.text);
            statics.push({ ...asyncItem, id_ref: item.id_ref });
          }
          staticMessageData.value = statics;

          nextTick(() => {
            const next_ref = document.getElementById('hello_source') as HTMLVideoElement;
            console.log(next_ref);
            videoManager.show(next_ref);
          });
        })
        .catch((err: any) => {
          message.error('生成视频失败');
        })
        .finally(() => {
          loading.value = false;
        });
    });
  });
</script>

<template>
  <div class="answer-page">
    <div class="top">
      <span style="cursor: pointer" @click="router.back()">
        <LeftOutlined />
        答疑
      </span>
    </div>
    <div class="wrap">
      <div class="content flex relative">
        <div
          v-if="!isStart"
          class="absolute h-full w-full top-0 left-0"
          style="
            background: rgba(0, 0, 0, 0.5);
            border-radius: 8px;
            color: #fff;
            font-size: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            z-index: 99;
          "
        >
          <PlayCircleOutlined @click="handleHelloPlay" />
        </div>
        <div ref="contentRef" class="flex-1 h-full overflow-y-auto">
          <BeforeAnswer v-if="status === 1" :loading="loading" @begin="handleBegin" />
          <MainTest
            v-if="status === 2"
            :list="answerData"
            :tip-length="tipData.length"
            :project-data="projectData"
            @next="nextQuestion"
            @answer-wrong="() => manageStaticVideo('answer_wrong')"
            @answer-right="() => manageStaticVideo('answer_right')"
            @complete="handleComplete"
            @scroll="handleScroll"
          />
          <GiveUp v-if="status === 3" @change-step="() => (status = 2)" />
          <Complete v-if="status === 4" @re-answer="handleBegin" />
        </div>
        <div class="w-24%">
          <video
            v-for="item in staticMessageData"
            :id="item.id_ref"
            :key="item.id_ref"
            style="display: none; width: 100%; height: 100%"
            :src="item.video_url"
            @ended="openAnimate(item.id_ref)"
          />
          <video
            :id="dynamic_video.id_ref"
            style="display: none; width: 100%; height: 100%"
            :src="dynamic_video.video_url"
            @ended="openAnimate(dynamic_video.id_ref)"
          />
        </div>
      </div>
      <div v-if="status === 2" class="progress" style="z-index: 0">
        <div class="h-full flex items-center">
          <QuestionTip :list="tipData" class="flex-1 overflow-x-auto" :can-selected="true" />
          <a-button danger style="height: 40px" @click="handleGiveUp">放弃答题</a-button>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
  .answer-page {
    background-color: #fff;
    height: 100%;
    .top {
      height: 60px;
      line-height: 60px;
      padding-left: 25px;
      color: #18191a;
      font-size: 16px;
      font-weight: 600;
    }
    .wrap {
      background-color: #f0f1f2;
      height: calc(100% - 60px);
      padding: 33px 113px 94px;
      width: 100%;
      position: relative;
      .content {
        background-color: #fff;
        height: 100%;
        border-radius: 8px;
      }
      .progress {
        position: absolute;
        width: 100%;
        height: 64px;
        bottom: 0;
        left: 0;
        background-color: #fff;
        padding: 0 32px;
      }
    }
  }
</style>
